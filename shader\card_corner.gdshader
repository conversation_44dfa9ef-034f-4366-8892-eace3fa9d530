shader_type canvas_item;

// 圆角半径 (0.0-0.5)，值为0.5时为圆形
uniform float corner_radius : hint_range(0.0, 0.5) = 0.15;
// 边缘平滑程度
uniform float edge_smoothness : hint_range(0.0, 0.1) = 0.01;

void fragment() {
    // 获取UV坐标相对于中心的位置 (0.5, 0.5)
    vec2 center_distance = abs(UV - vec2(0.5));

    // 计算到最近卡片角落的距离
    float corner_distance = length(max(center_distance - vec2(0.5 - corner_radius), vec2(0.0)));

    // 创建圆角蒙版 - 使用smoothstep创建平滑边缘
    float corner_mask = 1.0 - smoothstep(corner_radius - edge_smoothness, corner_radius + edge_smoothness, corner_distance);

    // 获取原始像素颜色
    vec4 color = texture(TEXTURE, UV);

    // 应用圆角蒙版到alpha通道
    color.a *= corner_mask;

    // 输出最终颜色
    COLOR = color;
}
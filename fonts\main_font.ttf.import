[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://bd4v4g8hcfqpk"
path="res://.godot/imported/main_font.ttf-38cb57a131e3638265e7c0a620635321.fontdata"

[deps]

source_file="res://fonts/main_font.ttf"
dest_files=["res://.godot/imported/main_font.ttf-38cb57a131e3638265e7c0a620635321.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}

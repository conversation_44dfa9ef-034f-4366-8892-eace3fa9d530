{"en": [{"text": "Welcome to 'Up Cards'!\nLet me give you a quick introduction to the game~", "highlight_node": "", "action_required": false}, {"text": "At the start of each level, you can get [img=32]res://assert/top/ap.png[/img] by [color=green]rolling dice[/color]. Now click the [img=32]res://assert/dice_panel/roll_button.png[/img] button to [color=green]roll the dice[/color].", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/RollButton"}, {"text": "You have 3 initial [color=green]dice rolls[/color]. Found the numbers you like? Click the [img=32]res://assert/dice_panel/confirm_button.png[/img] button to officially begin!", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/ConfirmButton"}, {"text": "The top 3 cards are [color=yellow]public cards[/color], and the bottom 3 are your [color=yellow]hand cards[/color].\nThe first [color=red]3 cards[/color] in your hand will be scored.", "highlight_node": "PlayerControl", "action_required": false}, {"text": "Scoring rules are as follows:\n[img=32]res://assert/top/score.png[/img] = [color=yellow]Card Face Value[/color] + [color=yellow]Card Combination Value[/color]", "highlight_node": "PlayerControl", "action_required": false}, {"text": "[color=yellow]Card Face Value[/color] is the value on each card.\n[color=green]Right-click[/color] any card to view its value and detailed information.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "card_detail"}, {"text": "Click anywhere to return to the game", "highlight_node": "CardDetailPanel/Panel", "action_required": true, "required_signal": "back_to_game"}, {"text": "[color=yellow]Card Combination Value[/color] is the combination value of the first 3 cards.\nClick the [img=32]res://assert/items/Scroll.png[/img] button to view combination value details.", "highlight_node": "BottomRight/Scroll", "action_required": true, "required_signal": "BottomRight/Scroll/ScrollButton"}, {"text": "Click anywhere to return to the game", "highlight_node": "CardRulesPanel", "action_required": true, "required_signal": "rule_back_to_game"}, {"text": "[color=green]Drag[/color] any [color=yellow]hand card[/color] to swap positions with another [color=yellow]hand card[/color]. This allows you to adjust card order.", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[color=green]Drag[/color] any [color=yellow]hand card[/color] to swap with any [color=yellow]public card[/color]. This allows you to change scoring cards.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "mouseDrag"}, {"text": "Click the [img=32]res://assert/right/draw_cards.png[/img] button to spend [color=red]1[/color] [img=32]res://assert/top/ap.png[/img] and draw [color=red]1[/color] card to your [color=yellow]hand[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "card_drawn"}, {"text": "Click the [img=32]res://assert/items/feather02.png[/img] button to switch to [color=yellow]Burn[/color] mode", "highlight_node": "RightSide/ChangeChip", "action_required": true, "required_signal": "magic_changed"}, {"text": "In [color=yellow]Burn[/color] mode, clicking the [img=32]res://assert/right/draw_cards.png[/img] button will spend [color=red]5[/color] [img=32]res://assert/top/ap.png[/img] to replace all [color=red]3[/color] [color=yellow]public cards[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "magic_deck_clicked"}, {"text": "Click [img=32]res://assert/right/close.png[/img]", "highlight_node": "RightSide/DiscardArea", "action_required": true, "required_signal": "discard_area_clicked"}, {"text": "Then click any [color=green]hand card[/color] to discard it, [color=green]right-click[/color] to end [color=yellow]discarding[/color]", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "discard_cancelled"}, {"text": "Here's some game information displayed.\nAfter [color=green]scoring[/color], [img=32]res://assert/top/score.png[/img] will be reduced by [img=32]res://assert/top/goal.png[/img], if the remaining score is greater than [color=red]0[/color], you can advance one [color=yellow]level[/color]!", "highlight_node": "TopBar", "action_required": false}, {"text": "That's all for the basic operations, now enjoy the game!\nWhen you're ready to settle the current round, click the [img=32]res://assert/left/button06.png[/img] button.\nNote that card [color=yellow]BUFFS[/color] only trigger when the cards are scored!", "highlight_node": "BottomRight/NextButton", "action_required": false}], "ja": [{"text": "「アップカード」へようこそ！\n簡単なゲーム紹介をさせていただきます～", "highlight_node": "", "action_required": false}, {"text": "各レベルの開始時に[color=green]サイコロを振って[/color][img=32]res://assert/top/ap.png[/img]を獲得できます。[img=32]res://assert/dice_panel/roll_button.png[/img]ボタンをクリックして[color=green]サイコロを振って[/color]ください。", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/RollButton"}, {"text": "最初に[color=green]サイコロを振る[/color]チャンスが3回あります。好みの目が出ましたか？[img=32]res://assert/dice_panel/confirm_button.png[/img]ボタンをクリックして、ゲームを始めましょう！", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/ConfirmButton"}, {"text": "上の3枚は[color=yellow]公開カード[/color]、下の3枚は[color=yellow]手札[/color]です。\n手札の最初の[color=red]3枚[/color]が得点計算の対象となります。", "highlight_node": "PlayerControl", "action_required": false}, {"text": "得点計算のルールは以下の通りです：\n[img=32]res://assert/top/score.png[/img] = [color=yellow]カードの数値[/color] + [color=yellow]カードの組み合わせ値[/color]", "highlight_node": "PlayerControl", "action_required": false}, {"text": "[color=yellow]カードの数値[/color]は各カードの値です。\n[color=green]右クリック[/color]で任意のカードの値と詳細情報を確認できます。", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "card_detail"}, {"text": "どこかをクリックしてゲームに戻る", "highlight_node": "CardDetailPanel/Panel", "action_required": true, "required_signal": "back_to_game"}, {"text": "[color=yellow]カードの組み合わせ値[/color]は最初の3枚の組み合わせによる値です。\n[img=32]res://assert/items/Scroll.png[/img]ボタンをクリックして、組み合わせ値の詳細を確認できます。", "highlight_node": "BottomRight/Scroll", "action_required": true, "required_signal": "BottomRight/Scroll/ScrollButton"}, {"text": "どこかをクリックしてゲームに戻る", "highlight_node": "CardRulesPanel", "action_required": true, "required_signal": "rule_back_to_game"}, {"text": "[color=yellow]手札[/color]同士を[color=green]ドラッグ[/color]して位置を交換できます。これでカードの順序を調整できます。", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[color=yellow]手札[/color]を[color=yellow]公開カード[/color]と[color=green]ドラッグ[/color]して交換できます。これで得点計算のカードを変更できます。", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[img=32]res://assert/right/draw_cards.png[/img]ボタンをクリックすると、[img=32]res://assert/top/ap.png[/img]を[color=red]1[/color]消費して[color=yellow]手札[/color]に[color=red]1[/color]枚カードを引きます。", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "card_drawn"}, {"text": "[img=32]res://assert/items/feather02.png[/img]ボタンをクリックして[color=yellow]バーン[/color]モードに切り替えます。", "highlight_node": "RightSide/ChangeChip", "action_required": true, "required_signal": "magic_changed"}, {"text": "[color=yellow]バーン[/color]モードでは、[img=32]res://assert/right/draw_cards.png[/img]ボタンをクリックすると[img=32]res://assert/top/ap.png[/img]を[color=red]5[/color]消費して[color=yellow]公開カード[/color][color=red]3[/color]枚すべてを入れ替えます。", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "magic_deck_clicked"}, {"text": "[img=32]res://assert/right/close.png[/img]をクリック", "highlight_node": "RightSide/DiscardArea", "action_required": true, "required_signal": "discard_area_clicked"}, {"text": "任意の[color=green]手札[/color]をクリックして捨て、[color=green]右クリック[/color]で[color=yellow]捨てる[/color]を終了します。", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "discard_cancelled"}, {"text": "ここにゲーム情報が表示されています。\n[color=green]得点計算[/color]後、[img=32]res://assert/top/score.png[/img]から[img=32]res://assert/top/goal.png[/img]が差し引かれ、残りの得点が[color=red]0[/color]より大きければ、[color=yellow]段階[/color]を1つ上がることができます！", "highlight_node": "TopBar", "action_required": false}, {"text": "基本操作の説明は以上です。それではゲームをお楽しみください！\n現在のターンの決算の準備ができましたら、[img=32]res://assert/left/button06.png[/img]ボタンをクリックしてください。\nカードの[color=yellow]バフ[/color]効果は得点計算時にのみ発動します！", "highlight_node": "BottomRight/NextButton", "action_required": false}], "ko": [{"text": "'업 카드'에 오신 것을 환영합니다!\n게임에 대해 간단히 설명해 드리겠습니다~", "highlight_node": "", "action_required": false}, {"text": "각 레벨 시작 시 [color=green]주사위를 굴려[/color] [img=32]res://assert/top/ap.png[/img]를 얻을 수 있습니다. [img=32]res://assert/dice_panel/roll_button.png[/img] 버튼을 클릭하여 [color=green]주사위를 굴려[/color]보세요.", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/RollButton"}, {"text": "처음에 [color=green]주사위를 굴릴[/color] 기회가 3번 있습니다. 원하는 숫자가 나왔나요? [img=32]res://assert/dice_panel/confirm_button.png[/img] 버튼을 클릭하여 게임을 시작하세요!", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/ConfirmButton"}, {"text": "위쪽 3장은 [color=yellow]공개 카드[/color], 아래쪽 3장은 [color=yellow]패[/color]입니다.\n패의 첫 [color=red]3장[/color]이 점수 계산에 사용됩니다.", "highlight_node": "PlayerControl", "action_required": false}, {"text": "점수 계산 규칙은 다음과 같습니다:\n[img=32]res://assert/top/score.png[/img] = [color=yellow]카드 수치[/color] + [color=yellow]카드 조합 값[/color]", "highlight_node": "PlayerControl", "action_required": false}, {"text": "[color=yellow]카드 수치[/color]는 각 카드의 값입니다.\n[color=green]우클릭[/color]으로 카드의 값과 상세 정보를 확인할 수 있습니다.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "card_detail"}, {"text": "아무 곳이나 클릭하여 게임으로 돌아가기", "highlight_node": "CardDetailPanel/Panel", "action_required": true, "required_signal": "back_to_game"}, {"text": "[color=yellow]카드 조합 값[/color]은 첫 3장의 조합 값입니다.\n[img=32]res://assert/items/Scroll.png[/img] 버튼을 클릭하여 조합 값 상세 정보를 확인하세요.", "highlight_node": "BottomRight/Scroll", "action_required": true, "required_signal": "BottomRight/Scroll/ScrollButton"}, {"text": "아무 곳이나 클릭하여 게임으로 돌아가기", "highlight_node": "CardRulesPanel", "action_required": true, "required_signal": "rule_back_to_game"}, {"text": "[color=yellow]패[/color]끼리 [color=green]드래그[/color]하여 위치를 교환할 수 있습니다. 이를 통해 카드 순서를 조정할 수 있습니다.", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[color=yellow]패[/color]를 [color=yellow]공개 카드[/color]와 [color=green]드래그[/color]하여 교환할 수 있습니다. 이를 통해 점수 계산 카드를 변경할 수 있습니다.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[img=32]res://assert/right/draw_cards.png[/img] 버튼을 클릭하면 [img=32]res://assert/top/ap.png[/img] [color=red]1[/color]개를 소비하여 [color=yellow]패[/color]에 카드 [color=red]1[/color]장을 뽑습니다.", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "card_drawn"}, {"text": "[img=32]res://assert/items/feather02.png[/img] 버튼을 클릭하여 [color=yellow]번[/color] 모드로 전환합니다.", "highlight_node": "RightSide/ChangeChip", "action_required": true, "required_signal": "magic_changed"}, {"text": "[color=yellow]번[/color] 모드에서 [img=32]res://assert/right/draw_cards.png[/img] 버튼을 클릭하면 [img=32]res://assert/top/ap.png[/img] [color=red]5[/color]개를 소비하여 [color=yellow]공개 카드[/color] [color=red]3[/color]장을 모두 교체합니다.", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "magic_deck_clicked"}, {"text": "[img=32]res://assert/right/close.png[/img] 클릭", "highlight_node": "RightSide/DiscardArea", "action_required": true, "required_signal": "discard_area_clicked"}, {"text": "원하는 [color=green]패[/color]를 클릭하여 버리고, [color=green]우클릭[/color]으로 [color=yellow]버리기[/color]를 종료합니다.", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "discard_cancelled"}, {"text": "여기에 게임 정보가 표시됩니다.\n[color=green]점수 계산[/color] 후, [img=32]res://assert/top/score.png[/img]에서 [img=32]res://assert/top/goal.png[/img]가 차감되며, 남은 점수가 [color=red]0[/color]보다 크면 한 [color=yellow]단계[/color] 올라갈 수 있습니다!", "highlight_node": "TopBar", "action_required": false}, {"text": "기본 조작 설명은 여기까지입니다. 이제 게임을 즐기세요!\n현재 턴을 정산할 준비가 되면 [img=32]res://assert/left/button06.png[/img] 버튼을 클릭하세요.\n카드 [color=yellow]버프[/color]는 점수 계산 시에만 발동됩니다!", "highlight_node": "BottomRight/NextButton", "action_required": false}], "zh_CN": [{"text": "欢迎来到《上上牌》！\n让我来做一个简单的游戏介绍吧~", "highlight_node": "", "action_required": false}, {"text": "每关卡开始时可以通过[color=green]掷骰子[/color]来获取[img=32]res://assert/top/ap.png[/img]，现在点击[img=32]res://assert/dice_panel/roll_button.png[/img]按钮来[color=green]掷骰子[/color]。", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/RollButton"}, {"text": "初始有3次[color=green]掷骰子[/color]机会，有你喜欢的点数了吗？点击[img=32]res://assert/dice_panel/confirm_button.png[/img]按钮，我们正式开始！", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/ConfirmButton"}, {"text": "上方3张为[color=yellow]公共牌[/color]，下方3张为[color=yellow]手牌[/color]。\n手牌前[color=red]3张[/color]用于结算。", "highlight_node": "PlayerControl", "action_required": false}, {"text": "结算规则如下：\n[img=32]res://assert/top/score.png[/img] = [color=yellow]卡牌牌面值[/color] + [color=yellow]卡牌组合值[/color]", "highlight_node": "PlayerControl", "action_required": false}, {"text": "[color=yellow]卡牌牌面值[/color] 为各个牌面的数值。\n[color=green]右击[/color]任意卡牌，查看卡牌分值以及卡牌的详细信息。", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "card_detail"}, {"text": "点击任意详细信息回到游戏", "highlight_node": "CardDetailPanel/Panel", "action_required": true, "required_signal": "back_to_game"}, {"text": "[color=yellow]卡牌组合值[/color] 为前3张卡牌的组合值。\n点击[img=32]res://assert/items/Scroll.png[/img]按钮，查看各组合值详情。", "highlight_node": "BottomRight/Scroll", "action_required": true, "required_signal": "BottomRight/Scroll/ScrollButton"}, {"text": "点击任意组合值回到游戏", "highlight_node": "CardRulesPanel", "action_required": true, "required_signal": "rule_back_to_game"}, {"text": "[color=green]拖动[/color]任意[color=yellow]手牌[/color]与任意[color=yellow]手牌[/color]交换位置。可调整卡牌顺序。", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[color=green]拖动[/color]任意[color=yellow]手牌[/color]与任意[color=yellow]公共牌[/color]交换位置。可更换结算牌。", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "mouseDrag"}, {"text": "点击[img=32]res://assert/right/draw_cards.png[/img]按钮，将消耗[color=red]1[/color]个[img=32]res://assert/top/ap.png[/img]，抽[color=red]1[/color]张牌到[color=yellow]手牌[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "card_drawn"}, {"text": "点击[img=32]res://assert/items/feather02.png[/img]按钮，可以切换为[color=yellow]燃烧[/color]状态", "highlight_node": "RightSide/ChangeChip", "action_required": true, "required_signal": "magic_changed"}, {"text": "[color=yellow]燃烧[/color]状态下点击[img=32]res://assert/right/draw_cards.png[/img]按钮，将消耗[color=red]5[/color]个[img=32]res://assert/top/ap.png[/img]，替换[color=red]3[/color]张[color=yellow]公共牌[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "magic_deck_clicked"}, {"text": "点击[img=32]res://assert/right/close.png[/img]", "highlight_node": "RightSide/DiscardArea", "action_required": true, "required_signal": "discard_area_clicked"}, {"text": "再点击任意[color=green]手牌[/color]则可将其丢弃，点击[color=green]右键[/color]结束[color=yellow]弃牌[/color]", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "discard_cancelled"}, {"text": "这里还有一些游戏信息展示。\n当[color=green]结算[/color]后，[img=32]res://assert/top/score.png[/img]将被扣减[img=32]res://assert/top/goal.png[/img]，若扣减后分值大于[color=red]0[/color]，可上升一[color=yellow]阶[/color]！", "highlight_node": "TopBar", "action_required": false}, {"text": "基本操作就介绍到这里，接下来就享受游戏吧！\n当你准备好结算当前阶时，点击[img=32]res://assert/left/button06.png[/img]按钮。\n被结算的卡牌[color=yellow]效果[/color]才会触发生效哦！", "highlight_node": "BottomRight/NextButton", "action_required": false}], "ru": [{"text": "Добро пожаловать в 'Up Cards'!\nПозвольте дать краткое введение в игру~", "highlight_node": "", "action_required": false}, {"text": "В начале каждого уровня вы можете получить [img=32]res://assert/top/ap.png[/img], [color=green]бросив кости[/color]. Нажмите кнопку [img=32]res://assert/dice_panel/roll_button.png[/img], чтобы [color=green]бросить кости[/color].", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/RollButton"}, {"text": "У вас есть 3 начальных [color=green]броска костей[/color]. Нашли нужные числа? Нажмите кнопку [img=32]res://assert/dice_panel/confirm_button.png[/img], чтобы начать!", "highlight_node": "DicePanel", "action_required": true, "required_signal": "DicePanel/BottomButtons/ConfirmButton"}, {"text": "Верхние 3 карты - [color=yellow]общие карты[/color], нижние 3 - ваши [color=yellow]карты в руке[/color].\nПервые [color=red]3 карты[/color] в руке будут засчитаны.", "highlight_node": "PlayerControl", "action_required": false}, {"text": "Правила подсчета:\n[img=32]res://assert/top/score.png[/img] = [color=yellow]Значение карты[/color] + [color=yellow]Бонус комбинации[/color]", "highlight_node": "PlayerControl", "action_required": false}, {"text": "[color=yellow]Значение карты[/color] - это значение на каждой карте.\n[color=green]Правый клик[/color] по любой карте для просмотра её значения и подробной информации.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "card_detail"}, {"text": "Кликните в любом месте, чтобы вернуться к игре", "highlight_node": "CardDetailPanel/Panel", "action_required": true, "required_signal": "back_to_game"}, {"text": "[color=yellow]Бонус комбинации[/color] - это значение комбинации первых 3 карт.\nНажмите кнопку [img=32]res://assert/items/Scroll.png[/img] для просмотра деталей комбинаций.", "highlight_node": "BottomRight/Scroll", "action_required": true, "required_signal": "BottomRight/Scroll/ScrollButton"}, {"text": "Кликните в любом месте, чтобы вернуться к игре", "highlight_node": "CardRulesPanel", "action_required": true, "required_signal": "rule_back_to_game"}, {"text": "[color=green]Перетащите[/color] любую [color=yellow]карту в руке[/color] для обмена позициями с другой [color=yellow]картой в руке[/color]. Это позволяет настроить порядок карт.", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "mouseDrag"}, {"text": "[color=green]Перетащите[/color] любую [color=yellow]карту в руке[/color] для обмена с любой [color=yellow]общей картой[/color]. Это позволяет изменить карты для подсчета.", "highlight_node": "PlayerControl", "action_required": true, "required_signal": "mouseDrag"}, {"text": "Нажмите кнопку [img=32]res://assert/right/draw_cards.png[/img], чтобы потратить [color=red]1[/color] [img=32]res://assert/top/ap.png[/img] и взять [color=red]1[/color] карту в [color=yellow]руку[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "card_drawn"}, {"text": "Нажмите кнопку [img=32]res://assert/items/feather02.png[/img], чтобы переключиться в режим [color=yellow]Сжигания[/color]", "highlight_node": "RightSide/ChangeChip", "action_required": true, "required_signal": "magic_changed"}, {"text": "В режиме [color=yellow]Сжигания[/color] нажатие кнопки [img=32]res://assert/right/draw_cards.png[/img] потратит [color=red]5[/color] [img=32]res://assert/top/ap.png[/img] для замены всех [color=red]3[/color] [color=yellow]общих карт[/color]", "highlight_node": "RightSide/DeckDiscardArea", "action_required": true, "required_signal": "magic_deck_clicked"}, {"text": "Нажмите [img=32]res://assert/right/close.png[/img]", "highlight_node": "RightSide/DiscardArea", "action_required": true, "required_signal": "discard_area_clicked"}, {"text": "Затем нажмите любую [color=green]карту в руке[/color], чтобы сбросить её, [color=green]правый клик[/color] для завершения [color=yellow]сброса[/color]", "highlight_node": "PlayerControl/PlayerArea", "action_required": true, "required_signal": "discard_cancelled"}, {"text": "Отлично! Здесь отображается информация об игре.\nПосле [color=green]расчета[/color] [img=32]res://assert/top/score.png[/img] будет уменьшен на [img=32]res://assert/top/goal.png[/img], и если результат больше [color=red]0[/color], вы поднимитесь на один [color=yellow]уровень[/color]!", "highlight_node": "TopBar", "action_required": false}, {"text": "Основные операции на этом завершены, теперь наслаждайтесь игрой!\nКогда будете готовы завершить текущий раунд, нажмите кнопку [img=32]res://assert/left/button06.png[/img].\nОбратите внимание, что [color=yellow]эффекты[/color] карт срабатывают только при подсчете очков!", "highlight_node": "BottomRight/NextButton", "action_required": false}]}
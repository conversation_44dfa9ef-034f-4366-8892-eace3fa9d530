extends Control

# 胜利结算面板 - 处理通关后的结算界面

# 信号
signal next_level_requested
signal main_menu_requested

# 预加载场景
const GourdItemScene = preload("res://scence/gourd_item.tscn")

# 配置
const MIN_GOURD_COUNT = 3  # 最小葫芦数量

# 奖励权重管理器
var reward_weight_manager = null

# 节点引用
@onready var level_label = $CenterContainer/Panel/VBoxContainer/LevelLabel
@onready var next_level_button = $CenterContainer/Panel/VBoxContainer/ButtonsContainer/NextLevelButton
@onready var next_level_button_mask = $CenterContainer/Panel/VBoxContainer/ButtonsContainer/NextLevelButton/Mask
@onready var main_menu_button = $CenterContainer/Panel/VBoxContainer/ButtonsContainer/MainMenuButton
@onready var gourd_container = $CenterContainer/Panel/VBoxContainer/GourdContainer

# 葫芦节点数组
var gourd_items = []
var current_level = 1
var selected_gourd_indices = []  # 存储选中的葫芦索引，按选择顺序排列
var selected_buff = null
var max_selections = 1  # 可选择的最大数量，后续可能增加
var zoo_buff_lvl_limit = 2 # 出现zoo_buff的关卡限制:2
var zoo_buff_probability = 0.8 # zoo_buff出现概率

# 奖励数据
var rewards_data = {}

# 获取本地化值的辅助函数
func _get_localized_value(value, locale: String = "") -> String:
    if locale == "":
        locale = TranslationServer.get_locale()

    if value is Dictionary:
        # 尝试获取当前语言的值
        if value.has(locale):
            return str(value[locale])
        # 回退到英语
        elif value.has("en"):
            return str(value["en"])
        # 回退到中文
        elif value.has("zh_CN"):
            return str(value["zh_CN"])
        # 如果都没有，返回第一个可用的值
        elif value.size() > 0:
            return str(value.values()[0])

    return str(value)

# 初始化
func _ready():
    # 初始隐藏面板
    visible = false

    # 创建奖励权重管理器
    reward_weight_manager = get_node("/root/RewardWeightManager")
    #add_child(reward_weight_manager)

    # 设置面板样式
    #_setup_panel_style()

    # 连接信号
    next_level_button.pressed.connect(_on_next_level_button_pressed)
    next_level_button.mouse_entered.connect(_on_next_level_button_mouse_entered)
    next_level_button.mouse_exited.connect(_on_next_level_button_mouse_exited)
    main_menu_button.pressed.connect(_on_main_menu_button_pressed)

    # 加载奖励配置
    _load_rewards_config()

    # 连接游戏管理器信号
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        game_manager.level_completed.connect(_on_level_completed)

    # 注意：语言变化时需要手动调用 _on_language_changed() 来重新加载奖励配置

# 加载奖励配置
func _load_rewards_config():
    var config_file = ConfigFile.new()
    var err = config_file.load("res://config/buff_rewards.cfg")
    
    if err == OK:
        print("加载奖励配置文件成功")
        # 读取配置中的所有奖励
        var sections = config_file.get_sections()
        for section in sections:
            var reward_id = section

            # 获取原始配置值
            var raw_name = config_file.get_value(section, "name", "未知奖励")
            var raw_description = config_file.get_value(section, "description", "没有描述")
            var reward_type = config_file.get_value(section, "type", "unknown")

            # 获取本地化的名称和描述
            var localized_name = _get_localized_value(raw_name)
            var localized_description = _get_localized_value(raw_description)
            var value = config_file.get_value(section, "value", 0)

            # 动态替换描述中的 %s 占位符
            var final_description
            if(reward_type == "demon_whisper"):
                final_description = localized_description % [str(value), str(value * 10)]
            else:
                final_description = localized_description % [str(value)]

            var reward_data = {
                "name": localized_name,
                "description": final_description,
                "icon": config_file.get_value(section, "icon", "res://assert/coins/coins.png"),
                "card_pic": config_file.get_value(section, "card_pic", "res://assert/victory_panel/card_coin.png"),
                "type": reward_type,
                "value": value,
                "raw_name": raw_name,  # 保存原始数据以便语言切换时重新加载
                "raw_description": localized_description,
                "section_name": section  # 保存section名称以便调试
            }
            # 使用section名称作为主键，同时创建type到section的映射
            rewards_data[reward_id] = reward_data
            # 如果type不同于section，也创建type的映射
            if reward_type != reward_id:
                rewards_data[reward_type] = reward_data
    else:
        print("无法加载奖励配置文件：", err)

# 语言变化时重新加载奖励配置
func _on_language_changed():
    # 重新加载奖励配置以获取新语言的文本
    _load_rewards_config()

# 计算葫芦缩放比例
func _calculate_gourd_scale(count: int) -> float:
    # 容器的实际大小
    var container_width = 450
    
    # 葫芦的原始大小（未缩放）
    var original_gourd_width = 150
    
    # 期望的葫芦之间的间距（原始大小的20%）
    var desired_spacing = original_gourd_width * 0.2
    
    # 计算总共需要的空间（所有葫芦的宽度 + 间距）
    var total_space_needed = (original_gourd_width * count) + (desired_spacing * (count - 1))
    
    # 计算需要的缩放比例
    var scale = container_width / total_space_needed
    
    # 限制最小和最大缩放比例
    scale = clamp(scale, 0.4, 0.8)
    
    return scale

# 创建葫芦项
func _create_gourd_items(count: int):
    # 清除现有的葫芦
    for item in gourd_items:
        item.queue_free()
    gourd_items.clear()
    
    # 确保至少有最小数量的葫芦
    count = max(count, MIN_GOURD_COUNT)
    
    # 计算葫芦缩放比例
    var gourd_scale = _calculate_gourd_scale(count)
    
    # 获取buff_manager节点
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        print("无法获取BuffManager节点")
        return
    
    # 使用权重系统选择奖励
    var selected_reward_types = []
    if reward_weight_manager:
        # 打印当前权重状态（调试用）
        reward_weight_manager.print_weight_status()

        # 根据权重选择奖励
        selected_reward_types = reward_weight_manager.select_rewards_by_weight(count)
        print("根据权重选择的奖励：", selected_reward_types)
    else:
        # 回退到原始随机选择方式
        var available_rewards = []
        for reward_key in rewards_data.keys():
            # 排除已经是zoo_buff的奖励
            if not reward_key.begins_with("zoo_buff_"):
                available_rewards.append(reward_key)
        available_rewards.shuffle()
        # 取前count个
        for i in range(min(count, available_rewards.size())):
            selected_reward_types.append(available_rewards[i])
    
    # 处理zoo_buff（如果需要的话）
    var level = GameManager.current_level
    if level > zoo_buff_lvl_limit and randf() < zoo_buff_probability:
        # 获取未启用的zoo_buff
        var available_zoo_buffs = []
        for buff in buff_manager.zoo_buff_pool:
            if not buff.is_enabled:
                available_zoo_buffs.append(buff)

        if not available_zoo_buffs.is_empty():
            available_zoo_buffs.shuffle()
            var zoo_buff_data = available_zoo_buffs[0]

            # 将zoo_buff转换为rewards_data格式
            var localized_name = ""
            if buff_manager:
                localized_name = buff_manager._get_localized_value(zoo_buff_data.name, TranslationServer.get_locale())
            else:
                localized_name = str(zoo_buff_data.name)

            var zoo_buff_reward = {
                "name": localized_name,
                "description": tr("UnlockCard") + localized_name,
                "icon": BuffManager.get_buff_texture_path(zoo_buff_data.type),
                "card_pic": "res://assert/victory_panel/card_zoo.png",
                "type": "zoo_buff",
                "value": zoo_buff_data.base_value_s,
                "original_data": zoo_buff_data,
                "is_zoo_buff": true
            }

            # 将zoo_buff添加到rewards_data
            var zoo_buff_key = "zoo_buff_" + zoo_buff_data.type
            rewards_data[zoo_buff_key] = zoo_buff_reward

            # 随机替换一个普通奖励
            if selected_reward_types.size() > 0:
                var replace_index = randi() % selected_reward_types.size()
                selected_reward_types[replace_index] = zoo_buff_key
                print("添加zoo_buff奖励：", zoo_buff_key)
    
    # 计算葫芦之间的间距
    var spacing = 170 * gourd_scale * 0.2  # 间距为葫芦宽度的20%
    var total_width = (170 * gourd_scale * count) + (spacing * (count - 1))
    var start_x = (450 - total_width) / 2  # 居中显示
    
    # 创建新的葫芦
    for i in range(count):
        var gourd_item = GourdItemScene.instantiate()
        gourd_container.add_child(gourd_item)
        gourd_items.append(gourd_item)
        
        # 设置位置和缩放
        gourd_item.position.x = start_x + (170 * gourd_scale + spacing) * i
        gourd_item.pivot_offset = gourd_item.size / 2
        gourd_item.scale = Vector2(gourd_scale, gourd_scale)
        
        # 设置BUFF数据
        if i < selected_reward_types.size():
            var reward_key = selected_reward_types[i]

            # 检查奖励数据是否存在
            if not reward_key in rewards_data:
                print("错误：找不到奖励数据，键：", reward_key)
                print("可用的奖励键：", rewards_data.keys())
                continue

            var reward_data = rewards_data[reward_key]
            # 如果是zoo_buff，添加金色和彩虹阴影效果
            if reward_data.get("is_zoo_buff", false):
                reward_data = reward_data.duplicate()  # 创建副本以避免修改原始数据
                # 确保名称是字符串格式
                var name_str = str(reward_data.name)
                reward_data.name = "[color=yellow]" + name_str + "[/color]"

                # 为GourdTexture节点添加彩虹阴影着色器
                var gourd_texture = gourd_item.get_node("FloatingContainer/GourdTexture")
                if gourd_texture:
                    # 加载彩虹阴影着色器
                    var rainbow_shader = load("res://shader/rainbow_shadow.gdshader")
                    var shader_material = ShaderMaterial.new()
                    shader_material.shader = rainbow_shader

                    # 设置着色器参数
                    shader_material.set_shader_parameter("strength", 0.5)
                    shader_material.set_shader_parameter("speed", 0.2)
                    shader_material.set_shader_parameter("angle", 45.0)

                    # 应用着色器材质
                    gourd_texture.material = shader_material
            gourd_item.setup(i, reward_data)
        
        # 连接选择信号
        gourd_item.gourd_selected.connect(_on_gourd_selected)
        gourd_item.gourd_deselected.connect(_on_gourd_deselected)
        
        # 开始漂浮动画（错开时间）
        #gourd_item.start_floating(randf_range(0, 0.5))

# 更新胜利信息
func update_victory_info(level: int):
    current_level = level
    level_label.text = tr("VictoryTitle") % [level + 1,max_selections]
    
    # 重置选择状态
    selected_gourd_indices.clear()
    selected_buff = null
    
    # 创建新的葫芦项
    _create_gourd_items(MIN_GOURD_COUNT)
    
    # 初始禁用下一关按钮
    next_level_button.disabled = true

# 显示胜利面板
func show_panel():
    # 获取并禁用结束阶按钮
    var end_round_button = get_node("%EndRoundButton")
    if end_round_button:
        end_round_button.disabled = true
    _update_next_button_state()
    
    # 设置为最顶层
    show()
    move_to_front()
    
    # 播放音效
    AudioManager.play_sfx("res://assert/audio/sfx/lvlup.wav")
    
    # 确保遮罩层接收所有输入
    $ColorRect.mouse_filter = Control.MOUSE_FILTER_STOP
    mouse_filter = Control.MOUSE_FILTER_STOP
    
    # 显示面板
    visible = true
    modulate.a = 0
    
    # 播放显示动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(1.0, 1.0), 0.3).from(Vector2(0.8, 0.8))

# 隐藏面板
func _hide_panel():
    $ColorRect.mouse_filter = Control.MOUSE_FILTER_IGNORE
    mouse_filter = Control.MOUSE_FILTER_IGNORE
    visible = false

# 葫芦选择处理
func _on_gourd_selected(index: int):
    # 如果已经选中，不做任何处理
    if index in selected_gourd_indices:
        return

    AudioManager.play_sfx("res://assert/audio/sfx/button_click001.mp3")
        
    # 如果已达到最大选择数量，取消最早选择的葫芦
    if selected_gourd_indices.size() >= max_selections:
        var earliest_index = selected_gourd_indices[0]
        gourd_items[earliest_index].deselect()
        gourd_items[earliest_index].restore()
        selected_gourd_indices.remove_at(0)
    
    # 添加新选择的葫芦
    selected_gourd_indices.append(index)
    #selected_buff = rewards_data.values()[index]
    selected_buff = gourd_items[index].buff_data
    
    # 处理选中和未选中的葫芦
    gourd_items[index].select()
    
    # 更新下一关按钮状态
    _update_next_button_state()

# 葫芦取消选择处理
func _on_gourd_deselected(index: int):
    # 从选中列表中移除
    var idx = selected_gourd_indices.find(index)
    if idx != -1:
        selected_gourd_indices.remove_at(idx)
        
    # 取消选中状态
    gourd_items[index].deselect()
    gourd_items[index].restore()
    
    # 如果没有选中的葫芦，清空selected_buff
    if selected_gourd_indices.is_empty():
        selected_buff = null
    else:
        # 使用最后选中的葫芦的buff
        selected_buff = rewards_data.values()[selected_gourd_indices[-1]]
    
    # 更新下一关按钮状态
    _update_next_button_state()

# 更新下一关按钮状态
func _update_next_button_state():
    next_level_button.disabled = selected_gourd_indices.size() < max_selections
    
    # 更新Mask节点的可见性
    if next_level_button_mask:
        next_level_button_mask.visible = next_level_button.disabled
    
    # 更新按钮提示文本
    if selected_gourd_indices.size() < max_selections:
        next_level_button.tooltip_text = "请选择 %d 个奖励" % max_selections
    else:
        next_level_button.tooltip_text = ""

# 获取当前选中的葫芦数量
func _get_selected_count() -> int:
    return selected_gourd_indices.size()

# 应用选中的BUFF
func _apply_selected_buff():
    if not selected_buff:
        return
        
    # 获取游戏管理器和BUFF管理器
    var game_manager = get_node("/root/GameManager")
    var buff_manager = get_node("/root/BuffManager")
    if not game_manager or not buff_manager:
        return

    # 记录奖励被选择（更新权重）
    if reward_weight_manager and selected_buff.type:
        reward_weight_manager.record_reward_selection(selected_buff.type)

    # 检查是否是zoo_buff
    if selected_buff.type == "zoo_buff" and "original_data" in selected_buff:
        # 启用zoo_buff并保存状态到 settings.cfg
        var zoo_buff_data = selected_buff.original_data
        for buff in buff_manager.zoo_buff_pool:
            if buff.type == zoo_buff_data.type:
                buff.is_enabled = true
                break
        buff_manager._save_zoo_buff_enabled_states()
        return
        
    # 根据BUFF类型应用效果
    match selected_buff.type:
        "dice_count":
            game_manager.set_dice_count(game_manager.dice_count + selected_buff.value)
            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": selected_buff.description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value
            })
        "hand_limit":
            game_manager.hand_limit += selected_buff.value
            game_manager.emit_signal("hand_limit_changed", game_manager.hand_limit)
            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": selected_buff.description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value
            })
        "start_score":
            game_manager.reward_score += selected_buff.value
            game_manager.emit_signal("score_changed", game_manager.total_score + game_manager.reward_score)
            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": selected_buff.description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value
            })
        "dice_chance":
            game_manager.dice_rolls_chance += selected_buff.value
            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": selected_buff.description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value
            })
        "double_gunner":
            game_manager.double_gunner_chance += selected_buff.value
            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": selected_buff.description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value
            })
        "demon_whisper":
            game_manager.add_demon_whisper_stack()
            # 更新描述以显示当前叠加效果
            var current_penalty = game_manager.demon_whisper_per_stage_penalty
            var current_bonus = game_manager.demon_whisper_bonus_per_5_stages

            # 获取本地化的描述模板
            var locale = TranslationServer.get_locale()
            var raw_description = selected_buff.raw_description
            var localized_description = ""

            if raw_description is Dictionary and locale in raw_description:
                localized_description = raw_description[locale]
            else:
                localized_description = str(raw_description)

            # 格式化描述
            var updated_description = localized_description % [str(current_penalty), str(current_bonus)]

            # 添加到永久buff区
            buff_manager.permanent_buff_added.emit({
                "icon": selected_buff.icon,
                "description": updated_description,
                "raw_description": selected_buff.raw_description,
                "card_pic": selected_buff.card_pic,
                "type": selected_buff.type,
                "value": selected_buff.value,
                "stacks": game_manager.demon_whisper_stacks  # 添加叠加次数信息
            })

# 关卡完成处理
func _on_level_completed(completed_level):
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 更新胜利信息
        update_victory_info(game_manager.current_level)
        # 显示面板
        show_panel()

# 下一关按钮点击处理
func _on_next_level_button_pressed():
    # 如果未选择足够的葫芦，不执行任何操作
    if _get_selected_count() < max_selections:
        return
    
    # 应用选中的BUFF
    _apply_selected_buff()
    
    # 播放隐藏动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 0.0, 0.2)
    tween.parallel().tween_property(self, "scale", Vector2(0.8, 0.8), 0.2)
    tween.tween_callback(func(): 
        _hide_panel()
        GameManager.level_up()
        emit_signal("next_level_requested")
    )

# 主菜单按钮点击处理
func _on_main_menu_button_pressed():
    # 播放隐藏动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 0.0, 0.2)
    tween.parallel().tween_property(self, "scale", Vector2(0.8, 0.8), 0.2)
    tween.tween_callback(func(): 
        _hide_panel()
        GameManager.return_to_main_menu()
        emit_signal("main_menu_requested")
    )

# 鼠标移入下一关按钮时播放音效和放大效果
func _on_next_level_button_mouse_entered():
    if not next_level_button.disabled:
        AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
        # 创建放大动画
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(next_level_button, "scale", Vector2(1.1, 1.1), 0.2)

# 鼠标移出下一关按钮时恢复大小
func _on_next_level_button_mouse_exited():
    if not next_level_button.disabled:
        # 创建缩小动画
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(next_level_button, "scale", Vector2(1.0, 1.0), 0.2)

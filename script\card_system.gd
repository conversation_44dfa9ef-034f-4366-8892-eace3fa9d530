extends Node

# 卡牌系统 - 负责卡牌的创建、管理和牌型判断

# 不再需要配置文件路径，直接从 BuffManager 获取规则值

# 卡牌花色枚举
enum CardSuit {
    SPADE,   # 黑桃
    HEART,   # 红桃
    CLUB,    # 梅花
    DIAMOND, # 方块
    JOKER    # 大小王
}

# 阶计数器 - 用于BUFF生成
var round_counter = 0

# BUFF相关
var buff_chance = 0.2  # 普通生成几率
var buff_chance_temp = 0.5  # 高生成几率
var active_buff_cards = []  # 已激活的BUFF卡牌

# 基础分数配置
var rules_values: Dictionary = {}

# 卡牌点数枚举
enum CardRank {
    ACE = 1,
    TWO = 2,
    THREE = 3,
    FOUR = 4,
    FIVE = 5,
    SIX = 6,
    SEVEN = 7,
    EIGHT = 8,
    NINE = 9,
    TEN = 10,
    JACK = 11,
    QUEEN = 12,
    KING = 13,
    JOKER_RED = 14,
    JOKER_BLACK = 15
}

# 牌型枚举
enum HandType {
    NONE,         # 无牌
    HIGH_CARD,    # 单张
    PAIR,         # 对子
    STRAIGHT,     # 顺子
    FLUSH,        # 金花
    STRAIGHT_FLUSH,# 顺金
    THREE_OF_KIND, # 豹子
    SPECIAL       # 特殊
}

# 卡牌资源路径
const CARD_BACK_PATH = "res://assert/cards/cardback.svg"
const CARD_PATH_FORMAT = "res://assert/cards/%s/%d.svg"

# 卡牌池
var deck: Array = []
var player_hand: Array = []
var common_pool: Array = []
var discard_pile: Array = []

# 信号
signal card_added_to_hand(card_data)
signal card_added_to_pool(card_data)
signal cards_swapped(hand_index, pool_index)
signal hand_evaluated(score, hand_type)
signal buff_card_created(card_data)
signal buff_activated(buff_data)

# 初始化
func _ready():
    # 确保BuffManager已经加载
    if not has_node("/root/BuffManager"):
        var buff_manager = load("res://script/buff_manager.gd").new()
        buff_manager.name = "BuffManager"
        get_node("/root").add_child(buff_manager)
    
    # 加载基础分数配置
    _load_base_scores()

# 加载基础分数配置
func _load_base_scores():
    # 从 BuffManager 获取规则值配置
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager and buff_manager.game_rules_values:
        # 将 BuffManager 的格式转换为 CardSystem 需要的格式
        rules_values = {"rules": []}
        for rule_id in buff_manager.game_rules_values.keys():
            var rule_data = buff_manager.game_rules_values[rule_id]
            rules_values.rules.append({
                "id": rule_id,
                "base_score": rule_data.base_score,
                "bonus_score": rule_data.bonus_score
            })
        # print("CardSystem 从 BuffManager 加载了 %d 个规则值" % rules_values.rules.size())
    else:
        print("CardSystem 无法从 BuffManager 获取规则值数据")
        rules_values = {"rules": []}

# 创建一副新牌
func create_new_deck():
    deck.clear()
    
    # 添加标准52张牌
    for suit in range(CardSuit.SPADE, CardSuit.JOKER):
        for rank in range(CardRank.ACE, CardRank.JOKER_RED):
            var card = {
                "suit": suit,
                "rank": rank,
                "value": get_card_value(rank),
                "path": get_card_path(suit, rank)
            }
            deck.append(card)
    
    # 添加大小王
    var joker_red = {
        "suit": CardSuit.JOKER,
        "rank": CardRank.JOKER_RED,
        "value": 20,
        "path": "res://assert/cards/joker/1.svg"
    }
    deck.append(joker_red)
    
    var joker_black = {
        "suit": CardSuit.JOKER,
        "rank": CardRank.JOKER_BLACK,
        "value": 20,
        "path": "res://assert/cards/joker/2.svg"
    }
    deck.append(joker_black)
    
    # 洗牌
    shuffle_deck()

# 洗牌
func shuffle_deck():
    deck.shuffle()

# 重置卡牌
func reset_cards():
    # 清空公共牌池
    common_pool.clear()
    
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    
    # 只有第一阶才创建新牌组
    if game_manager and game_manager.current_round == 1:
        # 第一阶时清空手牌
        player_hand.clear()
        discard_pile.clear()
        active_buff_cards.clear()
        create_new_deck()
        round_counter = 0
    else:
        # 后续阶只需要洗牌
        shuffle_deck()
    
    # 增加阶计数
    round_counter += 1

# 发牌到玩家手牌
func deal_to_player(count: int = 1):
    var tooltip_bubble = get_node("/root/GameScene/TooltipBubble")
    if player_hand.size() > GameManager.max_hand_limit:
        if tooltip_bubble:
            tooltip_bubble.show_tooltip("已达最大手牌数!")
        return false
    for i in range(count):
        if deck.size() > 0:
            var card = deck.pop_front()
            player_hand.append(card)
            emit_signal("card_added_to_hand", card)
        else:
            if tooltip_bubble:
                tooltip_bubble.show_tooltip("卡牌已用完!")
            return false
    return true

# 发牌到公共牌池
func deal_to_common_pool(count: int = 1):
    for i in range(count):
        if deck.size() > 0:
            var card = deck.pop_front()
            var buff_manager = get_node("/root/BuffManager")
            # 每阶都有几率生成BUFF卡牌
            if round_counter % 1 == 0:
                if randf() < buff_chance:
                    if buff_manager :
                        # 给卡牌添加BUFF效果
                        card = buff_manager.apply_buff_to_card(card)
                        emit_signal("buff_card_created", card)
            
            # 每5阶，必生成BUFF卡牌
            if round_counter % 5 == 0:
                if randf() < 1:
                    if buff_manager:
                        # 给卡牌添加BUFF效果
                        card = buff_manager.apply_buff_to_card(card)
                        emit_signal("buff_card_created", card)

            # 每2阶生成几率提高BUFF卡牌
            if round_counter % 2 == 0:
                if randf() < buff_chance_temp:
                    if buff_manager :
                        # 给卡牌添加BUFF效果
                        card = buff_manager.apply_buff_to_card(card)
                        emit_signal("buff_card_created", card)
            
            common_pool.append(card)
            emit_signal("card_added_to_pool", card)

# 交换手牌和公共牌池中的牌
func swap_cards(hand_index: int, pool_index: int) -> bool:
    # 检查索引是否有效
    if hand_index < 0 or hand_index >= player_hand.size():
        return false
    if pool_index < 0 or pool_index >= common_pool.size():
        return false
 
    # 交换卡牌
    var temp = player_hand[hand_index]
    player_hand[hand_index] = common_pool[pool_index]
    common_pool[pool_index] = temp
    
    # 发出信号
    cards_swapped.emit(hand_index, pool_index)
    
    return true

# 弃牌前三张手牌
func discard_hand_front_cards():
    # 将前三张手牌加入弃牌堆，但先移除BUFF效果
    var player_hand_front = player_hand.slice(0,3)
    var remaining_cards = player_hand.slice(3)
    
    for card in player_hand_front:
        var card_to_discard = card.duplicate()
        
        # 如果卡牌有BUFF效果，移除它
        if "buff" in card_to_discard:
            card_to_discard.erase("buff")
        
        deck.append(card_to_discard)
        # discard_pile.append(card_to_discard)
    
    # 清空前三张手牌，保留剩余手牌
    player_hand = remaining_cards
    
    # 将弃牌堆中的牌重新随机加入牌堆
    # for card in discard_pile:
    #     deck.append(card)
    
    # 清空弃牌堆
    # discard_pile.clear()
   
    # 洗牌以确保随机性
    shuffle_deck()

    return player_hand_front

# 弃牌公共牌池中的所有卡牌
func discard_common_pool_cards():
    # 将公共牌池中的所有卡牌加入弃牌堆，但先移除BUFF效果
    for card in common_pool:
        var card_to_discard = card.duplicate()
        
        # 如果卡牌有BUFF效果，移除它
        if "buff" in card_to_discard:
            card_to_discard.erase("buff")
        
        deck.append(card_to_discard)
        # discard_pile.append(card_to_discard)
    
    # 清空公共牌池
    var discarded_cards = common_pool.duplicate()
    common_pool.clear()
    
    # 将弃牌堆中的牌重新随机加入牌堆
    # 这些牌已经在上面的步骤中移除了BUFF效果
    # for card in discard_pile:
    #     deck.append(card)
    
    # 清空弃牌堆
    # discard_pile.clear()
    
    # 洗牌以确保随机性
    shuffle_deck()
    
    return discarded_cards

# 获取卡牌路径
func get_card_path(suit: int, rank: int) -> String:
    var suit_name = ""
    
    match suit:
        CardSuit.SPADE:
            suit_name = "spade"
        CardSuit.HEART:
            suit_name = "heart"
        CardSuit.CLUB:
            suit_name = "club"
        CardSuit.DIAMOND:
            suit_name = "diamond"
        CardSuit.JOKER:
            return "res://assert/cards/joker/%d.svg" % [1 if rank == CardRank.JOKER_RED else 2]
    
    return CARD_PATH_FORMAT % [suit_name, rank]

# 获取卡牌点数对应的分值
func get_card_value(rank: int) -> int:
    if rank >= CardRank.JACK and rank <= CardRank.KING:
        return 10  # JQK都是10分
    else:
        return rank  # 其他牌按点数计分，A为1分

# 计算前三张牌的最大积分
func evaluate_hand(is_add_buff: bool = false) -> Dictionary:
    # if player_hand.size() < 3:
    #     return {"score": 0, "type": HandType.HIGH_CARD}
    
    # 只取前三张牌进行评估
    var hand = player_hand.slice(0, 3)
    
    # 检查是否有BUFF卡牌在前三张中，如果有则激活BUFF
    check_and_activate_buffs(hand,is_add_buff)

    # 检查牌型并计算分数
    var result = check_hand_type(hand)
    
    # 发出信号
    emit_signal("hand_evaluated", result.score, result.type)
    
    return result

# 检查牌型并计算分数
func check_hand_type(hand: Array) -> Dictionary:
    # 提取牌的花色和点数
    var suits = []
    var ranks = []
    var face_value = 0
    
    for card in hand:
        # 跳过被标记为禁用的卡牌
        if "is_disabled" in card and card.is_disabled:
            continue
            
        suits.append(card.suit)
        ranks.append(card.rank)
        face_value += card.value
    
    # 如果所有卡牌都被禁用，返回零分
    if suits.size() == 0:
        return {"score": 0, "type": HandType.NONE, "face_value": 0, "base_score": 0}
    
    # 排序点数，方便检查顺子
    ranks.sort()
    
    # 检查是否为豹子（三张相同点数）
    var is_three_of_kind = ranks.size() == 3 and ranks[0] == ranks[1] and ranks[1] == ranks[2]
    
    # 检查是否为同花（三张同花色）
    var is_flush = suits.size() == 3 and suits[0] == suits[1] and suits[1] == suits[2]
    
    # 检查是否为顺子
    var is_straight = false
    if ranks.size() == 3:
        if ranks[2] - ranks[1] == 1 and ranks[1] - ranks[0] == 1:
            # 特殊情况：K-JOKER-JOKER
            if ranks[0] == CardRank.KING and ranks[1] == CardRank.JOKER_RED and ranks[2] == CardRank.JOKER_BLACK:
                is_straight = false
            else:
                is_straight = true
        # 特殊情况：A-K-Q
        elif ranks[0] == CardRank.ACE and ranks[1] == CardRank.QUEEN and ranks[2] == CardRank.KING:
            is_straight = true
    
    # 检查是否为对子(大小王也算对子)
    var is_pair = ranks.size() >= 2 and ((ranks[0] == ranks[1] or (ranks.size() == 3 and ranks[1] == ranks[2])) or (ranks.has(CardRank.JOKER_RED) and ranks.has(CardRank.JOKER_BLACK)))
    
    # 检查是否为特殊牌型（点数相同且总和为骰子点数）
    var is_special = false
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var dice_sum = 0
        for dice in game_manager.last_dice_result:
            dice_sum += dice
        
        if face_value == dice_sum:
            is_special = true
    
    # 创建所有可能的牌型及其分数
    var possible_hands = []
    
    if is_special:
        possible_hands.append({
            "type": HandType.SPECIAL,
            "score": _get_base_score("vip"),
            "rule_id": "vip",
            "type_name": get_localized_hand_type_name("vip")
        })

    if is_three_of_kind:
        possible_hands.append({
            "type": HandType.THREE_OF_KIND,
            "score": _get_base_score("three_of_kind"),
            "rule_id": "three_of_kind",
            "type_name": get_localized_hand_type_name("three_of_kind")
        })

    if is_straight and is_flush:
        possible_hands.append({
            "type": HandType.STRAIGHT_FLUSH,
            "score": _get_base_score("straight_flush"),
            "rule_id": "straight_flush",
            "type_name": get_localized_hand_type_name("straight_flush")
        })

    if is_flush:
        possible_hands.append({
            "type": HandType.FLUSH,
            "score": _get_base_score("flush"),
            "rule_id": "flush",
            "type_name": get_localized_hand_type_name("flush")
        })

    if is_straight:
        possible_hands.append({
            "type": HandType.STRAIGHT,
            "score": _get_base_score("straight"),
            "rule_id": "straight",
            "type_name": get_localized_hand_type_name("straight")
        })

    if is_pair:
        possible_hands.append({
            "type": HandType.PAIR,
            "score": _get_base_score("pair"),
            "rule_id": "pair",
            "type_name": get_localized_hand_type_name("pair")
        })

    if possible_hands.size() == 0:
        possible_hands.append({
            "type": HandType.HIGH_CARD,
            "score": _get_base_score("high_card"),
            "rule_id": "high_card",
            "type_name": get_localized_hand_type_name("high_card")
        })

    # 按分数从高到低排序
    possible_hands.sort_custom(func(a, b): return a.score > b.score)

    # 选择分数最高的牌型
    var hand_type = possible_hands[0].type
    var base_score = possible_hands[0].score
    var type_name = possible_hands[0].type_name

    # 计算总分数
    var total_score = base_score + face_value

    return {
        "score": total_score,
        "type": hand_type,
        "face_value": face_value,
        "base_score": base_score,
        "type_name": type_name,
        "possible_hands": possible_hands
    }

# 从规则配置中获取基础分数
func _get_base_score(rule_id: String) -> int:
    # 实时加载最新的基础分数配置
    _load_base_scores()
    
    if rules_values == null or not rules_values.has("rules") or rules_values.rules.is_empty():
        return 0
    
    # 使用规则ID查找对应的规则
    for rule in rules_values.rules:
        if rule.id == rule_id:
            return rule.base_score + rule.bonus_score
    
    return 0  # 如果没找到对应的规则，返回0分

# 获取牌型名称（中文版本，保留向后兼容）
func get_hand_type_name(hand_type: int) -> String:
    match hand_type:
        HandType.NONE:
            return "无牌"
        HandType.HIGH_CARD:
            return "单张"
        HandType.PAIR:
            return "对子"
        HandType.STRAIGHT:
            return "顺子"
        HandType.FLUSH:
            return "金花"
        HandType.STRAIGHT_FLUSH:
            return "顺金"
        HandType.THREE_OF_KIND:
            return "豹子"
        HandType.SPECIAL:
            return "VIP"
        _:
            return "未知"

# 获取国际化的牌型名称
func get_localized_hand_type_name_by_type(hand_type: int) -> String:
    match hand_type:
        HandType.NONE:
            return tr("HAND_TYPE_NONE")
        HandType.HIGH_CARD:
            return tr("HAND_TYPE_HIGH_CARD")
        HandType.PAIR:
            return tr("HAND_TYPE_PAIR")
        HandType.STRAIGHT:
            return tr("HAND_TYPE_STRAIGHT")
        HandType.FLUSH:
            return tr("HAND_TYPE_FLUSH")
        HandType.STRAIGHT_FLUSH:
            return tr("HAND_TYPE_STRAIGHT_FLUSH")
        HandType.THREE_OF_KIND:
            return tr("HAND_TYPE_THREE_OF_KIND")
        HandType.SPECIAL:
            return tr("HAND_TYPE_SPECIAL")
        _:
            return tr("HAND_TYPE_UNKNOWN")

# 从game_rules.json获取本地化牌型名称
func get_localized_hand_type_name(rule_id: String) -> String:
    var file = FileAccess.open("res://data/game_rules.json", FileAccess.READ)
    if not file:
        return ""

    var json_string = file.get_as_text()
    file.close()

    var json = JSON.new()
    var parse_result = json.parse(json_string)
    if parse_result != OK:
        return ""

    var data = json.data
    var current_locale = TranslationServer.get_locale()

    # 如果当前语言不存在，回退到英语
    if not data.has(current_locale):
        current_locale = "en"

    if not data.has(current_locale) or not data[current_locale].has("rules"):
        return ""

    # 查找对应的规则
    for rule in data[current_locale].rules:
        if rule.id == rule_id:
            return rule.name

    return ""

# 检查并激活BUFF
func check_and_activate_buffs(hand,is_add_buff):
    # 获取BuffManager
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        return
    
    # 清除之前激活的所有BUFF卡牌
    #active_buff_cards.clear()
    
    # 检查前三张牌中是否有BUFF卡牌
    for card in hand:
        if "buff" in card:
            # 确保这张卡牌确实在当前手牌的前三张位置
            var is_in_front_three = false
            for i in range(min(3, player_hand.size())):
                if player_hand[i] == card:
                    is_in_front_three = true
                    break
            
            # 只有当卡牌在前三张位置时才激活BUFF
            if is_in_front_three and not active_buff_cards.has(card):
                # 卡牌不能被禁用
                if "is_disabled" in card and card.is_disabled:
                    continue
                active_buff_cards.append(card)
                buff_manager.activate_buff(card.buff,is_add_buff)
                if is_add_buff:
                    emit_signal("buff_activated", card.buff)

# 获取激活的BUFF卡牌
func get_active_buff_cards() -> Array:
    return active_buff_cards

# 增加随机卡牌到牌堆
func add_random_card_to_deck(count: int = 1):
    for i in range(count):
        # 从所有卡牌中随机选择一张
        var random_card = deck[randi() % deck.size()]

        # 将这张随机卡牌添加到牌堆
        deck.append(random_card)

# 将HandType枚举值映射到规则ID
func get_hand_type_rule_id(hand_type: int) -> String:
    match hand_type:
        HandType.HIGH_CARD:
            return "high_card"
        HandType.PAIR:
            return "pair"
        HandType.STRAIGHT:
            return "straight"
        HandType.FLUSH:
            return "flush"
        HandType.STRAIGHT_FLUSH:
            return "straight_flush"
        HandType.THREE_OF_KIND:
            return "three_of_kind"
        HandType.SPECIAL:
            return "vip"
        _:
            return "high_card"

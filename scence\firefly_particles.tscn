[gd_scene load_steps=8 format=3 uid="uid://csgr12uwl10ik"]

[ext_resource type="Script" uid="uid://cgamw4aed41d8" path="res://script/firefly_particles.gd" id="1_k8qpl"]

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_tlhaw"]
blend_mode = 1
light_mode = 1
particles_animation = true
particles_anim_h_frames = 1
particles_anim_v_frames = 1
particles_anim_loop = true

[sub_resource type="Gradient" id="Gradient_light"]
offsets = PackedFloat32Array(0, 0.5, 1)
colors = PackedColorArray(1, 1, 1, 1, 0.5, 0.8, 0.3, 0.7, 0, 0, 0, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_light"]
gradient = SubResource("Gradient_light")
width = 128
height = 128
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(1, 0.5)

[sub_resource type="Gradient" id="Gradient_firefly"]
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)
colors = PackedColorArray(0, 0, 0, 0, 0.3, 0.8, 0.2, 1, 0.5, 1, 0.3, 1, 0, 0, 0, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_firefly"]
gradient = SubResource("Gradient_firefly")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_firefly"]
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(576, 324, 1)
direction = Vector3(0, -1, 0)
spread = 180.0
initial_velocity_min = 5.0
initial_velocity_max = 15.0
angular_velocity_min = -15.0
angular_velocity_max = 15.0
gravity = Vector3(0, 0, 0)
radial_accel_min = -5.0
radial_accel_max = 5.0
tangential_accel_min = -3.0
tangential_accel_max = 3.0
damping_min = 1.0
damping_max = 5.0
scale_min = 0.1
scale_max = 0.3
color_ramp = SubResource("GradientTexture1D_firefly")
anim_speed_min = 0.2
anim_speed_max = 1.0
turbulence_enabled = true
turbulence_noise_strength = 0.2
turbulence_noise_scale = 0.5
turbulence_noise_speed = Vector3(0.1, 0.1, 0.1)
turbulence_influence_min = 0.01
turbulence_influence_max = 0.03

[node name="FireflyParticles" type="GPUParticles2D"]
modulate = Color(1.2, 1.2, 1, 1)
material = SubResource("CanvasItemMaterial_tlhaw")
amount = 50
texture = SubResource("GradientTexture2D_light")
lifetime = 8.0
preprocess = 4.0
randomness = 1.0
visibility_rect = Rect2(-576, -324, 1152, 648)
process_material = SubResource("ParticleProcessMaterial_firefly")
script = ExtResource("1_k8qpl")

[node name="PulsateTimer" type="Timer" parent="."]
wait_time = 0.05
autostart = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

extends Node

# 弃牌区域管理器
# 负责处理弃牌区域的交互逻辑和动画效果

# 节点引用
var discard_area: Control # 弃牌区域
var discard_image: TextureRect # 弃牌图片节点
var discard_shadow: TextureRect # 阴影节点
var tooltip_bubble # 提示气泡
var cursor_manager # 光标管理器
var audio_manager # 音频管理器
var mask_layer: ColorRect = null # 遮罩层
var hand_container: Node = null # 手牌容器引用

# 状态变量
var area_tween = null # 存储当前的Tween动画引用
var image_breath_tween = null # 呼吸动画
var mouse_is_in_area = false # 鼠标是否在区域内
var is_pending_deletion = false # 是否处于待删除状态
var dashed_line = null # 虚线节点

# 信号
signal is_pending_deletion_cancelled # 取消弃牌状态的信号

# 初始化方法
func initialize(area_node, tooltip, cursor_mgr, audio_mgr):
    # 保存节点引用
    discard_area = area_node
    tooltip_bubble = tooltip
    cursor_manager = cursor_mgr
    audio_manager = audio_mgr
    
    # 获取子节点
    discard_image = discard_area.get_node_or_null("DiscardImage")
    discard_shadow = discard_area.get_node_or_null("DiscardShadow")
    
    # 获取手牌容器引用
    hand_container = discard_area.get_tree().get_root().get_node_or_null("GameScene/PlayerControl/PlayerArea/HandContainer")
    
    # 设置初始状态
    if discard_image:
        discard_image.pivot_offset = discard_image.size / 2
        # discard_image.resized.connect(_on_discard_image_size_changed)
        _start_image_breath()
    
    # 连接信号
    discard_area.mouse_entered.connect(_on_area_mouse_entered)
    discard_area.mouse_exited.connect(_on_area_mouse_exited)
    
    # 添加点击和右键点击处理
    discard_area.gui_input.connect(_on_area_gui_input)
    
    # 创建虚线节点
    _create_dashed_line()
    
    # 注册全局输入处理（替换之前错误的连接方法）
    set_process_input(true)  # 启用_input处理
    
    # 确保脚本节点是 active 的
    set_process(true)

# 创建虚线节点
func _create_dashed_line():
    # 如果已经存在旧的虚线，先移除
    if dashed_line:
        if dashed_line.is_inside_tree():
            dashed_line.queue_free()
        dashed_line = null
    
    # 创建新的虚线节点
    dashed_line = Line2D.new()
    dashed_line.name = "DashedLine"
    dashed_line.width = 50.0
    dashed_line.default_color = Color(1, 1, 1, 0.5)
    
    # 使用虚线效果
    dashed_line.width_curve = Curve.new()
    dashed_line.width_curve.add_point(Vector2(0.0, 0.0))
    dashed_line.width_curve.add_point(Vector2(1.0, 1.0))
    
    # 设置虚线样式
    dashed_line.joint_mode = Line2D.LINE_JOINT_ROUND
    dashed_line.begin_cap_mode = Line2D.LINE_CAP_NONE
    dashed_line.end_cap_mode = Line2D.LINE_CAP_NONE

    dashed_line.texture = load("res://assert/right/arrow.png")
    dashed_line.texture_mode = Line2D.LINE_TEXTURE_TILE

    dashed_line.gradient = load("res://themes/arrow_gradient.tres")
    
    # 设置初始点 - 只需起点和终点，曲线效果会在更新方法中添加
    dashed_line.add_point(Vector2.ZERO)
    dashed_line.add_point(Vector2.ZERO)
    
    dashed_line.visible = false
    
    # 确保高可见性
    dashed_line.z_index = 5
    dashed_line.z_as_relative = false
    
    # 将虚线添加为场景树的最顶层节点
    var canvas_layer = CanvasLayer.new()
    canvas_layer.name = "DashLineLayer"
    canvas_layer.layer = 5  # 确保在最上层
    discard_area.get_tree().get_root().add_child(canvas_layer)
    canvas_layer.add_child(dashed_line)

# DiscardImage自适应pivot_offset
func _on_discard_image_size_changed():
    if discard_image:
        discard_image.pivot_offset = discard_image.size / 2

# 启动呼吸动画
func _start_image_breath():
    if not discard_image and not discard_shadow:
        return
    if image_breath_tween and image_breath_tween.is_valid():
        image_breath_tween.kill()
    # 立即重置scale，防止残留
    discard_image.scale = Vector2(1, 1)
    discard_shadow.scale = Vector2(1, 1)
    image_breath_tween = create_tween()
    # image_breath_tween.set_loops()
    image_breath_tween.set_ease(Tween.EASE_IN_OUT)
    image_breath_tween.set_trans(Tween.TRANS_SINE)
    image_breath_tween.tween_property(discard_image, "scale", Vector2(1.1, 1.1), 1.5)
    image_breath_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.1, 1.1), 1.5)
    image_breath_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.2, 1.5)
    image_breath_tween.tween_property(discard_image, "scale", Vector2(1.0, 1.0), 1.5)
    image_breath_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.0, 1.0), 1.5)
    image_breath_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.4, 1.5)

# 停止呼吸动画
func _stop_image_breath():
    if image_breath_tween and image_breath_tween.is_valid():
        image_breath_tween.kill()
        image_breath_tween = null
    if discard_image:
        discard_image.scale = Vector2(1, 1)

# 停止呼吸动画（外部调用）
func stop_breath_animation():
    _stop_image_breath()

# 处理弃牌区域点击事件
func _on_area_gui_input(event):
    # 处理左键点击事件
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        _toggle_pending_deletion_state()
        # 接受事件，防止传递
        get_viewport().set_input_as_handled()
    
    # 处理右键点击事件 - 取消待删除状态
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_RIGHT and event.pressed and is_pending_deletion:
        _cancel_pending_deletion()
        # 接受事件，防止传递
        get_viewport().set_input_as_handled()

# 切换待删除状态
func _toggle_pending_deletion_state():
    if is_pending_deletion:
        _cancel_pending_deletion()
    else:
        _enter_pending_deletion()

# 创建遮罩层
func _create_mask_layer():
    if mask_layer:
        if mask_layer.is_inside_tree():
            mask_layer.queue_free()
        mask_layer = null
    
    # 创建遮罩层
    mask_layer = ColorRect.new()
    mask_layer.name = "DeletionMaskLayer"
    mask_layer.mouse_filter = Control.MOUSE_FILTER_STOP # 阻止鼠标事件传递
    mask_layer.color = Color(0, 0, 0, 0.0) # 半透明黑色
    
    # 设置遮罩层大小为整个视口
    var viewport_size = discard_area.get_viewport_rect().size
    mask_layer.custom_minimum_size = viewport_size
    mask_layer.size = viewport_size
    
    # 将遮罩层添加到场景中的适当位置
    var canvas_layer = CanvasLayer.new()
    canvas_layer.name = "MaskLayer"
    canvas_layer.layer = 3 # 确保在大多数UI下面，但在游戏内容上面
    discard_area.get_tree().get_root().add_child(canvas_layer)
    canvas_layer.add_child(mask_layer)
    
    # 如果有手牌容器，将其移到遮罩层上层
    if hand_container:
        # 记住原始层级和位置信息
        var original_layer = hand_container.get_parent()
        var original_index = hand_container.get_index()
        var original_global_position = hand_container.global_position
        
        # 创建新的画布层专门放置手牌
        var hand_canvas_layer = CanvasLayer.new()
        hand_canvas_layer.name = "HandLayer"
        hand_canvas_layer.layer = 4 # 确保在遮罩层上面
        discard_area.get_tree().get_root().add_child(hand_canvas_layer)
        
        # 临时保存手牌容器的原始父节点和位置信息
        hand_container.get_parent().remove_child(hand_container)
        hand_canvas_layer.add_child(hand_container)
        
        # 保存原始信息以便恢复
        mask_layer.set_meta("original_hand_parent", original_layer)
        mask_layer.set_meta("original_hand_index", original_index)
        mask_layer.set_meta("original_hand_position", original_global_position)
        
        # 恢复全局位置和缩放
        hand_container.global_position = original_global_position
    
    # 将弃牌区域移到遮罩层上层
    if discard_area:
        # 记住原始层级和位置信息
        var original_discard_parent = discard_area.get_parent()
        var original_discard_index = discard_area.get_index()
        var original_discard_position = discard_area.global_position
        
        # 创建新的画布层专门放置弃牌区域
        var discard_canvas_layer = CanvasLayer.new()
        discard_canvas_layer.name = "DiscardLayer"
        discard_canvas_layer.layer = 4 # 与手牌层同级
        discard_area.get_tree().get_root().add_child(discard_canvas_layer)
        
        # 临时保存弃牌区域的原始父节点和位置信息
        discard_area.get_parent().remove_child(discard_area)
        discard_canvas_layer.add_child(discard_area)
        
        # 保存原始信息以便恢复
        mask_layer.set_meta("original_discard_parent", original_discard_parent)
        mask_layer.set_meta("original_discard_index", original_discard_index)
        mask_layer.set_meta("original_discard_position", original_discard_position)
        
        # 恢复全局位置
        discard_area.global_position = original_discard_position
        mouse_is_in_area = true

# 移除遮罩层
func _remove_mask_layer():
    if mask_layer:
        # 恢复手牌容器到原始位置
        if hand_container:
            var original_parent = mask_layer.get_meta("original_hand_parent")
            var original_index = mask_layer.get_meta("original_hand_index")
            
            if original_parent and hand_container.get_parent():
                # 保存当前全局位置
                var current_global_position = hand_container.global_position
                
                # 移回原始父节点
                hand_container.get_parent().remove_child(hand_container)
                original_parent.add_child(hand_container)
                original_parent.move_child(hand_container, original_index)
                
                # 恢复全局位置和缩放
                hand_container.global_position = current_global_position
        
        # 恢复弃牌区域到原始位置
        if discard_area:
            var original_discard_parent = mask_layer.get_meta("original_discard_parent")
            var original_discard_index = mask_layer.get_meta("original_discard_index")
            
            if original_discard_parent and discard_area.get_parent():
                # 保存当前全局位置
                var current_discard_position = discard_area.global_position
                
                # 移回原始父节点
                discard_area.get_parent().remove_child(discard_area)
                original_discard_parent.add_child(discard_area)
                original_discard_parent.move_child(discard_area, original_discard_index)
                
                # 恢复全局位置
                discard_area.global_position = current_discard_position
                mouse_is_in_area = true # 最好是判断当前鼠标位置是否弃牌区discard_area内
        
        # 移除遮罩层及其父Canvas层
        var parent = mask_layer.get_parent()
        if parent and parent is CanvasLayer:
            parent.queue_free()
        elif mask_layer.is_inside_tree():
            mask_layer.queue_free()
        mask_layer = null

# 进入待删除状态
func _enter_pending_deletion():
    if is_pending_deletion:
        return
    
    is_pending_deletion = true
    
    # 重新创建虚线确保显示正确
    _create_dashed_line()
    
    # 创建遮罩层
    _create_mask_layer()
    
    # Q弹动画切换为打开图片
    _change_discard_image(true)
    
    # 改变鼠标指针为删除模式
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

# 取消待删除状态
func _cancel_pending_deletion():
    if not is_pending_deletion:
        return
    
    is_pending_deletion = false
    
    # 隐藏虚线
    if dashed_line:
        dashed_line.visible = false
        
        # 移除虚线节点及其父Canvas层
        var parent = dashed_line.get_parent()
        if parent and parent is CanvasLayer:
            parent.queue_free()
        elif dashed_line.is_inside_tree():
            dashed_line.queue_free()
        dashed_line = null
    
    # 移除遮罩层
    _remove_mask_layer()
    
    # Q弹动画切换回关闭图片
    _change_discard_image(false)
    
    # 恢复正常鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)
    
    # 发出取消弃牌状态的信号
    emit_signal("is_pending_deletion_cancelled")
    
    # 播放音效
    # if audio_manager:
    #     audio_manager.play_sfx("res://assert/audio/sfx/click.wav")

# 切换弃牌图片并添加Q弹动画
func _change_discard_image(is_open: bool):
    if not discard_image:
        return
    
    # 停止呼吸动画
    _stop_image_breath()
    
    # 创建Q弹缩放动画
    var texture_tween = create_tween()
    texture_tween.set_ease(Tween.EASE_OUT)
    texture_tween.set_trans(Tween.TRANS_ELASTIC)
    
    discard_image.pivot_offset = discard_image.size / 2
    
    # 切换贴图
    texture_tween.tween_callback(func():
        discard_image.texture = load("res://assert/right/open.png" if is_open else "res://assert/right/close.png")
    )
    
    # 然后弹回并稍微过冲
    texture_tween.tween_property(discard_image, "scale", Vector2(1.4, 1.4), 0.3)
    texture_tween.parallel().tween_property(discard_shadow, "scale", Vector2(0.9, 0.9), 0.3)
    texture_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.2, 0.3)
    
    # 最后恢复正常大小
    if is_open:
        texture_tween.tween_property(discard_image, "scale", Vector2(1.1, 1.1), 0.2)
        texture_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.1, 1.1), 0.2)
        texture_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.3, 0.2)
        AudioManager.play_sfx("res://assert/audio/sfx/open.mp3")
    else:
        texture_tween.tween_property(discard_image, "scale", Vector2(1.0, 1.0), 0.2)
        texture_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.0, 1.0), 0.2)
        texture_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.4, 0.2)
        AudioManager.play_sfx("res://assert/audio/sfx/close.mp3")
    
    
    # 完成后恢复呼吸动画
    texture_tween.tween_callback(func():
        if not is_pending_deletion:
            _start_image_breath()
    )

# 更新虚线绘制
func _update_dashed_line():
    if not dashed_line or not discard_area or not is_pending_deletion:
        return
    
    # 确保虚线可见
    dashed_line.visible = true
    
    # 获取当前鼠标位置和弃牌区中心位置
    var mouse_pos = discard_area.get_viewport().get_mouse_position()
    var discard_center = discard_image.global_position + discard_image.size / 2
    
    # 清除旧点
    dashed_line.clear_points()
    
    # 创建曲线效果 - 使用多个点以平滑曲线
    var curve_points = 20
    for i in range(curve_points + 1):
        var t = float(i) / curve_points
        
        # 二次贝塞尔曲线计算
        # 使用与弃牌区和鼠标位置中点向上偏移的控制点以创建弧形效果
        var mid_point = discard_center.lerp(mouse_pos, 0.5)
        var control_point = mid_point + Vector2(0, -80 * sin(PI * t))
        
        # 计算曲线上的点
        var point = discard_center.lerp(mouse_pos, t) + Vector2(0, -40 * sin(PI * t))
        
        # 添加到虚线
        dashed_line.add_point(point)

# 鼠标进入区域
func _on_area_mouse_entered():
    if mouse_is_in_area:
        return
    mouse_is_in_area = true
    _stop_image_breath()
    if area_tween and area_tween.is_valid():
        area_tween.kill()
    
    discard_image.pivot_offset = discard_image.size / 2
    area_tween = create_tween()
    area_tween.set_ease(Tween.EASE_OUT)
    area_tween.set_trans(Tween.TRANS_ELASTIC)
    area_tween.tween_property(discard_image, "scale", Vector2(1.2, 1.2), 0.3)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.2, 1.2), 0.3)
    area_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.2, 0.3)
    
    # 改变鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)
    
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    

# 鼠标离开区域
func _on_area_mouse_exited():
    mouse_is_in_area = false
    if not is_pending_deletion:
        _start_image_breath()
    if area_tween and area_tween.is_valid():
        area_tween.kill()
    area_tween = create_tween()
    area_tween.set_ease(Tween.EASE_OUT)
    area_tween.set_trans(Tween.TRANS_CUBIC)
    area_tween.tween_property(discard_image, "scale", Vector2(1.0, 1.0), 0.2)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.0, 1.0), 0.2)
    area_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.4, 0.2)
    
    # 恢复正常鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)
    
    # 恢复shader参数 - 降低脉冲速度
    if discard_image and discard_image.material:
        discard_image.material.set_shader_parameter("pulsation_speed", 0.5)

# 当卡牌拖动到区域上时
func on_card_drag_over():
    if area_tween and area_tween.is_valid():
        area_tween.kill()
    discard_image.pivot_offset = discard_image.size / 2
    area_tween = create_tween()
    area_tween.set_ease(Tween.EASE_OUT)
    area_tween.set_trans(Tween.TRANS_ELASTIC)
    area_tween.tween_property(discard_image, "scale", Vector2(1.5, 1.5), 0.3)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.5, 1.5), 0.3)
    
    # 调整shader参数 - 增加脉冲速度
    if discard_image and discard_image.material:
        discard_image.material.set_shader_parameter("pulsation_speed", 1.5)
    
    # 改变鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)

# 当卡牌离开区域时
func on_card_drag_exit():
    # 恢复正常大小
    _reset_scale()
    
    # 恢复shader参数 - 降低脉冲速度
    if discard_image and discard_image.material:
        discard_image.material.set_shader_parameter("pulsation_speed", 0.5)
    
    # 恢复正常鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

# 重置缩放
func _reset_scale():
    if area_tween and area_tween.is_valid():
        area_tween.kill()
    area_tween = create_tween()
    area_tween.set_ease(Tween.EASE_OUT)
    area_tween.set_trans(Tween.TRANS_CUBIC)
    area_tween.tween_property(discard_image, "scale", Vector2(1.0, 1.0), 0.2)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.0, 1.0), 0.2)
    area_tween.parallel().tween_property(discard_shadow, "modulate:a", 0.4, 0.2)

# 播放弃牌完成后的动画
func play_discard_complete_animation():
    if area_tween and area_tween.is_valid():
        area_tween.kill()
    area_tween = create_tween()
    area_tween.set_ease(Tween.EASE_OUT)
    area_tween.set_trans(Tween.TRANS_ELASTIC)
    # 先快速放大
    area_tween.tween_property(discard_image, "scale", Vector2(1.5, 1.5), 0.2)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.5, 1.5), 0.2)
    # 然后缓慢恢复到原始大小
    area_tween.tween_property(discard_image, "scale", Vector2(1.0, 1.0), 0.3)
    area_tween.parallel().tween_property(discard_shadow, "scale", Vector2(1.0, 1.0), 0.3)

# 设置区域高亮
func set_highlight(highlight: bool):
    if discard_area:
        discard_area.modulate = Color(1, 0.5, 0.5, 1.0) if highlight else Color(1, 1, 1)

# 获取当前区域的全局矩形（用于检测拖放）
func get_global_rect():
    return discard_area.get_global_rect()

# 获取区域中心位置
func get_center_position() -> Vector2:
    return discard_area.global_position + Vector2(discard_area.size.x/2, discard_area.size.y/2)

# 检查是否处于待删除状态
func is_in_deletion_mode() -> bool:
    return is_pending_deletion

# 处理卡牌删除
func process_card_deletion(card_index: int) -> bool:
    if not is_pending_deletion:
        return false
    
    # 取消待删除状态
    _cancel_pending_deletion()
    
    # 返回true表示应该执行删除操作
    return true

# 处理方法 - 添加鼠标跟踪和虚线绘制
func _process(delta):
    # 更新虚线位置(仅在待删除状态下)
    if is_pending_deletion:
        _update_dashed_line()
    
    # 检测全局右键点击事件(取消待删除状态)
    if is_pending_deletion and Input.is_action_just_pressed("ui_cancel"):
        _cancel_pending_deletion()

# 处理全局输入事件（替换之前的_on_global_gui_input方法）
func _input(event):
    if not is_pending_deletion:
        return
    
    # 处理右键点击事件 - 取消待删除状态
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_RIGHT and event.pressed:
        _cancel_pending_deletion()
        # 接受事件，防止传递
        get_viewport().set_input_as_handled()

# 在退出时确保清理
func _exit_tree():
    _remove_mask_layer()
    if dashed_line and dashed_line.is_inside_tree():
        dashed_line.queue_free()
    if image_breath_tween:
        image_breath_tween.kill()

# # 处理方法
# func _process(delta):
#     if discard_image and discard_shadow:
#         discard_shadow.scale = discard_image.scale * 1.05 

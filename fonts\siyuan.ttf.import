[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://cdnwx7btcvfpe"
path="res://.godot/imported/siyuan.ttf-f830c8b3a29ec4f5c278d523087f7909.fontdata"

[deps]

source_file="res://fonts/siyuan.ttf"
dest_files=["res://.godot/imported/siyuan.ttf-f830c8b3a29ec4f5c278d523087f7909.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}

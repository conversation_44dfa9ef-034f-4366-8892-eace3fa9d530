[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://pim5ij4xkhbg"
path="res://.godot/imported/grass.png-d8574de336dfa2be90c14a1f8e2e8e5f.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assert/back/grass.png"
dest_files=["res://.godot/imported/grass.png-d8574de336dfa2be90c14a1f8e2e8e5f.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1

shader_type canvas_item;
        
uniform float blur_amount : hint_range(0.0, 5.0) = 2.0;
uniform float motion_speed : hint_range(0.0, 10.0) = 5.0;

void fragment() {
    vec2 uv = SCREEN_UV;
    vec2 offset = vec2(sin(TIME * motion_speed) * 0.01, cos(TIME * motion_speed) * 0.01);
    
    // 采样多个偏移点并平均，创建模糊效果
    vec4 color = vec4(0.0);
    float total_weight = 0.0;
    
    for(float x = -blur_amount; x <= blur_amount; x += 1.0) {
        for(float y = -blur_amount; y <= blur_amount; y += 1.0) {
            float weight = 1.0 - length(vec2(x, y)) / (blur_amount * 1.4);
            if(weight < 0.0) weight = 0.0;
            
            color += texture(TEXTURE, UV + vec2(x, y) * 0.01 + offset) * weight;
            total_weight += weight;
        }
    }
    
    COLOR = color / total_weight;
}
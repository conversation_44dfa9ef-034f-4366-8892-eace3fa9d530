[gd_scene load_steps=7 format=3 uid="uid://c8443w5xhulcw"]

[ext_resource type="Script" uid="uid://bqrenhc7x2mvw" path="res://script/card_rules_panel.gd" id="1_card_rules"]
[ext_resource type="Shader" uid="uid://c3vssyi6tuty7" path="res://shader/card_rules_panel_shader.gdshader" id="2_2bke1"]
[ext_resource type="Texture2D" uid="uid://drdnxkdttdkss" path="res://assert/back/scroll02.png" id="2_qfuwm"]

[sub_resource type="Gradient" id="Gradient_2bke1"]
colors = PackedColorArray(0, 0, 0, 1, 0.462291, 0.462291, 0.46229, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_aajbx"]
gradient = SubResource("Gradient_2bke1")
fill = 1
fill_from = Vector2(0.5, 0.5)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_yrjc6"]
shader = ExtResource("2_2bke1")
shader_parameter/gradient = SubResource("GradientTexture2D_aajbx")
shader_parameter/spread = 0.5
shader_parameter/cutoff = 2.0
shader_parameter/size = 0.63
shader_parameter/speed = 1.0
shader_parameter/ray1_density = 50.0
shader_parameter/ray2_density = 15.0
shader_parameter/ray2_intensity = 1.0
shader_parameter/core_intensity = 5.0
shader_parameter/hdr = false
shader_parameter/seed = 8.0

[node name="CardRulesPanel" type="Panel"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -331.0
offset_top = -172.0
offset_right = 319.0
offset_bottom = 178.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(300, 150)
script = ExtResource("1_card_rules")

[node name="Shadow" type="TextureRect" parent="."]
modulate = Color(0, 0, 0, 0.54902)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -92.0
offset_right = 108.0
offset_bottom = 72.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(400, 300)
texture = ExtResource("2_qfuwm")
expand_mode = 1
stretch_mode = 5

[node name="Background" type="TextureRect" parent="."]
modulate = Color(0.860369, 0.860369, 0.860369, 1)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -84.0
offset_top = -111.0
offset_right = 84.0
offset_bottom = 53.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(400, 300)
texture = ExtResource("2_qfuwm")
expand_mode = 1
stretch_mode = 5

[node name="Shine" type="ColorRect" parent="."]
show_behind_parent = true
material = SubResource("ShaderMaterial_yrjc6")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -518.0
offset_top = -366.0
offset_right = 518.0
offset_bottom = 358.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(578, 317)
mouse_filter = 2

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = -40.0
offset_right = -30.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 30
alignment = 1

[node name="CardRulesTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.8, 0.2, 1)
theme_override_colors/font_outline_color = Color(0.7, 0, 0, 1)
theme_override_constants/outline_size = 6
theme_override_font_sizes/font_size = 36
text = "CardRulesTitle"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="RuleContent" type="RichTextLabel" parent="VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_colors/table_even_row_bg = Color(0.803922, 0.690196, 0.580392, 1)
theme_override_constants/table_v_separation = 6
theme_override_constants/table_h_separation = 10
theme_override_constants/line_separation = -8
theme_override_font_sizes/normal_font_size = 15
theme_override_font_sizes/bold_font_size = 20
bbcode_enabled = true
fit_content = true
scroll_active = false
horizontal_alignment = 1
vertical_alignment = 3

[node name="CloseButton" type="Button" parent="."]
visible = false
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -52.0
offset_top = -62.0
offset_right = -12.0
offset_bottom = -20.0
grow_horizontal = 0
theme_override_font_sizes/font_size = 24
text = "×"

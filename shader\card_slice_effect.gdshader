shader_type canvas_item;

uniform vec2 slice_center = vec2(0.5, 0.5);

uniform float slice_angle = 6.0;

uniform float slice_distance = 0.0;

uniform float pixel_size = 300.0;

uniform float expansion_factor = 0;

uniform vec4 border_color = vec4(0.0, 0.0, 0.0, 0.0);

vec2 pixel_uv(vec2 uv, float pixel_sizee) {
    return floor(uv * pixel_sizee) / pixel_sizee;
}

vec2 rotate(vec2 point, vec2 center, float angle) {
    vec2 translated_point = point - center;
    float sin_angle = sin(angle);
    float cos_angle = cos(angle);
    vec2 rotated_point = vec2(
        translated_point.x * cos_angle - translated_point.y * sin_angle,
        translated_point.x * sin_angle + translated_point.y * cos_angle
    );
    return rotated_point + center;
}

void vertex() {
    VERTEX *= (1.0 + expansion_factor * 2.0);
    UV = (UV - 0.5) * (1.0 + expansion_factor * 2.0) + 0.5;
}

void fragment() {
    vec2 uv = UV;

    uv = pixel_uv(uv, pixel_size);

    vec2 offset = uv - slice_center;

    vec2 rotated_offset = rotate(offset, vec2(0.0), -slice_angle);

    bool is_top = rotated_offset.y > 0.0;

    vec2 movement = vec2(
        slice_distance * cos(slice_angle),
        slice_distance * sin(slice_angle)
    );

    if (is_top) {
        uv += movement;
    } else {
        uv -= movement;
    }

    if (uv.x < 0.0 || uv.x > 1.0 || uv.y < 0.0 || uv.y > 1.0) {
        COLOR = border_color;
    } else {
        COLOR = texture(TEXTURE, uv);
    }
}

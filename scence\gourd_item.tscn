[gd_scene load_steps=11 format=3 uid="uid://b4r6n8p3j2xky"]

[ext_resource type="Script" uid="uid://cv6818yss2exd" path="res://script/gourd_item.gd" id="1_script"]
[ext_resource type="Shader" uid="uid://c87ppamjqg4kc" path="res://shader/shine_panel_shader.gdshader" id="2_0jv5y"]
[ext_resource type="PackedScene" uid="uid://c8j6vw4m4yqx5" path="res://scence/hover_tooltip.tscn" id="3_tooltip"]
[ext_resource type="Texture2D" uid="uid://derhkcr1h0k7q" path="res://assert/victory_panel/card_coin.png" id="4_37o84"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_selected"]
bg_color = Color(1, 0.843137, 0, 0.196078)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.843137, 0, 1)
corner_radius_top_left = 12
corner_radius_top_right = 12
corner_radius_bottom_right = 12
corner_radius_bottom_left = 12

[sub_resource type="FastNoiseLite" id="FastNoiseLite_37o84"]

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_0jv5y"]
noise = SubResource("FastNoiseLite_37o84")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_8gj0g"]

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_37o84"]
noise = SubResource("FastNoiseLite_8gj0g")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_4h57l"]
shader = ExtResource("2_0jv5y")
shader_parameter/shine_num = 16.0
shader_parameter/rotation_speed = 0.5
shader_parameter/light_magnitude = 4.0
shader_parameter/filter_distence = 1.0
shader_parameter/filter_magnitude = 1.0
shader_parameter/filter_minv = 0.818
shader_parameter/cut_inner_radius = 0.0
shader_parameter/color = Color(1, 1, 0, 1)
shader_parameter/NOISE = SubResource("NoiseTexture2D_0jv5y")
shader_parameter/NOISE2 = SubResource("NoiseTexture2D_37o84")
shader_parameter/NOISE_speed = Vector2(0, 0)
shader_parameter/NOISE_magnitude = 0.0

[node name="GourdItem" type="Control"]
custom_minimum_size = Vector2(140, 180)
layout_mode = 3
anchors_preset = 0
offset_right = 140.0
offset_bottom = 180.0
pivot_offset = Vector2(70, 90)
script = ExtResource("1_script")

[node name="SelectionPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_selected")

[node name="Shine" type="ColorRect" parent="."]
visible = false
material = SubResource("ShaderMaterial_4h57l")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(70, 90)

[node name="FloatingContainer" type="Control" parent="."]
custom_minimum_size = Vector2(140, 140)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(70, 70)

[node name="Shadow" type="TextureRect" parent="FloatingContainer"]
modulate = Color(0, 0, 0, 0.32549)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 19.0
offset_top = 8.0
offset_right = 7.0
offset_bottom = -2.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(50, 50)
texture = ExtResource("4_37o84")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="GourdTexture" type="TextureRect" parent="FloatingContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 7.0
offset_top = 3.0
offset_right = -5.0
offset_bottom = -7.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(50, 50)
texture = ExtResource("4_37o84")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="BuffName" type="RichTextLabel" parent="FloatingContainer"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 31.0
offset_top = -38.0
offset_right = -28.0
offset_bottom = -16.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/default_color = Color(0.266667, 0.0941176, 0.376471, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 0
theme_override_font_sizes/normal_font_size = 15
bbcode_enabled = true
text = "BUFF名称"
fit_content = true
scroll_active = false
autowrap_mode = 0
shortcut_keys_enabled = false
horizontal_alignment = 1
vertical_alignment = 1

[node name="HoverTooltip" parent="." instance=ExtResource("3_tooltip")]
visible = false

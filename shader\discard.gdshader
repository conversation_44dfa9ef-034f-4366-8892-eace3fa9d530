shader_type canvas_item;

uniform sampler2D portal_texture : source_color, filter_linear_mipmap_anisotropic;

uniform vec4 portal_tint : source_color = vec4(1.0, 1.0, 1.0, 1.0);

uniform float vortex_effect_radius : hint_range(0.01, 1.0, 0.01) = 0.5;

uniform float twist_strength : hint_range(0.0, 30.0, 0.1) = 20;

uniform float pulsation_speed : hint_range(0.0, 5.0, 0.01) = 1.5;

uniform float breath_magnitude : hint_range(-0.3, 0.3, 0.005) = 0.05;

uniform float overall_rotation_speed : hint_range(-2.0, 2.0, 0.01) = 0.25;

uniform float texture_scroll_speed_x : hint_range(-1.0, 1.0, 0.01) = 0;

uniform float texture_scroll_speed_y : hint_range(-1.0, 1.0, 0.01) = 0;

uniform float edge_softness : hint_range(0.01, 0.5, 0.005) = 0.1;

mat2 rotate(float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return mat2(vec2(c, -s), vec2(s, c));
}


void fragment() {
    vec2 node_pixel_size = 1.0 / TEXTURE_PIXEL_SIZE;
    vec2 uv = UV - 0.5;

    if (node_pixel_size.x > node_pixel_size.y) {
        uv.x *= node_pixel_size.x / node_pixel_size.y;
    } else if (node_pixel_size.y > node_pixel_size.x) {
        uv.y *= node_pixel_size.y / node_pixel_size.x;
    }

    float time_progress = sin(TIME * pulsation_speed);

    if (overall_rotation_speed != 0.0) {
        uv = rotate(TIME * overall_rotation_speed) * uv;
    }

    float distance_from_center = length(uv);

    float twist_spatial_factor = smoothstep(0.0, vortex_effect_radius, vortex_effect_radius - distance_from_center);
    float current_animated_twist = twist_strength * time_progress;
    float twist_angle = twist_spatial_factor * current_animated_twist;

    if (distance_from_center < vortex_effect_radius) {
        uv = rotate(twist_angle) * uv;
    }

    float breath_value = breath_magnitude * time_progress;
    if (distance_from_center > 0.0001) {
        float new_radial_distance = max(0.0, distance_from_center + breath_value);
        uv *= (new_radial_distance / distance_from_center);
    }

    vec2 texture_scroll_speed = vec2(texture_scroll_speed_x, texture_scroll_speed_y);
    vec2 scroll_offset = TIME * texture_scroll_speed;
    vec2 final_scrolling_uv = uv + scroll_offset;

    if (node_pixel_size.x > node_pixel_size.y) {
        final_scrolling_uv.x /= (node_pixel_size.x / node_pixel_size.y);
    } else if (node_pixel_size.y > node_pixel_size.x) {
        final_scrolling_uv.y /= (node_pixel_size.y / node_pixel_size.x);
    }
    final_scrolling_uv += 0.5;

    float alpha = smoothstep(vortex_effect_radius, vortex_effect_radius - edge_softness, distance_from_center);

    vec4 texture_color = texture(portal_texture, fract(final_scrolling_uv));

    COLOR = texture_color * portal_tint;
    COLOR.a *= alpha;

    if (UV.x < 0.0 || UV.x > 1.0 || UV.y < 0.0 || UV.y > 1.0) {
        COLOR.a = 0.0;
    }
}
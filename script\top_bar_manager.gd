extends Control

# TopBarManager - 处理游戏界面顶部栏的展示逻辑
# 包括阶数、总积分和限制积分的展示和动画效果

# 节点引用
@onready var round_label = $RoundInfo/Round/RoundLabel
@onready var round_title = $RoundInfo/Round/RoundTitle
@onready var round_icon = $RoundInfo/Round/round
@onready var level_label = $RoundInfo/Level/LevelLabel
@onready var level_title = $RoundInfo/Level/LevelTitle
@onready var level_icon = $RoundInfo/Level/level
@onready var score_value_label = $ScoreInfo/ScoreLabel
@onready var limit_label = $LimitInfo/LimitLabel
@onready var score_coin = $ScoreInfo/coins
@onready var cash_icon = $LimitInfo/cash
@onready var reduction_hint = $ScoreInfo/ReductionHint
@onready var progress_bar = null # 进度条会在_setup_progress_bar中创建和设置
@onready var progress_label = null # 进度条文本会在_setup_progress_bar中创建和设置
@onready var round_progress_bar = null # 阶进度条会在_setup_round_progress_bar中创建和设置
@onready var round_progress_label = null # 阶进度条文本会在_setup_round_progress_bar中创建和设置

# 动画相关变量
var coin_shine_timer: float = 0.0
var coin_shine_interval: float = 8.0  # 每8秒一次光泽效果
var is_coin_shining: bool = false
var coin_jump_timer: float = 0.0
var coin_jump_interval: float = 5.0  # 每5秒跳一次
var coin_is_jumping: bool = false
var is_score_animating: bool = false
var is_limit_animating: bool = false
var limit_label_animation_timer: float = 0.0
var limit_label_animation_speed: float = 3.0
var level_shine_timer: float = 0.0
var level_shine_interval: float = 6.0  # 每6秒一次炫光效果

# 积分警告动画相关变量
var score_warning_timer: float = 0.0
var score_warning_interval: float = 4.0  # 每4秒检查一次
var is_score_warning: bool = false

# 进度条相关变量
var current_progress: float = 0.0
var total_level_score: int = 0  # 关卡总积分（只包括牌面、组合和BUFF积分）
var gross_score: int = 0  # 毛积分（牌面、组合和BUFF积分的总和）
var current_score: int = 0  # 当前净积分（包含扣减后的值）
var total_level_rounds: int = 0  # 关卡总阶数
var current_round: int = 1  # 当前阶数

var round_progress: float = 0.0
var progress_shader = ShaderMaterial.new()


# 初始化
func _init_ready():
    # 确保引用有效
    if score_value_label == null:
        score_value_label = $ScoreInfo/ScoreLabel
    
    if score_coin == null:
        score_coin = $ScoreInfo/coins
    
    # 初始化累积限制积分
    GameManager.accumulated_limit_score = 0
    
    # 设置金币图标
    _setup_coin_icon()
    
    # 应用样式和颜色到标签
    _apply_styles()
    
    # 连接需要的信号
    _connect_signals()
    
    # 初始化进度条
    # _setup_progress_bar()
    
    # 初始化阶进度条
    _setup_round_progress_bar()
    
    # 如果进度条已经存在，设置其缩放中心点
    if round_progress_bar:
        round_progress_bar.pivot_offset = round_progress_bar.size / 2

    reduction_hint.visible = false
    
    # 明确启用处理功能
    set_process(true)
    print("TopBarManager: _ready完成，处理功能已启用")
    
    # 调试信息
    # dump_node_info()
    
    # 测试是否处理功能正常工作
    get_tree().create_timer(1.0).timeout.connect(func(): print("TopBarManager: 5秒后验证处理状态: ", is_processing()))

# 打印节点调试信息
func dump_node_info():
    print("====TopBarManager节点信息====")
    print("是否在场景树中: ", is_inside_tree())
    print("是否可见: ", visible)
    print("是否正在处理: ", is_processing())
    print("名称: ", name)
    print("父节点: ", get_parent().name if get_parent() else "无")
    print("子节点数量: ", get_child_count())
    for i in range(get_child_count()):
        print("子节点[", i, "]: ", get_child(i).name)
    print("=============================")

# 设置金币图标
func _setup_coin_icon():
    # 如果score_coin已经存在，则不需要创建
    if score_coin != null:
        # 设置金币的缩放中心点
        score_coin.pivot_offset = score_coin.size / 2
        
        # 应用闪光效果Shader
        var shine_material = ShaderMaterial.new()
        shine_material.shader = load("res://shader/coin_shine.gdshader")
        shine_material.set_shader_parameter("shine_color", Color(1.0, 1.0, 0.8, 0.8))
        shine_material.set_shader_parameter("shine_speed", 0.5)
        shine_material.set_shader_parameter("shine_width", 0.2)
        score_coin.material = shine_material
        return
    
    # 如果score_value_label为null，无法继续处理
    if score_value_label == null:
        push_error("无法设置金币图标: score_value_label为null")
        return
    
    # 创建金币图标
    score_coin = TextureRect.new()
    score_coin.name = "coins"
    score_coin.texture = load("res://assert/coins/coins.png")
    score_coin.custom_minimum_size = Vector2(40, 40)
    score_coin.expand_mode = 1  # EXPAND_KEEP_ASPECT
    score_coin.stretch_mode = 4  # STRETCH_KEEP_ASPECT_CENTERED
    
    # 添加圆角效果
    var corner_material = ShaderMaterial.new()
    corner_material.shader = load("res://shader/card_corner.gdshader")
    corner_material.set_shader_parameter("corner_radius", 0.15)
    score_coin.material = corner_material
    
    # 设置旋转中心
    score_coin.pivot_offset = score_coin.size / 2
    
    # 将金币添加到积分标签前面
    var parent = score_value_label.get_parent()
    var index = score_value_label.get_index()
    
    # 创建容器来放置金币和数字
    var container = HBoxContainer.new()
    container.alignment = BoxContainer.ALIGNMENT_CENTER
    container.add_theme_constant_override("separation", 10)  # 增加间距
    
    # 重新组织节点结构
    parent.remove_child(score_value_label)
    parent.add_child(container)
    parent.move_child(container, index)
    
    container.add_child(score_coin)
    container.add_child(score_value_label)
    
    # 应用闪光效果Shader
    var shine_material = ShaderMaterial.new()
    shine_material.shader = load("res://shader/coin_shine.gdshader")
    shine_material.set_shader_parameter("shine_color", Color(1.0, 1.0, 0.8, 0.8))
    shine_material.set_shader_parameter("shine_speed", 0.5)
    shine_material.set_shader_parameter("shine_width", 0.2)
    score_coin.material = shine_material

# 应用样式和颜色
func _apply_styles():
    # 为分数标签应用样式
    if score_value_label:
        # score_value_label.add_theme_font_size_override("font_size", 30)
        score_value_label.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
        score_value_label.add_theme_constant_override("outline_size", 4)
        score_value_label.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 1.0))
    
    # 为限制标签设置样式
    if limit_label:
        limit_label.pivot_offset = limit_label.size / 2  # 设置缩放中心点
        
    # 为阶数标签设置样式
    if round_label:
        # round_label.add_theme_font_size_override("font_size", 30)
        round_label.pivot_offset = round_label.size / 2  # 设置缩放中心点
    
    # if round_title:
    #     round_title.add_theme_font_size_override("font_size", 15)
    
    # 为阶图标设置旋转中心点
    if round_icon:
        round_icon.pivot_offset = round_icon.size / 2
    
    # 为关卡标签设置样式
    if level_label:
        # level_label.add_theme_font_size_override("font_size", 30)
        level_label.add_theme_color_override("font_color", Color(0.3, 0.7, 1.0, 1.0)) # 蓝色
        level_label.pivot_offset = level_label.size / 2  # 设置缩放中心点

    # if level_title:
    #     level_title.add_theme_font_size_override("font_size", 15)
    
    # 为关卡图标设置旋转中心点
    if level_icon:
        level_icon.pivot_offset = level_icon.size / 2

# 连接必要的信号
func _connect_signals():
    # 游戏管理器信号
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        game_manager.round_changed.connect(_on_round_changed)
        game_manager.score_changed.connect(_on_score_changed)
        game_manager.limit_score_changed.connect(_on_limit_score_changed)
        game_manager.level_changed.connect(_on_level_changed)
        game_manager.game_state_changed.connect(_on_game_state_changed)

# 帧更新处理
func _process(delta): 
    # 确保节点在处理前有效
    if not is_instance_valid(self) or not is_processing():
        return
    
    # 处理限制标签动画
    # _process_limit_label_animation(delta)
    
    # 金币跳跃动画 - 只有在没有积分动画和跳跃动画时才播放
    #if score_coin and is_instance_valid(score_coin) and !is_coin_shining and !coin_is_jumping and !is_score_animating:
        #coin_jump_timer += delta
        #if coin_jump_timer >= coin_jump_interval:
            #coin_jump_timer = 0
            #_play_coin_jump_animation()
    
    # 金币光泽动画
    if score_coin and is_instance_valid(score_coin) and !is_coin_shining and !coin_is_jumping and !is_score_animating:
        coin_shine_timer += delta
        if coin_shine_timer >= coin_shine_interval:
            coin_shine_timer = 0
            _play_coin_shine_animation()
            
    # 等级图标炫光效果
    if level_icon and is_instance_valid(level_icon):
        level_shine_timer += delta
        if level_shine_timer >= level_shine_interval:
            level_shine_timer = 0
            _play_level_shine_animation()
            
    # 检查积分是否小于限制值
    if score_value_label and limit_label and !is_score_warning:
        score_warning_timer += delta
        if score_warning_timer >= score_warning_interval:
            score_warning_timer = 0
            var current_score = int(score_value_label.text)
            var limit_score = int(limit_label.text)
            if current_score < limit_score:
                is_score_warning = true
                var animation_manager = get_node_or_null("/root/AnimationManager")
                if animation_manager:
                    # 播放警告动画
                    animation_manager.play_score_warning_animation(score_value_label)
                    await get_tree().create_timer(1).timeout
                    is_score_warning = false

# 处理限制标签的动画
func _process_limit_label_animation(delta):
    # 确保所有需要的节点都有效
    if not is_limit_animating or not limit_label or not is_instance_valid(limit_label):
        return
    
    # 安全地更新动画
    if not is_processing():
        return
        
    limit_label_animation_timer += delta * limit_label_animation_speed
    
    # 计算脉动效果
    var pulse = abs(sin(limit_label_animation_timer))
    var scale_factor = 1.0 + pulse * 0.1  # 10%的脉动幅度
    
    # 应用脉动效果到限制标签
    if limit_label and is_instance_valid(limit_label):
        limit_label.scale = Vector2(scale_factor, scale_factor)
        
        # 更改颜色 - 从正常到警告色
        var warning_color = Color(1, 0.5, 0, 1)  # 橙色警告色
        var normal_color = Color(1, 1, 1, 1)  # 原始颜色
        limit_label.add_theme_color_override("font_color", normal_color.lerp(warning_color, pulse))
    
    # 如果cash图标存在，也给它应用相同的脉动效果
    if cash_icon and is_instance_valid(cash_icon):
        cash_icon.scale = Vector2(scale_factor, scale_factor)
        # 添加额外的轻微旋转效果
        cash_icon.rotation_degrees = pulse * 5.0  # 最大旋转角度为5度

# 金币跳跃动画
func _play_coin_jump_animation():
    if coin_is_jumping or not score_coin:
        return
    
    coin_is_jumping = true
    
    # 创建跳跃动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BOUNCE)
    
    # 跳跃动画序列 - 更高的跳跃高度
    tween.tween_property(score_coin, "position:y", score_coin.position.y - 30, 0.2)
    tween.tween_property(score_coin, "position:y", score_coin.position.y, 0.35)
    
    # 同时添加旋转和缩放效果
    var parallel_tween = create_tween()
    parallel_tween.set_ease(Tween.EASE_OUT)
    parallel_tween.set_trans(Tween.TRANS_BACK)
    
    # 添加缩放效果
    parallel_tween.tween_property(score_coin, "scale", Vector2(1.2, 1.2), 0.2)
    parallel_tween.tween_property(score_coin, "scale", Vector2(1.0, 1.0), 0.35)
    
    # 添加旋转效果
    var rotation_tween = create_tween()
    rotation_tween.set_ease(Tween.EASE_OUT)
    rotation_tween.set_trans(Tween.TRANS_SINE)
    rotation_tween.tween_property(score_coin, "rotation_degrees", 360, 0.55)
    rotation_tween.tween_property(score_coin, "rotation_degrees", 0, 0.01)
    
    # 完成后重置跳跃状态
    await tween.finished
    coin_is_jumping = false

# 金币光泽特效
func _play_coin_shine_animation():
    if is_coin_shining or not score_coin or not score_coin.material:
        return
        
    is_coin_shining = true
    
    # 设置shader参数以触发光泽效果
    score_coin.material.set_shader_parameter("shine_progress", 0.0)
    
    # 创建光泽动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_trans(Tween.TRANS_SINE)
    tween.tween_method(func(value): score_coin.material.set_shader_parameter("shine_progress", value), 
                      0.0, 1.0, 1.5)
    
    # 添加轻微旋转
    #var rotate_tween = create_tween()
    #rotate_tween.set_ease(Tween.EASE_IN_OUT)
    #rotate_tween.set_trans(Tween.TRANS_SINE)
    #rotate_tween.tween_property(score_coin, "rotation_degrees", -5, 0.7)
    #rotate_tween.tween_property(score_coin, "rotation_degrees", 5, 0.7)
    #rotate_tween.tween_property(score_coin, "rotation_degrees", 0, 0.3)
    
    # 完成后重置状态
    await tween.finished
    is_coin_shining = false

# 通用的金币Q弹膨胀效果方法
func play_coin_elastic_effect(coin_node, scale_tween = null):
    if not coin_node:
        return scale_tween
        
    # 金币Q弹膨胀效果
    if scale_tween == null:
        scale_tween = create_tween()
    scale_tween.set_ease(Tween.EASE_OUT)
    scale_tween.set_trans(Tween.TRANS_ELASTIC)
    scale_tween.tween_property(coin_node, "scale", Vector2(1.4, 1.4), 0.3)
    scale_tween.tween_property(coin_node, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 添加闪光效果
    if coin_node.material and coin_node.material.has_method("set_shader_parameter"):
        if coin_node.material.shader and coin_node.material.shader.resource_path.ends_with("coin_shine.gdshader"):
            coin_node.material.set_shader_parameter("shine_progress", 0.0)
            var shine_tween = create_tween()
            shine_tween.tween_method(func(value): coin_node.material.set_shader_parameter("shine_progress", value), 
                           0.0, 1.0, 0.8)
    
    return scale_tween

# 播放积分动画
func play_score_animation(old_score, new_score):
    # 标记积分动画正在播放
    is_score_animating = true
    
    # 创建动画
    var tween = create_tween()
    
    # 数字滚动效果 - 从旧积分滚动到新积分
    if score_value_label:
        tween.parallel().tween_method(func(value): self.score_value_label.text = "%d" % value, old_score, new_score, 0.3)
        
        # 膨胀效果
        tween.parallel().tween_property(self.score_value_label, "scale", Vector2(1.5, 1.5), 0.3)
        tween.parallel().tween_property(self.score_value_label, "theme_override_colors/font_color", Color(1, 0.9, 0, 1), 0.3) # 变为亮金色
        
        # 添加轻微抖动效果
        tween.parallel().tween_property(self.score_value_label, "rotation", -0.05, 0.2)
        tween.tween_property(self.score_value_label, "rotation", 0, 0.1)
    
    # 使用单独提取的方法播放金币膨胀效果
    if score_coin:
        play_coin_elastic_effect(score_coin, tween)
    
    # 恢复正常大小和颜色
    if score_value_label:
        tween.parallel().tween_property(self.score_value_label, "scale", Vector2(1.0, 1.0), 0.2)
        tween.parallel().tween_property(self.score_value_label, "theme_override_colors/font_color", Color(1.0, 0.84, 0.0, 1.0), 0.2) # 恢复金色
    
    # 动画结束后重置积分动画状态
    tween.tween_callback(func(): is_score_animating = false)
    
    return tween

# 播放限制积分动画
func play_limit_animation(new_limit):
    # 创建动画
    var tween = create_tween()
    
    # 设置限制标签动画
    tween.tween_property(limit_label, "scale", Vector2(1.3, 1.3), 0.2)
    #tween.tween_property(limit_label, "theme_override_colors/font_color", Color(1, 0.5, 0, 1), 0.2)
    
    # 如果cash图标存在，也给它设置动画
    if cash_icon:
        var cash_tween = create_tween()
        cash_tween.tween_property(cash_icon, "scale", Vector2(1.3, 1.3), 0.2)
        cash_tween.tween_property(cash_icon, "scale", Vector2(1.0, 1.0), 0.2)
        # 添加轻微旋转动画
        cash_tween.parallel().tween_property(cash_icon, "rotation_degrees", 15, 0.2)
        cash_tween.tween_property(cash_icon, "rotation_degrees", 0, 0.2)
    
    # 继续限制标签的动画
    tween.tween_property(limit_label, "scale", Vector2(1.0, 1.0), 0.2)
    #tween.tween_property(limit_label, "theme_override_colors/font_color", Color(1, 1, 1, 1), 0.2)
    
    return tween

# 分数增加时的Q弹膨胀特效
func play_score_increase_animation(old_score, new_score):
    # 标记积分动画正在播放
    is_score_animating = true
    
    # 数字变化动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    if score_value_label:
        tween.tween_method(func(value): score_value_label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 使用通用的金币Q弹膨胀效果
    if score_coin:
        # 播放Q弹膨胀效果
        play_coin_elastic_effect(score_coin, tween)
    
    # 数字也有膨胀效果
    if score_value_label:
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(score_value_label, "scale", Vector2(1.3, 1.3), 0.3)
        tween.tween_property(score_value_label, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 确保动画完成后重置状态
    var delay_timer = get_tree().create_timer(0.8)
    delay_timer.timeout.connect(func(): is_score_animating = false)
    
    return tween

# 分数减少时的抖动特效
func play_score_decrease_animation(old_score, new_score):
    # 标记积分动画正在播放
    is_score_animating = true
    
    # 数字变化动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    if score_value_label:
        tween.tween_method(func(value): score_value_label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 金币抖动特效
    if score_coin:
        var shake_tween = create_tween()
        shake_tween.set_ease(Tween.EASE_OUT)
        shake_tween.set_trans(Tween.TRANS_SINE)
        
        # 金币快速抖动
        for i in range(8):
            var offset = 3.0 * (1.0 - i/8.0)  # 抖动幅度逐渐减小
            var direction = 1 if i % 2 == 0 else -1
            shake_tween.tween_property(score_coin, "position:x", 
                                 score_coin.position.x + offset * direction, 0.05)
        
        # 最后回到原位
        shake_tween.tween_property(score_coin, "position:x", score_coin.position.x, 0.05)
        
        # 颜色变红后恢复
        var color_tween = create_tween()
        color_tween.tween_property(score_coin, "modulate", Color(1.5, 0.5, 0.5, 1.0), 0.2)
        color_tween.tween_property(score_coin, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.3)
    
    # 数字闪红
    if score_value_label:
        var label_color_tween = create_tween()
        label_color_tween.tween_property(score_value_label, "theme_override_colors/font_color", Color(1.0, 0.3, 0.3, 1.0), 0.2)
        label_color_tween.tween_property(score_value_label, "theme_override_colors/font_color", Color(1.0, 0.84, 0.0, 1.0), 0.3)
    
    # 确保动画完成后重置状态
    var delay_timer = get_tree().create_timer(0.8)
    delay_timer.timeout.connect(func(): is_score_animating = false)
    
    return tween

# 播放阶数增加动画
func play_round_increase_animation(old_round, new_round):
    if not round_icon or not round_label:
        return

    # 播放阶升级开始音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/break.wav")
        
    # 创建阶升级特效
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 阶图标动画 - Q弹膨胀并旋转
    tween.tween_property(round_icon, "scale", Vector2(1.5, 1.5), 0.3)
    tween.parallel().tween_property(round_icon, "modulate", Color(2.0, 2.0, 0.5, 1.0), 0.3) # 金黄色强光
    tween.parallel().tween_property(round_icon, "rotation_degrees", 360, 0.6) # 旋转一周
    
    # 阶数字增加动画
    var label_tween = create_tween()
    label_tween.set_ease(Tween.EASE_OUT)
    label_tween.set_trans(Tween.TRANS_ELASTIC)
    label_tween.tween_method(func(value): round_label.text = str(value), old_round, new_round, 0.5)
    label_tween.parallel().tween_property(round_label, "modulate", Color(2.0, 2.0, 0.5, 1.0), 0.3)
    label_tween.parallel().tween_property(round_label, "scale", Vector2(1.5, 1.5), 0.3)
    
    # 创建升级粒子效果
    AnimationManager.create_level_up_particles(round_label.global_position)
    
    # 恢复正常
    tween.tween_property(round_icon, "scale", Vector2(1.0, 1.0), 0.4)
    tween.tween_property(round_icon, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.4)
    tween.tween_property(round_icon, "rotation_degrees", 0, 0.01)
    label_tween.tween_property(round_label, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.4)
    label_tween.tween_property(round_label, "scale", Vector2(1.0, 1.0), 0.4)
    
    return tween

# 等级图标炫光动画
func _play_level_shine_animation():
    if not round_icon or not round_label:
        return
    
    # 创建炫光动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_trans(Tween.TRANS_SINE)
    
    # 图标轻微放大并发光
    tween.tween_property(round_icon, "modulate", Color(1.5, 1.5, 1.0, 1.0), 0.5)
    tween.tween_property(round_icon, "scale", Vector2(1.2, 1.2), 0.5)
    
    # 同时让阶数也有轻微发光效果
    var label_tween = create_tween()
    label_tween.set_ease(Tween.EASE_IN_OUT)
    label_tween.set_trans(Tween.TRANS_SINE)
    label_tween.tween_property(round_label, "modulate", Color(1.5, 1.5, 0.8, 1.0), 0.5)
    
    # 恢复正常
    tween.tween_property(round_icon, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.5)
    tween.tween_property(round_icon, "scale", Vector2(1.0, 1.0), 0.5)
    label_tween.tween_property(round_label, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.5)

# 阶数变化处理
func _on_round_changed(new_round):
    if round_label:
        # 获取当前阶数
        var old_round = 1
        if round_label.text.is_valid_int():
            old_round = int(round_label.text)
        
        # 更新当前阶数
        current_round = new_round
        
        # 更新阶进度条
        _update_round_progress_bar()
        
        # 如果阶数增加，播放升级动画
        if new_round > old_round:
            play_round_increase_animation(old_round, new_round)
        else:
            # 直接更新阶数文本（只显示数字）
            round_label.text = str(new_round)

# 分数变化处理
func _on_score_changed(new_score, total_score_for_level = 0, progress = 0.0, is_limit_score = true):
    # 更新净积分值
    current_score = new_score
    
    # 更新总积分值（如果提供）
    if total_score_for_level > 0:
        total_level_score = total_score_for_level
        
        # 更新毛积分（不含扣减的积分总和）
        if is_limit_score:
            gross_score = current_score + GameManager.accumulated_limit_score
        else:
            gross_score = current_score
        
        # 确保毛积分不超过总积分（防止进度条溢出）
        gross_score = min(gross_score, total_level_score)
    
    # 实时计算进度，使用毛积分（不考虑扣减）
    if total_level_score > 0:
        current_progress = float(gross_score) / total_level_score
        # 确保进度不超过1.0
        current_progress = min(current_progress, 1.0)
    else:
        current_progress = 0.0
    
    # 更新进度条
    _update_progress_bar()
    
    if score_value_label:
        # 确定分数变化类型
        var old_score = 0
        var current_text = score_value_label.text
        if current_text != "0" and current_text.is_valid_int():
            old_score = int(current_text)
        
        # 分数增加时使用Q弹膨胀特效
        if new_score > old_score:
            play_score_increase_animation(old_score, new_score)
        # 分数减少时使用抖动特效
        elif new_score < old_score:
            play_score_decrease_animation(old_score, new_score)
        else:
            # 分数不变，使用简单动画
            var tween = create_tween()
            tween.tween_method(func(value): score_value_label.text = "%d" % value, old_score, new_score, 0.3)
            # 动画结束后重置积分动画状态
            tween.tween_callback(func(): is_score_animating = false)

# 限制积分变化处理
func _on_limit_score_changed(new_limit):
    # 更新限制标签文本
    if limit_label:
        limit_label.text = str(new_limit)
        
        # 播放限制标签动画
        play_limit_animation(new_limit)
        
        # 开启限制标签的持续动画
        is_limit_animating = true
        limit_label_animation_timer = 0.0  # 重置动画计时器
    
    # 更新累积扣减的限制积分
    GameManager.current_limit_score = new_limit
    GameManager.accumulated_limit_score += new_limit
    
    # 注意：不再更新进度条，因为扣减积分不影响进度条
    # 只更新标签显示来反映扣减
    _update_progress_label()

# 启用限制标签动画
func enable_limit_animation(enable = true):
    is_limit_animating = enable
    if not enable:
        # 恢复限制标签的原始状态
        limit_label.scale = Vector2(1.0, 1.0)
        limit_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))  # 恢复白色
        
        # 同时恢复cash图标状态
        if cash_icon:
            cash_icon.scale = Vector2(1.0, 1.0)
            cash_icon.rotation_degrees = 0

# 更新UI显示
func update_ui(round_value = null, score_value = null, limit_value = null):
    # 更新阶显示
    if round_value != null:
        round_label.text = round_value
    
    # 更新积分显示
    if score_value != null:
        if score_value_label:
            score_value_label.text = "%d" % score_value
    
    # 更新限制显示
    if limit_value != null:
        limit_label.text = str(limit_value)

# 手动初始化节点引用 - 用于外部设置节点
func init_references(p_round_label, p_limit_label, p_score_label = null, p_cash_icon = null, p_round_icon = null, p_score_coin = null, p_level_label = null, p_level_icon = null,p_progress_bar = null,p_round_title = null,p_level_title = null,p_reduction_hint = null):
    # 手动设置节点引用
    if p_round_label:
        round_label = p_round_label
        # 初始化时将阶数显示改为仅数字
        var text = round_label.text
        if text.begins_with("阶: "):
            round_label.text = text.trim_prefix("阶: ")
    
    if p_limit_label:
        limit_label = p_limit_label
        
    if p_score_label:
        score_value_label = p_score_label
    
    if p_cash_icon:
        cash_icon = p_cash_icon
        # 设置cash图标的缩放中心点
        if cash_icon:
            cash_icon.pivot_offset = cash_icon.size / 2
            
    if p_round_icon:
        round_icon = p_round_icon
        # 设置阶图标的缩放中心点
        if round_icon:
            round_icon.pivot_offset = round_icon.size / 2
    
    # 设置金币引用
    if p_score_coin:
        score_coin = p_score_coin
        # 设置金币的缩放中心点
        score_coin.pivot_offset = score_coin.size / 2
    
    # 设置关卡标签引用
    if p_level_label:
        level_label = p_level_label
    
    # 设置关卡图标引用
    if p_level_icon:
        level_icon = p_level_icon
        # 设置关卡图标的缩放中心点
        if level_icon:
            level_icon.pivot_offset = level_icon.size / 2
    
    if p_progress_bar:
        round_progress_bar = p_progress_bar
        # 连接进度条大小变化信号
        round_progress_bar.resized.connect(_update_progress_bar_pivot)
    
    if p_round_title:
        round_title = p_round_title

    if p_level_title:
        level_title = p_level_title
    
    if p_reduction_hint:
        reduction_hint = p_reduction_hint
    
    _init_ready()

# 脚本初始化 - 在类方法前调用，确保处理功能开启
func _init():
    # 在脚本初始化时启用处理
    set_process(true)

# 设置处理状态
func force_enable_processing():
    if not is_processing():
        set_process(true)
    return is_processing()

# 播放关卡提升动画
func play_level_up_animation(old_level, new_level):
    if not level_icon or not level_label:
        return

    # 播放阶升级开始音效
    # var audio_manager = get_node_or_null("/root/AudioManager")
    # if audio_manager:
    #     audio_manager.play_sfx("res://assert/audio/sfx/successful3.wav")
        
    # 创建关卡升级特效
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 关卡图标动画 - Q弹膨胀并旋转
    tween.tween_property(level_icon, "scale", Vector2(1.8, 1.8), 0.4)
    tween.parallel().tween_property(level_icon, "modulate", Color(2.0, 2.0, 0.5, 1.0), 0.4) # 金黄色强光
    tween.parallel().tween_property(level_icon, "rotation_degrees", 720, 0.8) # 旋转两周
    
    # 关卡数字增加动画
    var label_tween = create_tween()
    label_tween.set_ease(Tween.EASE_OUT)
    label_tween.set_trans(Tween.TRANS_ELASTIC)
    label_tween.tween_method(func(value): level_label.text = str(value), old_level, new_level, 0.6)
    label_tween.parallel().tween_property(level_label, "modulate", Color(1.5, 2.0, 3.0, 1.0), 0.4) # 蓝色高亮
    label_tween.parallel().tween_property(level_label, "scale", Vector2(1.8, 1.8), 0.4)
    
    # 创建升级粒子效果
    AnimationManager.create_level_up_particles(level_label.global_position, Color(0.3, 0.7, 1.0, 1.0))
    
    # 恢复正常
    tween.tween_property(level_icon, "scale", Vector2(1.0, 1.0), 0.5)
    tween.tween_property(level_icon, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.5)
    tween.tween_property(level_icon, "rotation_degrees", 0, 0.01)
    label_tween.tween_property(level_label, "modulate", Color(0.3, 0.7, 1.0, 1.0), 0.5)
    label_tween.tween_property(level_label, "scale", Vector2(1.0, 1.0), 0.5)
    
    return tween

# 关卡变化处理
func _on_level_changed(new_level):
    if level_label:
        # 获取当前关卡数
        var old_level = 1
        if level_label.text.is_valid_int():
            old_level = int(level_label.text)
        
        # 如果关卡数增加，播放升级动画
        if new_level > old_level:
            play_level_up_animation(old_level, new_level)
        else:
            # 直接更新关卡数文本
            level_label.text = str(new_level)
    
    # 关卡变化时重置累积限制积分
    GameManager.accumulated_limit_score = 0
    
    # 更新进度条的关卡总积分
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        total_level_score = game_manager.calc_total_score(new_level)
        current_score = 0
        
        # 设置毛积分（关卡开始时没有扣减，所以毛积分等于净积分）
        gross_score = current_score
        
        current_progress = 0.0
        _update_progress_bar()
        
        # 更新阶进度条的关卡总阶数
        total_level_rounds = game_manager.get_total_rounds_for_level(new_level)
        current_round = 1
        _update_round_progress_bar()

# 更新进度条
func _update_progress_bar():
    if progress_bar:
        # 设置进度条值
        progress_bar.value = current_progress
        
        # 设置进度条文本
        _update_progress_label()
            
        # 根据进度设置进度条颜色
        var progress_style = progress_bar.get_theme_stylebox("fill")
        if not progress_style:
            progress_style = progress_bar.get("theme_override_styles/fill")
            
        if progress_style:
            var color = Color.from_hsv(0.33 * current_progress, 0.8, 0.9) # 从红色渐变到绿色
            progress_style.bg_color = color

# 设置进度条样式和初始值
func _setup_progress_bar():
    # 获取游戏场景根节点
    var parent_scene = get_parent().get_parent()
    
    # 如果已经存在进度条，先移除它
    if progress_bar:
        if progress_bar.get_parent() and progress_bar.get_parent().get_parent():
            progress_bar.get_parent().get_parent().remove_child(progress_bar.get_parent())
        progress_bar = null
    
    # 创建新的进度条
    progress_bar = ProgressBar.new()
    progress_bar.name = "ProgressBar"
    progress_bar.min_value = 0.0
    progress_bar.max_value = 1.0
    progress_bar.value = 0.0
    progress_bar.show_percentage = false # 不显示内置百分比
    
    # 设置进度条大小和位置
    progress_bar.size_flags_horizontal = SIZE_EXPAND_FILL
    progress_bar.size_flags_vertical = SIZE_FILL
    progress_bar.custom_minimum_size = Vector2(0, 20)  # 增加高度为20像素
    
    # 创建进度条容器，用于定位
    var progress_container = Control.new()
    progress_container.name = "ProgressContainer"
    progress_container.set_anchors_preset(Control.PRESET_TOP_WIDE)  # 顶部全宽
    progress_container.offset_top = 0
    progress_container.offset_bottom = 20
    
    # 将进度条锚点设置为填满容器
    progress_bar.set_anchors_preset(Control.PRESET_FULL_RECT)
    progress_bar.offset_top = 0
    progress_bar.offset_left = 0
    progress_bar.offset_right = 0
    progress_bar.offset_bottom = 0
    
    # 设置进度条样式
    var progress_style = StyleBoxFlat.new()
    progress_style.bg_color = Color(0.2, 0.8, 0.2)  # 绿色
    progress_style.corner_radius_top_left = 0
    progress_style.corner_radius_top_right = 0
    progress_style.corner_radius_bottom_left = 5
    progress_style.corner_radius_bottom_right = 5
    progress_bar.add_theme_stylebox_override("fill", progress_style)
    
    var bg_style = StyleBoxFlat.new()
    bg_style.bg_color = Color(0.2, 0.2, 0.2, 0.7)  # 半透明灰色
    bg_style.corner_radius_top_left = 0
    bg_style.corner_radius_top_right = 0
    bg_style.corner_radius_bottom_left = 5
    bg_style.corner_radius_bottom_right = 5
    progress_bar.add_theme_stylebox_override("background", bg_style)
    
    # 添加进度文本
    progress_label = Label.new()
    progress_label.name = "Label"
    progress_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    progress_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
    progress_label.add_theme_font_size_override("font_size", 14)  # 增大字体大小
    progress_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    progress_label.add_theme_constant_override("outline_size", 1)
    progress_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 1))
    
    # 设置文本标签的锚点和边距
    progress_label.set_anchors_preset(Control.PRESET_FULL_RECT)
    progress_label.offset_top = 0
    progress_label.offset_left = 0
    progress_label.offset_right = 0
    progress_label.offset_bottom = 0
    
    progress_bar.add_child(progress_label)
    
    # 将进度条添加到容器
    progress_container.add_child(progress_bar)
    
    # 将容器添加到场景根节点，而不是TopBar
    parent_scene.add_child(progress_container)
    
    # 确保容器在顶部
    progress_container.z_index = 10
    
    # 获取游戏管理器，初始化进度条数据
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 获取当前关卡的总积分值
        total_level_score = game_manager.calc_total_score(game_manager.current_level)
        # 初始净积分为当前总积分
        current_score = game_manager.total_score
        
        gross_score = current_score 
        
        # 初始化进度，使用毛积分计算
        current_progress = float(gross_score) / total_level_score if total_level_score > 0 else 0.0
        # 确保进度不超过1.0
        current_progress = min(current_progress, 1.0)
    
    # 设置初始进度条值
    progress_bar.value = current_progress
    
    # 设置初始进度文本
    _update_progress_label()
    
    # 确保进度条立即可见
    progress_bar.visible = true

# 仅更新进度条文本
func _update_progress_label():
    if progress_label:
        var percent = int(current_progress * 100)
        # 显示毛积分（不含扣减）、总积分和百分比
        progress_label.text = "%d / %d (%d%%)" % [gross_score, total_level_score, percent]
        
        # 添加净积分和累积扣减显示
        progress_label.text += " [净积分: %d - 累计扣减: %d]" % [current_score, GameManager.accumulated_limit_score]

# 设置阶进度条样式和初始值
func _setup_round_progress_bar():
    # 获取游戏场景根节点
    var parent_scene = get_parent().get_parent()
    
    # 如果已经存在阶进度条，先移除它
    # if round_progress_bar:
    #     if round_progress_bar.get_parent() and round_progress_bar.get_parent().get_parent():
    #         round_progress_bar.get_parent().get_parent().remove_child(round_progress_bar.get_parent())
    #     round_progress_bar = null
    
    # 创建新的阶进度条
    # round_progress_bar = ProgressBar.new()
    # round_progress_bar.name = "RoundProgressBar"
    # round_progress_bar.min_value = 1.0
    # round_progress_bar.max_value = 100.0
    # round_progress_bar.value = 0.0
    # round_progress_bar.show_percentage = false # 不显示内置百分比
    
    # # 设置阶进度条大小和位置
    # round_progress_bar.size_flags_horizontal = SIZE_EXPAND_FILL
    # round_progress_bar.size_flags_vertical = SIZE_FILL
    # round_progress_bar.custom_minimum_size = Vector2(0, 20)  # 高度为20像素
    
    # 创建阶进度条容器，用于定位
    # var round_progress_container = Control.new()
    # round_progress_container.name = "RoundProgressContainer"
    # round_progress_container.set_anchors_preset(Control.PRESET_TOP_WIDE)  # 顶部全宽
    # round_progress_container.offset_top = 0
    # round_progress_container.offset_bottom = 20
    
    # 将进度条锚点设置为填满容器
    # round_progress_bar.set_anchors_preset(Control.PRESET_FULL_RECT)
    # round_progress_bar.offset_top = 0
    # round_progress_bar.offset_left = 0
    # round_progress_bar.offset_right = 0
    # round_progress_bar.offset_bottom = 0
    
    # 添加阶进度文本
    round_progress_label = Label.new()
    round_progress_label.name = "RoundLabel"
    round_progress_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    round_progress_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
    round_progress_label.add_theme_font_size_override("font_size", 14)  # 增大字体大小
    round_progress_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    round_progress_label.add_theme_constant_override("outline_size", 1)
    round_progress_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 1))
    
    # 设置文本标签的锚点和边距
    round_progress_label.set_anchors_preset(Control.PRESET_FULL_RECT)
    round_progress_label.offset_top = 0
    round_progress_label.offset_left = 0
    round_progress_label.offset_right = 0
    round_progress_label.offset_bottom = 0
    
    round_progress_bar.add_child(round_progress_label)
    
    # 设置进度条的缩放中心点
    round_progress_bar.pivot_offset = round_progress_bar.size / 2
    
    # 将阶进度条添加到容器
    # round_progress_container.add_child(round_progress_bar)
    
    # 将容器添加到场景根节点
    # parent_scene.add_child(round_progress_container)
    
    # 确保容器在顶部
    # round_progress_container.z_index = 10
    
    # 获取游戏管理器，初始化阶进度条数据
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 获取当前关卡的总阶数
        total_level_rounds = game_manager.get_total_rounds_for_level(game_manager.current_level)
        # 获取当前阶数
        current_round = game_manager.current_round
        
        # 初始化阶进度
        round_progress = float(current_round) / total_level_rounds if total_level_rounds > 0 else 0.0
        # 确保进度不超过1.0
        round_progress = min(round_progress, 1.0)
        round_progress_bar.value = round_progress
    
    # 设置初始阶进度文本
    _update_round_progress_label()
    
    # 确保阶进度条立即可见
    round_progress_bar.visible = true
    
    progress_shader.shader = load("res://shader/progress_shader01.gdshader")
    progress_shader.set_shader_parameter("progress", round_progress)
    progress_shader.set_shader_parameter("u_aspect", round_progress_bar.size.x/round_progress_bar.size.y)
    round_progress_bar.material = progress_shader

# 更新阶进度条
func _update_round_progress_bar():
    if round_progress_bar:
        # 计算新的阶进度
        var new_progress = float(current_round) / total_level_rounds if total_level_rounds > 0 else 0.0
        # 确保进度不超过1.0
        new_progress = min(new_progress, 1.0)
        
        # 获取当前进度作为旧进度
        var old_progress = round_progress
        
        # 更新存储的进度值
        round_progress = new_progress
        
        # 设置阶进度条文本
        _update_round_progress_label()
            
        # 根据进度设置阶进度条颜色
        var progress_style = round_progress_bar.get_theme_stylebox("fill")
        if not progress_style:
            progress_style = round_progress_bar.get("theme_override_styles/fill")
            
        if progress_style:
            var color = Color.from_hsv(0.55 + 0.2 * round_progress, 0.8, 0.9) # 从蓝色渐变到紫色
            progress_style.bg_color = color
        
        # 只在进度发生变化时播放简单的补间动画
        if old_progress != new_progress:
            # 进度值动画
            var tween = create_tween()
            tween.set_ease(Tween.EASE_OUT)
            tween.set_trans(Tween.TRANS_CUBIC)
            tween.tween_property(round_progress_bar, "value", new_progress, 0.3)
            
            # 更新shader参数
            if progress_shader:
                tween.parallel().tween_method(
                    func(prog): progress_shader.set_shader_parameter("progress", prog),
                    old_progress,
                    new_progress,
                    0.3
                )
            
            # 添加放大凸效果动画
            var scale_tween = create_tween()
            scale_tween.set_ease(Tween.EASE_OUT)
            scale_tween.set_trans(Tween.TRANS_ELASTIC)
            # 先快速放大
            scale_tween.tween_property(round_progress_bar, "scale", Vector2(1.1, 1.15), 0.15)
            # 然后弹性恢复
            scale_tween.tween_property(round_progress_bar, "scale", Vector2(1.0, 1.0), 0.3)

# 更新阶进度条文本
func _update_round_progress_label():
    if round_progress_label:
        current_round = min(current_round,total_level_rounds)
        # var percent = int(float(current_round) / total_level_rounds * 100) if total_level_rounds > 0 else 0
        round_progress_label.text = tr("RoundTitle") + ": %d / %d" % [current_round, total_level_rounds]

# 处理游戏状态变化
func _on_game_state_changed(new_state):
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
    
    # 根据游戏状态执行操作
    match new_state:
        game_manager.GameState.PREPARATION:
            # 确保进度条可见（如果重新开始游戏）
            # if progress_bar == null:
                # _setup_progress_bar()
            
            # 确保阶进度条可见
            if round_progress_bar == null:
                _setup_round_progress_bar()

# 显示扣减提示
func show_reduction_hint(amount: int):
    if amount <= 0:
        reduction_hint.visible = false
        return
        
    reduction_hint.text = "(-%d)" % amount
    reduction_hint.visible = true
    
    # 创建一个简单的出现动画
    reduction_hint.modulate.a = 0
    var tween = create_tween()
    tween.tween_property(reduction_hint, "modulate:a", 1.0, 0.2)

# 隐藏扣减提示
func hide_reduction_hint():
    reduction_hint.visible = false

# 更新进度条缩放中心点
func _update_progress_bar_pivot():
    if round_progress_bar:
        round_progress_bar.pivot_offset = round_progress_bar.size / 2

# 初始化
func _ready():
    # 连接进度条大小变化信号
    if round_progress_bar:
        round_progress_bar.resized.connect(_update_progress_bar_pivot)
    
    _init_ready()

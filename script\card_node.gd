extends TextureRect

# 卡牌节点 - 处理单个卡牌的显示和交互逻辑

# 拖动相关变量
var is_dragging = false
var can_drag = true
var drag_offset = Vector2.ZERO  # 拖动时鼠标相对于卡牌的偏移

# 卡牌数据
var card_data = null
var original_position = Vector2.ZERO
var is_in_hand = true
var card_index = -1
var has_buff = false
var buff_label = null
var buff_effect = null
var buff_ray_effect = null  # 新增的光芒特效节点
var rounds_label = null     # 新增的阶数标签
var is_disabled = false     # 新增的禁用状态标记

# 动画效果相关变量
var is_hovered = false
var hover_scale = 1.15  # 悬停时的放大比例
var original_scale = Vector2(1.0, 1.0)
var target_scale = Vector2(1.0, 1.0)
var scale_speed = 5.0  # 调整后的缩放速度
var buff_shader_material = null  # BUFF牌效果
var buff_shader_rect = null
var ray_material = null  # 光芒特效材质
var is_front_card = false  # 是否是前三张卡牌

# 信号
signal card_drag_started(card_node)
signal card_drag_ended(card_node, drop_position)
signal card_clicked(card_node)
signal card_right_clicked(card_node)

# 漂浮效果相关变量
var float_time = 0.0
var float_amplitude = 3.0

# 溶解效果相关变量
var is_dissolving = false
var dissolve_progress = 1.0
var dissolve_speed = 2.0  # 溶解速度，值越大溶解越快

var base_card_width = 80
var base_card_height = 120
var rounds_label_font_size = 22

var is_buff_diplay_area = false   

# 移除 BuffOverlay 引用
# @onready var buff_overlay: TextureRect = $BuffOverlay 
@onready var noise_texture = preload("res://noise/fast_noise_lite.tres") # 噪声纹理可能还用于其他效果，保留
@onready var static_shadow = $StaticShadow

# 获取主材质的引用
var main_material: ShaderMaterial = null
var shadow_material: ShaderMaterial = null  # 新增：阴影材质引用

# 将 BUFF 描述文本格式化为汉字和数字分行显示
func format_buff_text(text: String) -> String:
    var result = ""
    var chinese_part = ""
    var number_part = ""
    var in_number = false
    
    # 遍历文本中的每个字符
    for i in range(text.length()):
        var ch = text[i]
        
        # 判断字符是否为数字 (也考虑 +- 符号)
        if ch.is_valid_int() or ch == "+" or ch == "×" or ch == "%" or ch == "-":
            # 如果开始出现数字，且之前有汉字，先添加汉字部分
            if not in_number and chinese_part != "":
                result += chinese_part + "\n"
                chinese_part = ""
            
            number_part += ch
            in_number = true
        else:
            # 如果开始出现汉字，且之前有数字，先添加数字部分
            if in_number and number_part != "":
                result += number_part + "\n"
                number_part = ""
            
            chinese_part += ch
            in_number = false
    
    # 添加最后一部分
    if in_number and number_part != "":
        result += number_part
    elif chinese_part != "":
        result += chinese_part
    
    return result.strip_edges() # 移除开头和结尾多余的换行符

# 设置浮动状态的阴影参数
func set_floating_shadow_params(time_offset: float):
    if static_shadow and shadow_material:
        static_shadow.visible = true
        shadow_material.set_shader_parameter("enable_float_effect", true)
        shadow_material.set_shader_parameter("shadow_offset", Vector2(10, 10))
        shadow_material.set_shader_parameter("time_offset", time_offset)
        shadow_material.set_shader_parameter("shadow_color", Color(0.0, 0.0, 0.0, 0.5))

# 设置非浮动状态的阴影参数
func set_static_shadow_params():
    if static_shadow and shadow_material:
        static_shadow.visible = true
        shadow_material.set_shader_parameter("enable_float_effect", false)
        shadow_material.set_shader_parameter("shadow_offset", Vector2(2, 2))
        shadow_material.set_shader_parameter("shadow_color", Color(0.0, 0.0, 0.0, 0.7))

func set_mouse_hover_shadow_params():
    if static_shadow and shadow_material:
        static_shadow.visible = true
        shadow_material.set_shader_parameter("enable_float_effect", false)
        shadow_material.set_shader_parameter("shadow_offset", Vector2(20, 30))
        shadow_material.set_shader_parameter("shadow_color", Color(0.0, 0.0, 0.0, 0.4))

# 应用浮动效果
func apply_float_effect():
    if not main_material:
        return # 没有材质无法应用
        
    if is_in_hand:
        # --- 启用浮动 --- 
        var time_offset = randf() * 10.0
        # 显示阴影并设置浮动参数
        set_floating_shadow_params(time_offset)
            
        # 应用浮动效果shader
        main_material.set_shader_parameter("enable_float_effect", true)
        main_material.set_shader_parameter("float_amplitude", float_amplitude)
        main_material.set_shader_parameter("float_frequency", 1.0)
        main_material.set_shader_parameter("time_offset", time_offset)  # 随机时间偏移，使卡牌不同步浮动
    
        # 禁用其他效果 (保留 enable_buff_overlay 的状态)
        main_material.set_shader_parameter("enable_hover_effect", false)
        main_material.set_shader_parameter("enable_dissolve_effect", false)
        main_material.set_shader_parameter("enable_ripple_effect", false)
        main_material.set_shader_parameter("enable_gold_flash_effect", false)
    
        # --- 检查并保持 BUFF 效果 --- 
        if has_buff:
            main_material.set_shader_parameter("enable_buff_overlay", true)
        else:
            main_material.set_shader_parameter("enable_buff_overlay", false)
    else:
        # --- 禁用浮动 --- 
        main_material.set_shader_parameter("enable_float_effect", false)
        # 设置静态阴影参数
        set_static_shadow_params()

# 初始化
func _ready():
    # 设置纹理矩形的大小
    custom_minimum_size = Vector2(base_card_width, base_card_height)
    size = custom_minimum_size
    
    # 确保旋转中心点设置在卡牌中心
    pivot_offset = Vector2(custom_minimum_size.x / 2, custom_minimum_size.y / 2)
    
    # 获取主材质并确保其唯一性
    if material is ShaderMaterial:
        # 创建材质副本以确保每个实例有独立状态
        main_material = material.duplicate() 
        self.material = main_material # 将节点材质也设置为这个独立副本
    elif material == null:
         print("警告: CardNode 没有设置 Material 资源!")
    else:
        print("警告: CardNode 的 Material 不是 ShaderMaterial!")
        main_material = null # 确保 main_material 在无效情况下为 null

    # 为StaticShadow创建独立的材质实例
    if static_shadow and static_shadow.material is ShaderMaterial:
        shadow_material = static_shadow.material.duplicate()
        static_shadow.material = shadow_material
        # 设置初始大小和偏移（确保在设置custom_minimum_size之后）
        static_shadow.custom_minimum_size = custom_minimum_size * 0.98
        static_shadow.size = custom_minimum_size * 0.98
        static_shadow.pivot_offset = static_shadow.size / 2
        # 设置初始阴影参数
        # shadow_material.set_shader_parameter("time_offset", randf() * 10.0)  # 随机时间偏移
    
    # 连接信号
    gui_input.connect(_on_gui_input)
    mouse_entered.connect(_on_mouse_entered)
    mouse_exited.connect(_on_mouse_exited)
    
    # 连接窗口大小变化信号
    get_tree().root.size_changed.connect(_on_window_size_changed)
    
    # 初始调整卡牌大小
    _adjust_card_size()
    
    # 创建卡牌阴影
    # card_shadow = Panel.new() # 移除此行
    # card_shadow.modulate = Color(1.0, 1.0, 1.0, shadow_opacity) # 移除此行
    # card_shadow.size = custom_minimum_size * 0.98  # 阴影比卡牌稍小 # 移除此行
    # card_shadow.position = shadow_offset # 移除此行
    # card_shadow.mouse_filter = Control.MOUSE_FILTER_IGNORE # 移除此行
    # card_shadow.show_behind_parent = true # 移除此行
    # card_shadow.z_index = 0 # 移除此行

    # 设置阴影样式 # 移除此行
    # var panel_style = StyleBoxFlat.new() # 移除此行
    # panel_style.bg_color = Color(0.25, 0.25, 0.25, 1.0) # 移除此行
    # 设置圆角半径 # 移除此行
    # var corner_radius = int(custom_minimum_size.x * 0.1) # 移除此行
    # panel_style.corner_radius_top_left = corner_radius # 移除此行
    # panel_style.corner_radius_top_right = corner_radius # 移除此行
    # panel_style.corner_radius_bottom_left = corner_radius # 移除此行
    # panel_style.corner_radius_bottom_right = corner_radius # 移除此行
    # 添加阴影效果 # 移除此行
    # panel_style.shadow_color = Color(0, 0, 0, 0.25)  # 调整阴影颜色 # 移除此行
    # panel_style.shadow_size = 0  # 减小基础阴影大小 # 移除此行
    # panel_style.shadow_offset = Vector2(2, 2) # 移除此行
    # card_shadow.add_theme_stylebox_override("panel", panel_style) # 移除此行

    # 将阴影添加到卡牌节点 # 移除此行
    # add_child(card_shadow) # 移除此行
    
    # 创建BUFF标签
    buff_label = Label.new()
    buff_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    buff_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
    #buff_label.position = Vector2(0, size.y / 3)
    buff_label.size = Vector2(size.x, size.y) # 增加高度以支持多行
    buff_label.add_theme_font_size_override("font_size", 20)
    # 默认不使用描边
    buff_label.add_theme_constant_override("outline_size", 3)
    buff_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART # 启用自动换行
    buff_label.visible = false
    add_child(buff_label)
    
    # 创建阶数标签
    rounds_label = Label.new()
    rounds_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
    rounds_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
    rounds_label.position = Vector2(size.x * 0.6, 0)
    rounds_label.add_theme_font_size_override("font_size", rounds_label_font_size)
    rounds_label.add_theme_color_override("font_color", Color(1.0, 0.9, 0.2))
    rounds_label.add_theme_constant_override("outline_size", 4)
    rounds_label.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0))
    rounds_label.visible = false
    rounds_label.scale = Vector2(1.0, 1.0)  # 设置初始缩放
    rounds_label.pivot_offset = rounds_label.size / 2  # 设置旋转和缩放中心点
    add_child(rounds_label)
    
    # 创建BUFF特效
    buff_effect = ColorRect.new()
    buff_effect.color = Color(1, 1, 1, 0.3)
    buff_effect.size = custom_minimum_size + Vector2(10, 0)
    buff_effect.position = Vector2(-5, 0)
    buff_effect.visible = false
    buff_effect.pivot_offset = buff_effect.size / 2
    buff_effect.mouse_filter = Control.MOUSE_FILTER_IGNORE  # 确保不拦截鼠标事件
    buff_effect.show_behind_parent = false
    add_child(buff_effect)
    
    # 创建光芒特效
    buff_ray_effect = ColorRect.new()
    buff_ray_effect.size = custom_minimum_size
    buff_ray_effect.position = Vector2(0, 0)
    buff_ray_effect.visible = false
    buff_ray_effect.mouse_filter = Control.MOUSE_FILTER_IGNORE  # 确保不拦截鼠标事件
    buff_ray_effect.show_behind_parent = false
    add_child(buff_ray_effect)

    # 光芒特效材质
    ray_material = ShaderMaterial.new()
    ray_material.shader = load("res://shader/card_effect4.gdshader")
    ray_material.set_shader_parameter("seed", randf() * 10.0)
    
    # 确保 BUFF Overlay 效果在开始时是禁用的
    if main_material:
        main_material.set_shader_parameter("enable_buff_overlay", false)

    # 连接BuffManager的信号
    var buff_manager = get_node("/root/BuffManager")
    if buff_manager:
        buff_manager.buff_rounds_updated.connect(_on_buff_rounds_updated)
        buff_manager.buff_expired.connect(_on_buff_expired)
    
    # 静态阴影材质的参数
    if shadow_material:
        shadow_material.set_shader_parameter("shadow_offset", Vector2(10, 10))
        # shadow_material.set_shader_parameter("time_offset", randf() * 10.0)

# 更新卡牌是否在手牌的状态
func update_hand_status(in_hand: bool):
    is_in_hand = in_hand
    # 应用或移除浮动效果
    apply_float_effect()
    
    # 如果不在手牌，也确保禁用悬停效果 (以防万一)
    if not is_in_hand and main_material:
        main_material.set_shader_parameter("enable_hover_effect", false)
        main_material.set_shader_parameter("is_hovered", false)

# 设置卡牌数据
func set_card_data(data, in_hand: bool = true, index: int = -1):
    card_data = data
    card_index = index
    
    if card_data:
        # 加载卡牌纹理
        texture = load(card_data.path)
        
        # 处理 BUFF 显示
        if "buff" in card_data and main_material:
            has_buff = true
            var buff_info = card_data.buff
            var buff_type = buff_info.type
            var buff_color_enum = buff_info.color 
            var buff_description = buff_info.description

            # --- 设置主材质的 BUFF Overlay 参数 --- 
            var texture_path = BuffManager.get_buff_texture_path(buff_type)
            var color = BuffManager.get_buff_visual_color(buff_color_enum)
            var buff_tex = load(texture_path)
            
            if buff_tex:
                main_material.set_shader_parameter("buff_texture", buff_tex)
                main_material.set_shader_parameter("buff_color", color)
                main_material.set_shader_parameter("buff_tile_scale", 1.0) # 设置平铺密度
                main_material.set_shader_parameter("buff_noise", noise_texture)
                main_material.set_shader_parameter("buff_noise_influence", 0.1)
                main_material.set_shader_parameter("enable_buff_overlay", true) # 启用效果
            else:
                print("警告: BUFF 贴图加载失败: ", texture_path)
                main_material.set_shader_parameter("enable_buff_overlay", false) # 确保禁用
            
            # --- BUFF Label (文字) - 汉字数字分行，颜色更明显 ---
            if buff_label:
                buff_label.text = format_buff_text(buff_description)
                # 设置文本颜色为BUFF颜色，并加深使其更明显
                var text_color = color
                text_color.a = 1.0  # 确保完全不透明
                # 使颜色更鲜艳
                text_color = text_color.lightened(-0.2)  # 加深颜色
                
                buff_label.add_theme_color_override("font_color", text_color)
                # 移除外边框颜色
                buff_label.remove_theme_color_override("font_outline_color")
                # 移除描边
                buff_label.add_theme_constant_override("outline_size", 3)
                
                buff_label.visible = true
                # 调整 buff_label 高度以容纳多行文本
                # buff_label.size.y = 60 # 增加高度
            
            # --- 显示阶数 (如果有) ---
            update_rounds_display()
        else:
            has_buff = false
            # 禁用主材质的 BUFF Overlay 效果
            if main_material:
                main_material.set_shader_parameter("enable_buff_overlay", false)
            
            # 隐藏 BUFF Label
            if buff_label:
                buff_label.remove_theme_color_override("font_color")
                buff_label.remove_theme_color_override("font_outline_color")
                buff_label.visible = false
            
            # 隐藏阶数标签
            if rounds_label:
                rounds_label.visible = false
            
        # 使用新函数设置初始手牌状态
        update_hand_status(in_hand)
    else:
        # --- 无卡牌数据 (卡背) ---
        texture = load("res://assert/cards/cardback02.png")
        has_buff = false
        # 禁用主材质的 BUFF Overlay 效果
        if main_material:
            main_material.set_shader_parameter("enable_buff_overlay", false)
            
        # 隐藏 BUFF Label
        if buff_label:
            buff_label.remove_theme_color_override("font_color")
            buff_label.remove_theme_color_override("font_outline_color")
            buff_label.visible = false

# 处理输入事件
func _on_gui_input(event):
    # 处理右键点击事件 - 添加右键点击卡牌详情
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_RIGHT and event.pressed:
        print("[CardNode] Right-click detected on: ")
        # 发出右键点击信号
        emit_signal("card_right_clicked", self)
        # 右键点击后不进行其他操作
        return
    
    # 如果不能拖动，则忽略后续的拖动事件
    if not can_drag:
        return
    
    # 处理鼠标按下事件 (左键)
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT:
        if event.pressed:
            # 开始拖动
            _start_drag()
            # 记录鼠标相对于卡牌的偏移
            drag_offset = get_global_mouse_position() - global_position
        elif is_dragging:
            # 结束拖动
            _end_drag(get_global_mouse_position())
            
            # 如果是点击而不是拖动，则发出点击信号
            if position.distance_to(original_position) < 10:
                card_clicked.emit(self)
    
    # 处理鼠标移动事件 (仅在拖动时)
    if event is InputEventMouseMotion and is_dragging:
        # 直接设置卡牌位置为鼠标位置减去偏移
        global_position = get_global_mouse_position() - drag_offset

# 鼠标进入事件处理
func _on_mouse_entered():
    is_hovered = true
    
    AudioManager.play_sfx("res://assert/audio/sfx/card_pop01.mp3")
    
    # 保存原始z_index并设置为较高值
    set_meta("original_z_index", z_index)
    z_index = 60
    
    # 显示 BUFF 文字
    if has_buff and buff_label:
        buff_label.visible = true
        if is_buff_diplay_area:
            buff_label.position.x = -size.x
        else:
            buff_label.position.y = -size.y/2
        
        # 使阶数标签在卡牌悬停时暂时隐藏，避免与BUFF文字重叠
        if rounds_label and rounds_label.visible:
            rounds_label.modulate.a = 0.0
    
    # 设置悬停缩放目标
    target_scale = Vector2(hover_scale, hover_scale)
    
    # 更新主材质的悬停 shader 参数
    if main_material:
        main_material.set_shader_parameter("is_hovered", true)
        main_material.set_shader_parameter("enable_hover_effect", true)
        # 禁用其他互斥效果
        main_material.set_shader_parameter("enable_float_effect", false)
        # main_material.set_shader_parameter("enable_buff_effect", false) # 不应禁用 buff overlay
        main_material.set_shader_parameter("enable_dissolve_effect", false)
        main_material.set_shader_parameter("enable_ripple_effect", false)
        main_material.set_shader_parameter("enable_gold_flash_effect", false)
    
    set_mouse_hover_shadow_params()
        

# 鼠标离开事件处理
func _on_mouse_exited():
    if is_dissolving:
        return
    is_hovered = false
    
    # 恢复原始z_index值
    if has_meta("original_z_index"):
        z_index = get_meta("original_z_index")
        remove_meta("original_z_index")
    
    # 隐藏 BUFF 文字
    if has_buff and buff_label:
        if is_buff_diplay_area:
            buff_label.position.x = 0
        else:
            buff_label.position.y = 0
        
        # 恢复阶数标签的显示
        if rounds_label and card_data and "buff" in card_data:
            var buff_manager = get_node("/root/BuffManager")
            if buff_manager and buff_manager.get_rounds_text(card_data.buff).length() > 0:
                rounds_label.modulate.a = 1.0
    
    # 设置非悬停缩放目标
    target_scale = original_scale # 恢复原始大小
    if is_front_card: # 如果是前三张牌，目标是 1.1
        target_scale = Vector2(1.1, 1.1)
    
    # 更新主材质的 shader 参数
    if main_material:
        main_material.set_shader_parameter("is_hovered", false)
        main_material.set_shader_parameter("enable_hover_effect", false)
        
        # 根据状态恢复其他效果
        apply_float_effect() # 这会设置 enable_float_effect 和 enable_buff_overlay
        if has_buff: # 如果不在手牌但有 BUFF
             # 需要确保 buff overlay 仍然启用，其他禁用
             main_material.set_shader_parameter("enable_dissolve_effect", false)
             main_material.set_shader_parameter("enable_ripple_effect", false)
             main_material.set_shader_parameter("enable_gold_flash_effect", false)
             main_material.set_shader_parameter("enable_buff_overlay", true)
        else: # 不在手牌也没 BUFF
             main_material.set_shader_parameter("enable_buff_overlay", false)
             # ... 可能需要禁用其他所有效果 ...

# 重置位置
func reset_position():
    position = original_position
    
# 每帧更新
func _process(delta):
    # 更新漂浮计时
    float_time += delta * 1.0  # 漂浮频率
    
    # 处理缩放动画
    if scale != target_scale:
        var t = 1.0 - pow(1.0 - (delta * 7), 7)  # 使用固定delta乘数
        scale = scale.lerp(target_scale, t)
        
        # 同步更新shader参数
        if material:
            material.set_shader_parameter("current_scale", scale.x)
    
    # 如果正在拖动，确保保持放大效果，但不应用其他效果
    if is_dragging:
        # 确保拖动时保持放大状态
        if scale != Vector2(hover_scale, hover_scale):
            scale = Vector2(hover_scale, hover_scale)
            if material:
                material.set_shader_parameter("current_scale", hover_scale)
                material.set_shader_parameter("is_hovered", true)
                # 确保启用悬停效果
                material.set_shader_parameter("enable_hover_effect", true)
        return
    
    # 处理溶解动画
    if is_dissolving:
        # 逐渐减小溶解进度值（从1到0）
        dissolve_progress -= delta * dissolve_speed
        
        # 确保溶解进度不会小于0
        if dissolve_progress <= 0.0:
            dissolve_progress = 0.0
            is_dissolving = false
            # 溶解完成后发出信号
            dissolve_completed.emit()
            # print("溶解完成")
        
        # 更新shader参数
        if material:
            material.set_shader_parameter("dissolve_progress", dissolve_progress)
            # 确保溶解效果始终启用
            material.set_shader_parameter("enable_dissolve_effect", true)
            # print("更新溶解进度：", dissolve_progress)
            
    # 根据漂浮效果更新阴影
    # if card_shadow && material && material.get_shader_parameter("enable_float_effect"): # 移除此行
    #     # 计算当前浮动Y偏移 # 移除此行
    #     var current_offset = sin(float_time + material.get_shader_parameter("time_offset")) * float_amplitude # 移除此行
        
    #     # 阴影位置跟随卡牌漂浮 # 移除此行
    #     var shadow_dynamic_offset = (current_offset * 0.35)  # 减小阴影移动幅度 # 移除此行
        
    #     # 计算水平偏移，使阴影移动更自然 # 移除此行
    #     var horizontal_offset = shadow_dynamic_offset * 0.8  # 水平移动幅度为垂直的0.8倍 # 移除此行
        
    #     # 更新阴影位置 - 添加水平和垂直移动 # 移除此行
    #     card_shadow.position = Vector2( # 移除此行
    #         shadow_offset.x + 10, # 移除此行
    #         shadow_offset.y + 10 + shadow_dynamic_offset # 移除此行
    #     ) # 移除此行
        
    #     # 更新阴影透明度 - 上浮时变淡 # 移除此行
    #     var shadow_base_opacity = shadow_opacity # 移除此行
    #     var opacity_variation = 0.3  # 透明度变化范围 # 移除此行
    #     var normalized_offset = (current_offset / float_amplitude + 1.0) / 2.0 # 移除此行
    #     var dynamic_opacity = shadow_base_opacity * (1.0 - normalized_offset * opacity_variation)  # 上浮时变淡 # 移除此行
    #     card_shadow.modulate.a = dynamic_opacity # 移除此行
        
    #     # 更新阴影大小 - 上浮时变大 # 移除此行
    #     var base_scale = 0.95  # 基础缩放 # 移除此行
    #     var size_variation = 0.05  # 大小变化范围 # 移除此行
    #     var size_factor = base_scale + normalized_offset * size_variation  # 上浮时变大 # 移除此行
    #     card_shadow.size = custom_minimum_size * size_factor # 移除此行
        
    #     # 更新样式 # 移除此行
    #     var panel_style = card_shadow.get_theme_stylebox("panel") as StyleBoxFlat # 移除此行
    #     if panel_style: # 移除此行
    #         # 更新圆角 # 移除此行
    #         var corner_radius = int(custom_minimum_size.x * 0.1) # 移除此行
    #         panel_style.corner_radius_top_left = corner_radius # 移除此行
    #         panel_style.corner_radius_top_right = corner_radius # 移除此行
    #         panel_style.corner_radius_bottom_left = corner_radius # 移除此行
    #         panel_style.corner_radius_bottom_right = corner_radius # 移除此行
            
    #         # 动态调整阴影 # 移除此行
    #         # var base_shadow_size = 3 # 移除此行
    #         # var shadow_size_variation = 2 # 移除此行
    #         # panel_style.shadow_size = int(base_shadow_size + normalized_offset * shadow_size_variation) # 移除此行
            
    #         # 动态调整阴影偏移 # 移除此行
    #         # var base_shadow_offset = 2 # 移除此行
    #         # var offset_variation = 3 # 移除此行
    #         # var dynamic_offset = base_shadow_offset + normalized_offset * offset_variation # 移除此行
    #         # panel_style.shadow_offset = Vector2(dynamic_offset * 0.8, dynamic_offset)  # 水平偏移稍小 # 移除此行
            
    #         # 动态调整阴影颜色 # 移除此行
    #         # var shadow_alpha = 0.25 - normalized_offset * 0.1  # 上浮时阴影更淡 # 移除此行
    #         # panel_style.shadow_color = Color(0, 0, 0, shadow_alpha) # 移除此行
            
    #         card_shadow.add_theme_stylebox_override("panel", panel_style) # 移除此行

# 设置是否可拖动
func set_draggable(draggable: bool):
    can_drag = draggable
    
# 高亮显示
func highlight(enable: bool = true, use_gold_effect: bool = false):
    if not main_material: return # 必须有主材质

    if enable:
        if use_gold_effect:
            # 应用金色闪动效果 (操作 main_material)
            main_material.set_shader_parameter("enable_gold_flash_effect", true)
            # 禁用其他互斥效果 (保留 BUFF)
            main_material.set_shader_parameter("enable_float_effect", false)
            main_material.set_shader_parameter("enable_hover_effect", false)
            main_material.set_shader_parameter("enable_dissolve_effect", false)
            main_material.set_shader_parameter("enable_ripple_effect", false)
            self.modulate = Color(1.0, 1.0, 1.0, 1.0) 
        else:
            # 使用普通高亮 (通过 modulate)
            self.modulate = Color(1.2, 1.2, 1.2, 1.0)
            # 同时确保禁用主材质的金色效果
            main_material.set_shader_parameter("enable_gold_flash_effect", false)
                    
    else:
        # 取消高亮
        self.modulate = Color(1.0, 1.0, 1.0, 1.0)
        # 禁用金色效果
        main_material.set_shader_parameter("enable_gold_flash_effect", false)
        # 根据状态恢复效果
        if is_in_hand:
            apply_float_effect() # 恢复浮动和可能的 BUFF
        elif has_buff:
            # 恢复 BUFF 显示，禁用其他
            main_material.set_shader_parameter("enable_buff_overlay", true)
            main_material.set_shader_parameter("enable_float_effect", false)
            # ... 禁用其他效果 ...
        else:
            # 禁用所有效果
            main_material.set_shader_parameter("enable_buff_overlay", false)
            main_material.set_shader_parameter("enable_float_effect", false)
            # ... 禁用其他效果 ...
        
# 设置为前三张卡牌
func set_as_front_card(enable: bool = true):
    is_front_card = enable
    
    if enable:
        # 前三张卡牌稍微放大
        target_scale = Vector2(1.1, 1.1)
    else:
        # 恢复正常大小
        target_scale = Vector2(0.9, 0.9)
    
    # 如果当前被悬停，则保持悬停效果
    if is_hovered:
        target_scale = Vector2(hover_scale, hover_scale)

# 设置大小
func set_card_size(size_multiplier: float = 1.0):
    custom_minimum_size = Vector2(base_card_width, base_card_height) * size_multiplier
    size = custom_minimum_size
    
    # 更新BUFF标签位置和大小
    if buff_label:
        buff_label.size = Vector2(size.x, size.y)
        #buff_label.position = Vector2(0, size.y / 3)
    
    # 更新BUFF特效大小
    if buff_effect:
        buff_effect.size = custom_minimum_size
    
    # 更新光芒特效大小
    if buff_ray_effect:
        buff_ray_effect.size = custom_minimum_size

    if rounds_label:
        rounds_label.position = Vector2(-custom_minimum_size.x + 120, 0)
        rounds_label.add_theme_font_size_override("font_size", int(24 * custom_minimum_size.x / base_card_width))

# 设置BUFF显示 (旧逻辑，可能需要重构)
func set_buff_display(buff_data):
    if not buff_label: return # 至少需要标签
    if not main_material: 
        print("警告: set_buff_display 调用时 main_material 为 null")
        return
    
    # 显示BUFF描述 - 汉字数字分行
    buff_label.text = format_buff_text(buff_data.description)
    buff_label.visible = true
    #buff_label.size.y = 60 # 调整高度以容纳多行文本
    
    # 获取颜色
    var buff_manager = get_node("/root/BuffManager")
    var color = Color.WHITE # 默认
    if buff_manager:
        color = buff_manager.get_buff_visual_color(buff_data.color) # 使用视觉颜色函数
        
    # 设置文本颜色，使其更明显
    var text_color = color
    text_color.a = 1.0  # 确保完全不透明
    # 使颜色更鲜艳
    text_color = text_color.lightened(-0.2)  # 加深颜色
    
    buff_label.add_theme_color_override("font_color", text_color)
    # 移除外边框颜色
    buff_label.remove_theme_color_override("font_outline_color")
    # 移除描边
    buff_label.add_theme_constant_override("outline_size", 3)
    
    # --- 更新主材质以显示 BUFF --- 
    has_buff = true # 标记有 BUFF
    var texture_path = BuffManager.get_buff_texture_path(buff_data.type)
    var buff_tex = load(texture_path)
    
    if buff_tex:
        main_material.set_shader_parameter("buff_texture", buff_tex)
        main_material.set_shader_parameter("buff_color", color)
        main_material.set_shader_parameter("buff_tile_scale", 1.0)
        main_material.set_shader_parameter("buff_noise", noise_texture)
        main_material.set_shader_parameter("buff_noise_influence", 0.1)
        main_material.set_shader_parameter("enable_buff_overlay", true)
    else:
        print("警告: BUFF 贴图加载失败 (set_buff_display): ", texture_path)
        main_material.set_shader_parameter("enable_buff_overlay", false)
        
    # 禁用其他可能冲突的效果
    main_material.set_shader_parameter("enable_float_effect", false)
    main_material.set_shader_parameter("enable_hover_effect", false)
    main_material.set_shader_parameter("enable_dissolve_effect", false)
    main_material.set_shader_parameter("enable_ripple_effect", false)
    main_material.set_shader_parameter("enable_gold_flash_effect", false)
    
    # 旧的 buff_effect 和 buff_ray_effect 相关代码应移除或重构
    # buff_effect.visible = true
    # buff_ray_effect.visible = true
        
    # 如果是手牌，理论上不应直接调用此函数，应由 set_card_data 处理
    # if is_in_hand:
    #     call_deferred("apply_float_effect")

# 隐藏BUFF显示 (旧逻辑，可能需要重构)
func hide_buff_display():
    if buff_label:
        buff_label.visible = false
        buff_label.remove_theme_color_override("font_color")
        buff_label.remove_theme_color_override("font_outline_color")
        
    has_buff = false # 标记无 BUFF
    # 禁用主材质的 BUFF 效果
    if main_material:
        main_material.set_shader_parameter("enable_buff_overlay", false)
    
    # 旧的 buff_effect 和 buff_ray_effect 相关代码应移除
    # if buff_effect: buff_effect.visible = false
    # if buff_ray_effect: buff_ray_effect.visible = false
        
    # 根据状态恢复效果
    if is_in_hand:
        apply_float_effect()
    # else: # 如果不在手牌，可能需要设置默认无效果状态
    #    if main_material:
    #       main_material.set_shader_parameter("enable_float_effect", false)
        
# 应用BUFF shader特效 (旧逻辑，应集成到 set_card_data 或 set_buff_display)
func apply_buff_shader(color_name):
    # 建议重构：此函数逻辑应合并到 set_card_data 或 set_buff_display
    if not main_material: return
    var buff_manager = get_node("/root/BuffManager")
    if buff_manager:
        var color = buff_manager.get_buff_visual_color(color_name)
        # 此处假设 BUFF 纹理等已通过其他方式设置
        main_material.set_shader_parameter("buff_color", color)
        main_material.set_shader_parameter("enable_buff_overlay", true)
        # 禁用其他效果
        main_material.set_shader_parameter("enable_float_effect", false)
        main_material.set_shader_parameter("enable_hover_effect", false)
        # ... 禁用其他 ...

# 设置为激活的BUFF卡牌显示
func set_as_active_buff_card():
    # ... (前面设置大小和交互的代码) ...
    
    # 强制显示BUFF效果 (操作 main_material)
    if card_data and "buff" in card_data and main_material:
        has_buff = true
        var buff_info = card_data.buff
        var buff_type = buff_info.type
        var buff_color_enum = buff_info.color
        var buff_description = buff_info.description
        var texture_path = BuffManager.get_buff_texture_path(buff_type)
        var color = BuffManager.get_buff_visual_color(buff_color_enum)
        var buff_tex = load(texture_path)
        
        if buff_tex:
            main_material.set_shader_parameter("buff_texture", buff_tex)
            main_material.set_shader_parameter("buff_color", color)
            main_material.set_shader_parameter("buff_tile_scale", 1.0) # 激活时可以稍微密集些
            main_material.set_shader_parameter("buff_noise", noise_texture)
            main_material.set_shader_parameter("buff_noise_influence", 0.15) # 激活时噪声强一点
            main_material.set_shader_parameter("enable_buff_overlay", true)
        else:
             main_material.set_shader_parameter("enable_buff_overlay", false)
             
        # 禁用其他效果
        main_material.set_shader_parameter("enable_float_effect", false)
        main_material.set_shader_parameter("enable_hover_effect", false)
        # ... 禁用其他 ...
            
        # 确保BUFF说明文本可见和颜色正确
        if buff_label:
            buff_label.text = format_buff_text(buff_description)
            buff_label.visible = true
            buff_label.show_behind_parent = false
            #buff_label.position = Vector2(0, size.y /3)
            
            # 设置文本颜色，使其更明显
            var text_color = color
            text_color.a = 1.0  # 确保完全不透明
            # 使颜色更鲜艳
            text_color = text_color.lightened(-0.2)  # 加深颜色
            
            buff_label.add_theme_color_override("font_color", text_color)
            # 移除外边框颜色
            buff_label.remove_theme_color_override("font_outline_color")
            # 移除描边
            buff_label.add_theme_constant_override("outline_size", 3)
        
    # ... (设置交互的代码) ...

# 启用溶解效果
func enable_dissolve_effect():
    if main_material:
        # 设置溶解纹理
        var dissolve_texture = preload("res://noise/card_node.tres")
        main_material.set_shader_parameter("dissolve_noise_texture", dissolve_texture)
        main_material.set_shader_parameter("dissolve_progress", 1.0)  # 从1开始，减小到0
        
        # 获取当前阴影颜色
        var current_shadow_color = shadow_material.get_shader_parameter("shadow_color") if shadow_material else Color(0.0, 0.0, 0.0, 0.7)
        
        # 创建Tween来实现阴影颜色渐变
        var tween = create_tween()
        tween.set_parallel(true)  # 允许多个属性同时动画
        
        # 添加阴影颜色渐变
        if shadow_material:
            tween.tween_method(
                func(color: Color): shadow_material.set_shader_parameter("shadow_color", color),
                current_shadow_color,
                Color(0.0, 0.0, 0.0, 0.0),
                0.2  # 0.5秒完成渐变
            )
        
        # 禁用其他效果 (包括 BUFF)
        main_material.set_shader_parameter("enable_float_effect", false)
        main_material.set_shader_parameter("enable_hover_effect", false)
        # main_material.set_shader_parameter("enable_buff_overlay", false)
        main_material.set_shader_parameter("enable_ripple_effect", false)
        main_material.set_shader_parameter("enable_gold_flash_effect", false)
        
        # 启用溶解效果
        main_material.set_shader_parameter("enable_dissolve_effect", true)
        
        # 设置溶解进度为1.0，将在_process中逐渐减小
        dissolve_progress = 1.0
        is_dissolving = true
        
        # 隐藏其他元素
        if rounds_label and buff_label:
            tween.tween_property(rounds_label, "modulate:a", 0.0, 0.2)
            tween.tween_property(buff_label, "modulate:a", 0.0, 0.2)
            await tween.finished
            rounds_label.visible = false
            buff_label.visible = false

# 设置发光效果 (旧逻辑，现在集成在 highlight 中)
# func set_glow_effect(enabled: bool):
    # ...

# 溶解效果完成信号
signal dissolve_completed

# 处理拖动开始
func _start_drag():
    is_dragging = true
    
    # 发送拖动开始信号
    card_drag_started.emit(self)
    
    # 保存原始位置
    original_position = position
    
    # 设置全局位置元信息，用于返回动画
    set_meta("hand_global_position", global_position)
    
    # 缩放卡牌
    scale = Vector2(hover_scale, hover_scale)
    target_scale = Vector2(hover_scale, hover_scale)
    
    # 更新shader参数
    if buff_shader_material:
        buff_shader_material.set_shader_parameter("is_hovered", true)
        buff_shader_material.set_shader_parameter("current_scale", hover_scale)
    
    # 保存原始z_index并设置为较高值，确保拖拽的卡牌显示在其他卡牌上方
    if not has_meta("original_z_index"):
        set_meta("original_z_index", z_index)
    z_index = 60
    
    # 将卡牌移到顶层
    get_parent().move_child(self, get_parent().get_child_count() - 1)
    
    # 在拖动时隐藏阴影
    # if static_shadow:
    #     static_shadow.visible = false

# 处理拖动结束
func _end_drag(drop_position):
    is_dragging = false
    
    # 发送拖动结束信号
    card_drag_ended.emit(self, drop_position)
    
    # 拖动结束后，状态由外部逻辑决定是否仍在手牌
    if is_in_hand:
        apply_float_effect()
    else:
        # 如果不在手牌，显示静态阴影
        set_static_shadow_params()

# 更新显示阶数
func update_rounds_display(is_animation: bool = false):
    if not has_buff or not card_data or not "buff" in card_data or not rounds_label:
        if rounds_label:
            rounds_label.visible = false
        return
    
    var buff_data = card_data.buff
    
    # 获取BuffManager
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        rounds_label.visible = false
        return
    
    # 获取阶数显示文本
    var rounds_text = buff_manager.get_rounds_text(buff_data)
    
    if rounds_text.length() > 0:
        # 保存原始文本以检查是否发生变化
        var original_text = rounds_label.text if rounds_label.visible else ""
        
        rounds_label.text = rounds_text
        rounds_label.visible = true
        
        # 根据剩余阶数调整颜色
        if buff_data.rounds_left <= 1:
            # 最后一阶显示红色警告
            rounds_label.add_theme_color_override("font_color", Color(1.0, 0.2, 0.2))
        elif buff_data.rounds_left <= 3:
            # 倒数第二阶显示橙色警告
            rounds_label.add_theme_color_override("font_color", Color(1.0, 0.6, 0.2))
        else:
            # 正常阶显示黄色
            rounds_label.add_theme_color_override("font_color", Color(1.0, 0.9, 0.2))
        
        # 如果文本发生变化，播放动画
        if is_animation:
            var animation_manager = get_node("/root/AnimationManager")
            if animation_manager:
                # 确保标签准备好进行动画
                rounds_label.pivot_offset = rounds_label.size / 2
                var tween = animation_manager.play_rounds_label_animation(rounds_label)
                await tween.finished
    else:
        rounds_label.visible = false

# BUFF阶数更新信号处理
func _on_buff_rounds_updated(buff_data):
    if card_data and "buff" in card_data and card_data.buff == buff_data:
        update_rounds_display(true)

# BUFF过期信号处理
func _on_buff_expired(buff_data):
    if card_data and "buff" in card_data and card_data.buff == buff_data:
        if not is_in_hand:
            # 触发溶解动画效果
            enable_dissolve_effect()
            # 隐藏阶数标签
            if rounds_label:
                rounds_label.visible = false
        else:
            #buff文字缓慢消失
            if buff_label and rounds_label:
                main_material.set_shader_parameter("enable_buff_overlay", false)
                var tween = create_tween()
                tween.tween_property(buff_label, "modulate:a", 0.0, 0.2)
                tween.parallel().tween_property(rounds_label, "modulate:a", 0.0, 0.2)
                await tween.finished
                buff_label.visible = false
                rounds_label.visible = false
        # 更新has_buff状态
        has_buff = false
        # 从active_buffs和buff_area中移除
        var buff_manager = get_node("/root/BuffManager")
        if buff_manager:
            buff_manager.active_buffs.erase(buff_data)
            buff_manager.buff_area.erase(buff_data)

# 设置卡牌禁用状态
func set_disabled(disabled: bool):
    is_disabled = disabled
    if is_disabled:
        # 应用禁止效果
        apply_forbidden_effect(true)
        # 禁用拖拽
        # can_drag = false
    else:
        # 移除禁止效果
        apply_forbidden_effect(false)
        # 恢复拖拽
        # can_drag = true
    
    # 更新材质参数
    if main_material:
        main_material.set_shader_parameter("is_disabled", is_disabled)

# 应用禁止效果
func apply_forbidden_effect(enable: bool = true):
    if not main_material: 
        return # 没有材质无法应用
        
    if enable:
        # 加载禁止图标纹理
        var forbidden_tex = load("res://assert/cards/forbidden.png")
        if forbidden_tex:
            # 设置禁止效果参数
            main_material.set_shader_parameter("forbidden_texture", forbidden_tex)
            # 启用禁止效果
            main_material.set_shader_parameter("enable_forbidden_effect", true)
            
            # 保持原有效果（浮动或BUFF）
            # 不需要禁用其他效果，禁止效果会叠加在其他效果之上
        else:
            print("警告: 禁止图标纹理加载失败")
            # 禁止纹理加载失败时使用备用方案：调整不透明度
            modulate = Color(0.5, 0.5, 0.5, 0.7)
    else:
        # 禁用禁止效果
        main_material.set_shader_parameter("enable_forbidden_effect", false)
        # 恢复正常显示
        modulate = Color(1, 1, 1, 1)

# 添加窗口大小变化的通知处理
func _notification(what):
    if what == NOTIFICATION_RESIZED:
        # 窗口大小变化时调整卡牌大小
        _adjust_card_size()
        
# 窗口大小变化处理
func _on_window_size_changed():
    _adjust_card_size()

# 调整卡牌大小以适应窗口大小变化
func _adjust_card_size():
    # 获取当前视口大小
    var viewport_size = get_viewport_rect().size
    
    # 计算基于视口大小的缩放因子
    # 使用较小的维度（宽度或高度）作为参考，确保卡牌完全可见
    var base_width = 1152.0  # 基准宽度
    var base_height = 648.0  # 基准高度
    
    var scale_factor_width = viewport_size.x / base_width
    var scale_factor_height = viewport_size.y / base_height
    
    # 使用较小的缩放因子，确保卡牌在任何方向都不会超出屏幕
    var scale_factor = min(scale_factor_width, scale_factor_height)
    
    # 应用缩放
    var base_size = Vector2(base_card_width, base_card_height)  # 卡牌的基本大小
    custom_minimum_size = base_size * scale_factor
    size = custom_minimum_size
    
    # 更新旋转中心点
    pivot_offset = custom_minimum_size / 2
    
    # 更新阴影大小和位置
    if static_shadow and shadow_material:
        static_shadow.custom_minimum_size = custom_minimum_size * 0.98
        static_shadow.size = custom_minimum_size * 0.98
        static_shadow.pivot_offset = static_shadow.size / 2
        
    # 更新BUFF标签位置和大小
    if buff_label:
        buff_label.size = Vector2(custom_minimum_size.x, 40 * scale_factor)
        buff_label.add_theme_font_size_override("font_size", int(24 * scale_factor))
    
    # 更新卡牌值标签的字体大小
    var value_label = get_node_or_null("ValueLabel")
    if value_label:
        value_label.add_theme_font_size_override("font_size", int(24 * scale_factor))
        value_label.position = Vector2(10 * scale_factor, 10 * scale_factor)
        value_label.size = Vector2(40 * scale_factor, 30 * scale_factor)
    
    # 更新花色图像的大小和位置
    var suit_image = get_node_or_null("SuitImage")
    if suit_image:
        suit_image.position = Vector2(10 * scale_factor, 45 * scale_factor)
        suit_image.size = Vector2(30 * scale_factor, 30 * scale_factor)
    
    # 更新BUFF特效大小
    if buff_effect:
        buff_effect.size = custom_minimum_size + Vector2(10 * scale_factor, 0)
        buff_effect.position = Vector2(-5 * scale_factor, 0)
        buff_effect.pivot_offset = buff_effect.size / 2
    
    # 更新光芒特效大小
    if buff_ray_effect:
        buff_ray_effect.size = custom_minimum_size

    if rounds_label:
        rounds_label.position = Vector2(-custom_minimum_size.x + 120, 0)
        rounds_label.add_theme_font_size_override("font_size", int(24 * custom_minimum_size.x / base_card_width))

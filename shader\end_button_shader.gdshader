shader_type canvas_item;

// 闪烁和发光效果参数
uniform float pulse_speed = 1.5;
uniform float pulse_intensity = 0.3;
uniform vec4 glow_color : source_color = vec4(1.0, 0.8, 0.2, 1.0);
uniform float glow_intensity = 0.0;  // 点击时会增加这个值

// 时间变量
uniform float time_offset = 0.0;

void fragment() {
    // 获取基础纹理颜色
    vec4 tex_color = texture(TEXTURE, UV);
    
    // 计算闪烁效果
    float pulse = sin(TIME * pulse_speed + time_offset) * 0.5 + 0.5;
    pulse = pulse * pulse_intensity;
    
    // 应用闪烁和发光效果
    vec4 final_color = tex_color;
    final_color.rgb += pulse * 0.2; // 增加闪烁效果的强度
    
    // 应用点击时的发光效果
    if (glow_intensity > 0.0) {
        // 计算到中心的距离，用于发光效果
        vec2 center = vec2(0.5, 0.5);
        float dist = distance(UV, center);
        
        // 在边缘添加发光
        float glow = smoothstep(0.5, 0.2, dist) * glow_intensity;
        final_color.rgb = mix(final_color.rgb, glow_color.rgb, glow * 0.6);
        
        // 整体增亮
        final_color.rgb += glow_color.rgb * glow_intensity * 0.3;
    }
    
    // 保留原始的alpha值
    final_color.a = tex_color.a;
    
    COLOR = final_color;
}

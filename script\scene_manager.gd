extends Node

# 场景管理器 - 用于处理场景之间的过渡

# 预加载过渡场景
var TransitionScene = preload("res://scence/transition_scene.tscn")

# 当前过渡场景实例
var current_transition_scene = null
var is_transitioning = false

# 单例模式
func _init():
    process_mode = Node.PROCESS_MODE_ALWAYS

# 清理当前过渡场景
func _cleanup_transition_scene():
    if current_transition_scene:
        if is_instance_valid(current_transition_scene):
            current_transition_scene.queue_free()
        current_transition_scene = null
    is_transitioning = false

# 带过渡动画切换场景
func change_scene_with_transition(next_scene_path: String):
    if is_transitioning:
        return
        
    is_transitioning = true
    
    # 清理旧的过渡场景（如果存在）
    _cleanup_transition_scene()
    
    # 创建新的过渡场景实例
    current_transition_scene = TransitionScene.instantiate()
    get_tree().root.add_child(current_transition_scene)
    current_transition_scene.transition_finished.connect(_on_transition_finished)
    
    # 开始场景过渡
    current_transition_scene.transition_to_scene(next_scene_path)

# 过渡动画完成回调
func _on_transition_finished():
    _cleanup_transition_scene()

# 手动更新过渡进度（用于资源加载时显示进度）
func update_transition_progress(value: float):
    if current_transition_scene and is_transitioning and is_instance_valid(current_transition_scene):
        current_transition_scene.update_progress(value)

# 强制结束过渡动画
func force_end_transition():
    if current_transition_scene and is_instance_valid(current_transition_scene):
        current_transition_scene.start_exit_transition()
    is_transitioning = false

# 手动开始过渡动画（不切换场景，只执行入场动画）
func start_transition():
    if is_transitioning:
        return
        
    is_transitioning = true
    
    # 清理旧的过渡场景（如果存在）
    _cleanup_transition_scene()
    
    # 创建新的过渡场景实例
    current_transition_scene = TransitionScene.instantiate()
    get_tree().root.add_child(current_transition_scene)
    current_transition_scene.transition_finished.connect(_on_transition_finished)
    
    # 仅执行入场动画
    current_transition_scene.start_enter_transition()
    
    return current_transition_scene

# 手动结束过渡动画（执行退场动画）
func end_transition():
    if not is_transitioning or not current_transition_scene:
        return
    
    if not is_instance_valid(current_transition_scene):
        _cleanup_transition_scene()
        return
    
    # 执行进度完成流程
    current_transition_scene.update_progress(1.0)
    
    # 等待进度完成
    await current_transition_scene.progress_completed
    
    if is_instance_valid(current_transition_scene):
        # 执行退场动画
        current_transition_scene.start_exit_transition()
    
    is_transitioning = false

# 当场景树改变时
func _notification(what):
    if what == NOTIFICATION_PREDELETE:
        _cleanup_transition_scene() 

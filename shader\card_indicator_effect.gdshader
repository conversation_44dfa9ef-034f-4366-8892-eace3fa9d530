shader_type canvas_item;

// 浮动效果参数
uniform float amplitude : hint_range(0.0, 10.0) = 3.0;  // 浮动幅度
uniform float frequency : hint_range(0.1, 5.0) = 1.0;    // 浮动频率
uniform float time_offset = 0.0;                        // 时间偏移，用于错开多个卡牌的浮动

// 闪烁效果参数
uniform float flash_speed : hint_range(0.1, 5.0) = 1.5;  // 闪烁速度
uniform float min_alpha : hint_range(0.1, 0.9) = 0.4;    // 最小透明度
uniform float max_alpha : hint_range(0.5, 1.0) = 0.8;    // 最大透明度

// 边缘模糊效果参数
uniform float edge_softness : hint_range(0.0, 0.5) = 0.1;  // 边缘模糊程度

// 颜色参数
uniform vec4 color : source_color = vec4(0, 0, 0, 0.6);  // 指示器颜色

// 弃牌警告效果参数
uniform bool is_discard_warning = false;  // 是否处于弃牌警告状态
uniform float warning_shake_intensity : hint_range(0.0, 10.0) = 1.0;  // 抖动强度
uniform float warning_flash_speed : hint_range(0.1, 10.0) = 5.0;  // 警告闪烁速度

void vertex() {
    // 应用垂直浮动效果
    float time = TIME * frequency + time_offset;
    float offset = sin(time) * amplitude;

    // 如果是弃牌警告状态，添加快速抖动效果
    if (is_discard_warning) {
        float shake_time = TIME * warning_flash_speed;
        offset += sin(shake_time * 1.5) * warning_shake_intensity;
        offset += cos(shake_time * 1.5) * warning_shake_intensity * 0.5;
        
        // 添加水平抖动
        VERTEX.x += sin(shake_time * 4.0) * warning_shake_intensity * 0.3;
    }

    // 只在Y轴上应用浮动
    VERTEX.y += offset;

    // 添加轻微的旋转效果
    float rotation = sin(time) * 0.01; // 非常轻微的旋转
    vec2 center = vec2(0.5, 0.5);
    vec2 centered_vertex = VERTEX - center;
    float s = sin(rotation);
    float c = cos(rotation);
    VERTEX.x = centered_vertex.x * c - centered_vertex.y * s + center.x;
    VERTEX.y = centered_vertex.x * s + centered_vertex.y * c + center.y;
}

void fragment() {
    // 获取原始颜色
    vec4 original_color = texture(TEXTURE, UV);

    // 计算闪烁效果的透明度
    float flash_time = is_discard_warning ? TIME * warning_flash_speed : TIME * flash_speed;
    float alpha_factor = mix(min_alpha, max_alpha, (sin(flash_time) + 1.0) / 2.0);

    // 计算到边缘的距离
    vec2 center_dist = abs(UV - vec2(0.5, 0.5)) * 2.0;
    float dist = max(center_dist.x, center_dist.y);

    // 使用smoothstep实现边缘模糊效果
    float edge_alpha = 1.0 - smoothstep(1.0 - edge_softness, 1.0, dist);

    // 如果是弃牌警告状态，使用红色并增加闪烁强度
    vec4 final_color = color;
    if (is_discard_warning) {
        final_color = vec4(1.0, 0.1, 0.1, color.a); // 红色
    }

    // 应用颜色、闪烁效果和边缘模糊
    COLOR = vec4(final_color.rgb, original_color.a * alpha_factor * edge_alpha);
}
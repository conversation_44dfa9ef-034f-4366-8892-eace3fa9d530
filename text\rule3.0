一、基础设定
1. 游戏构成及说明
   - 使用标准52张扑克牌以及大小王
   - 任何弃牌都会被重新随机位置加入牌堆
   - 被消耗的卡牌在本局游戏中不再出现


2. 胜负条件
   - 每阶有积分限制，积分限制=固定积分限制-骰子总分数。随着阶数增加，固定积分限制也随之增大。第一阶固定积分限制为30分，以后每阶固定积分限制为：30+阶数*5
   - 阶结束小于积分限制则为失败，失败游戏结束，进入结算页面。
   - 阶结束大于等于积分限制则为成功，成功后总积分扣减积分限制，剩余积分会累积到下一阶。

3. 游戏界面
    - 开始/主菜单界面（居中展示：开始游戏按钮，游戏设置按钮，退出游戏按钮）
    - 游戏界面 （顶部左边位置展示阶数和积分池积分数，顶部右边位置展示”现在分数/限制积分“，中心靠下部分展示玩家手牌（明牌），中心靠上部分展示公共牌池（明牌），
    界面右边靠中心位置展示牌堆（暗牌，多张牌重叠），牌堆左边显示弃牌区，界面左下角展示现在可用行动点数，界面右下角展示结束阶按钮）
    - 骰子界面（弹出框，半透明） （中心位置有三个骰子，界面右侧靠中心位置有一个确认按钮）
    - 设置界面（弹出框，半透明）（调整游戏大小下拉框（全屏/无边框/各种分辨率），调整声音大小（背景声音，特效声音））
    - 结算界面（弹出框，半透明）（展示玩家本局所得总积分）
    进入游戏第一个界面为开始界面，点击开始游戏则进入游戏界面，点击游戏设置则弹出设置界面，点击退出游戏则退出；
    游戏界面右上角有两个按钮，一个为设置，点击也会弹出设置页面，另一个为退出，点击后弹出提示是否退回到主菜单，点击是则回到主菜单；
    设置界面最下方中心有一个返回按钮，点击后退出设置界面，返回到上一个界面；
    结算界面最下方中心有一个退回主菜单按钮，点击后回到主菜单。

二、核心流程
1. 游戏阶流程
   准备阶段  -> 行动阶段 -> 结算阶段

准备阶段：
    进入游戏界面，首先会弹出骰子界面，点击骰子后撒骰子（模拟撒骰子动画），骰子撒出来的点数之合为骰子总分数）。玩家每阶有三次撒骰子机会。
    玩家最多有三次撒骰子机会，骰子总分数则会显示到游戏界面的顶部右边位置。骰子总分数确认完成后，骰子界面自动退出，回到游戏界面。
    首先系统会先发三张牌给玩家，展示到玩家手牌区，手牌中心横向对齐，每张牌之间都有间隙。
    然后发三张牌到公共牌池，展示到公共牌池区，公共牌池区也是横向中心对齐，每张牌之间有间隙。
    
行动阶段：
    初始行动点数为3点。行动点数不使用可累计到下阶，每阶固定增加2点行动点数，但最大累计为5点。
    玩家可以拖动手牌以调整手牌位置顺序，不消耗行动点数。
    点击牌堆则添加牌堆最上方一张牌到手牌最右边（动画：卡牌背面展示从牌堆滑动到手牌，再翻面），行动点数-1。
    拖拽手牌到公共牌池对应的牌上，则交换对应牌和拖拽的牌，行动点数-2。
    在点击结束阶时，如果玩家手牌区有多余5张牌，则不可结束，必须弃牌至5张。弃牌不消耗行动点数。
    玩家调整好手牌后，可点击结束阶，结束阶按钮可按下的条件是手牌区至少有三张手牌。同时，手牌区只有大于3张牌时才可弃牌。
    结束阶时，会计算前三张牌（从左到右）最大积分，也就是根据《牌型规则》计算出的可获得的最大积分数（这个积分也会展示在三张牌上方中间位置）

结算阶段：
    总积分（总积分=积分池+本阶积分）超过积分限制数则判定阶胜利，自动进入下一阶，手牌区计算积分的卡牌会被消耗，在本局游戏中不再出现，公共牌区卡牌会被弃牌，玩家手牌区非计算积分卡牌保留，总积分扣减本阶积分限制，重新进入准备阶段，阶数加一，行动力+2；
    否则判定失败，弹出结算界面


三、游戏效果
特效规则：
    每张明牌都会有轻微浮动特效。
    有特殊效果的BUFF牌会有单独效果。
    玩家手牌前三张牌会一直保持轻微发亮特效，且比后面的牌稍稍大点。
    前三张牌根据《牌型规则》和《特色规则》，在三张牌上方中间位置展示这三张牌可获得最大积分数。

四、牌型规则
豹子：三张点相同的牌。例：AAA、222。（AAA最大，222最小）。基础得分30
顺金：花色相同的顺子。例：黑桃456、红桃789。最大的顺金为花色相同的QKA，最小的顺金为花色相同的123。（QKA最大，A23最小）。基础得分25
金花：花色相同，非顺子。例：黑桃368，方块145。（JKA最大，235最小）。基础得分20
顺子：花色不同的顺子。例：黑桃5红桃6方块7。最大的顺子为花色不同的QKA，最小的顺子为花色不同的123。（QKA最大，A23最小）基础得分15
对子：两张点数相同的牌。例：223，334。（KAA最大，223最小）基础得分10
单张：三张牌不组成任何类型的牌。（JKA最大，235最小）基础得分5
特殊：牌型点数相同且总和为骰子点数。基础得分50
牌面总分为三张牌面分数总和（数字牌对应相应分数，A为1分，JQK为10分）
阶总得分=基础得分+牌面总分


五、特色规则
每5阶发放至公共牌池的牌会随机出现BUFF效果，效果名称显示在卡牌正下方，玩家在交换对应牌并在阶结束时使用效果（使用效果：结束阶时，BUFF牌在手牌前三，计算入总积分）则效果从本阶开始在本局游戏生效。
效果生效后，对应BUFF牌显示在最左侧卡牌BUFF区（纵向显示，堆叠效果）。
BUFF效果仅在计算当前阶总积分时有效，BUFF仅有加成效果，BUFF效果有如下几种：
 - 1 牌堆随机 +N 张卡牌 （N范围[-5,10]），特效：白色
 - 2 所有相同点数牌面分数 +N （N范围[1,10]整数），特效：绿色
 - 2 所有相同牌型牌面分数 +N （N范围[1,10]整数），特效：绿色
 - 3 所有相同点数牌面分数 *N （N范围[2,5]整数），特效：蓝色
 - 3 所有相同牌型牌面分数 *N （N范围[1.5,3]一位小数），特效：蓝色
 - 4 骰子数量 +N （N为-1或1，骰子数量最小0，超出限制的BUFF无效）特效：紫色
 - 4 掷骰子机会 +N （N为-1或1）特效：紫色
 - 4 手牌区上限 +N （N范围[-2,2]整数，手牌区最少3，超出限制的BUFF无效）特效：紫色
使用效果后，所有牌堆、手牌、弃牌区对应卡牌都附加BUFF效果，同时附加特效。
N在范围内取值并不是完全随机，规则：N范围[min,max]，第一阶N范围[min,min]，以后每增加5阶N范围[min,min+2]，直到最大范围增加值max。
BUFF效果出现的概率随序号分布，序号越小，出现概率越大。
特效效果为卡牌表面附加一层很薄的浅色玻璃效果。

 
 六、核心玩法总结
 游戏的内核是通过随机的卡牌进行组合积分进行闯关，通过随机性出来的卡牌点数和组合的类型还有与公共牌叠加BUFF的效果，综合来计算分数。
 游戏前期纯看运气，卡牌的随机点数和随机组合来决定分数大小，有限的换牌和抽牌次数限制了玩家每阶操作的步数。
 中期玩家需要通过前期叠加的BUFF来增加积分数。
 后期基本是空白的，积分的阶制增加是必须，需要增加游戏难度，BUFF的叠加一定程度会降低积分获取的难度，所以要有所制衡。但是现在阶段的BUFF还是缺乏想象力。
 会导致后期难度陡然上升，而且BUFF的随机性极大增加了不确定性而削弱了游戏的策略性。
 现在游戏会更偏向随机，纯赌博性质，而策略性质较少。
 游戏的复杂度偏低，BUFF的设计上有所单一，仅限积分或者骰子或者卡牌数据变化，而且未做平衡。
 现在的结论是：
 需要增加游戏复杂度：
 1.需要增加BUFF类型，提高整体BUFF多样性

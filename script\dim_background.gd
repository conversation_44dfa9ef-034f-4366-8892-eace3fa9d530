extends ColorRect

var current_mask_path: PackedVector2Array

func _ready() -> void:
    # 设置为接收所有鼠标输入
    mouse_filter = Control.MOUSE_FILTER_STOP

func _draw() -> void:
    # 禁用 ColorRect 的默认绘制
    color = Color(0, 0, 0, 0)
    
    if has_meta("mask_path"):
        var path = get_meta("mask_path")
        if path.size() > 0:
            current_mask_path = path
            # 创建一个完整的屏幕大小的矩形路径
            var screen_rect = PackedVector2Array([
                Vector2.ZERO,
                Vector2(size.x, 0),
                Vector2(size.x, size.y),
                Vector2(0, size.y),
                Vector2.ZERO,
            ])

            for i in range(path.size()):
                screen_rect.append(path[i])
            # 封边
            screen_rect.append(path[0])
            screen_rect.append(Vector2.ZERO)

            # 使用原始的遮罩颜色绘制整个屏幕
            draw_colored_polygon(screen_rect, Color(0, 0, 0, 0.7))
        else:
            current_mask_path = PackedVector2Array()

func _gui_input(event: InputEvent) -> void:
    if event is InputEventMouse:
        var mouse_pos = get_local_mouse_position()
        # 如果鼠标在高亮区域内，不阻止事件传递
        if current_mask_path.size() > 0 and Geometry2D.is_point_in_polygon(mouse_pos, current_mask_path):
            mouse_filter = Control.MOUSE_FILTER_IGNORE 

1. 特效牌被交换到手牌区后无法再次被移动  √
2. BUFF牌展示区卡牌没有特效且无BUFF说明，而且还会在掷骰子界面显现出来 √
3. BUFF区的牌未对后续手牌生效BUFF √
3. 增加掷骰子次数有显示，但是如果上次用完下阶就没有掷骰子次数 √
3. 每阶BUFF区掷骰子数增减都会生效 √
3. 手牌区上限BUFF未生效 √
3. 有些BUFF没有生效 √
3. 骰子数量增加概率指数级别降低 暂时不做
3. BUFF区卡牌获取难度指数级别增加 暂时不做
4. 卡牌锯齿问题 √
4. 拖拽松开后卡牌落地特效 √
4. 卡牌交换特效 √
4. 抽牌按钮重新设计以及抽牌特效 √
4. 弃牌区域重新设计 √
4. 公共牌池区域重新设计 暂时没有好的想法
4. BUFF区重新设计 暂时没有好的想法
4. 弃牌动画 √
5. 骰子页面重新设计 √
    骰子页面在游戏页面内嵌，不再单独页面 √
    骰子弹出和回收动画 √
    骰子滚动动画，粒子特效，停止特效 √
    掷骰子和停止统一按钮 不做
5. 结算页面重新设计 没有设计X
5. 加入光标动画及动作 √
5. 积分结算动画优化：
    手牌结束溶解 √
    公共池牌结束飞回抽牌区 √
    结算积分碰撞扣减 √
    优化动画效果 √
5. buff牌应用到结算积分时 牌型和点数buff类型出现错乱 √
5. 弃牌动画更改为卡牌飞回手牌区，阶结束手牌前三张卡牌溶解（溶解动画统一为消失动画，不再挂钩弃牌动画） √
5. 主菜单背景变为有卡牌不断落下的效果 √
5. 卡牌有重叠bug，出现两张卡牌叠在一起的情况
5. 交换卡牌后动画未结束情况下再次交换卡牌会交换错乱

6. 特效bug
    落牌特效替换以及位置显示bug √
    buff牌特效替换，简单图标buff， √
    加深卡牌正面颜色，不能用白色了，特效有用到白色，或者特效换颜色 √
6. 顶部文字优化：
    总积分，限制，阶数 √
6. 增加规则，卡牌抽完则游戏直接结束。弱化牌堆的概念，牌堆还是存在，但是不显示，抽牌、掷骰子都需要消耗积分（第一次免费），积分小于或等于0则游戏结束。取消消耗卡牌设定，手牌被使用后也属于弃牌，回归牌堆。 积分暂时不走扣减
6. 重构一下full代码，现在这个文件有点重了。  待继续优化
6. 规则修改：
    抽牌，换牌都需要消耗积分 暂时不做
    增加牌堆BUFF替换为增加单阶积分BUFF √
    进入BUFF区的牌规则增加：并非所有BUFF牌可进入BUFF区，一次性BUFF消耗后不会进入BUFF区，如：增加单阶积分，增加骰子数，增加手牌数；需要根据卡牌判断后计算积分的BUFF在使用后进入BUFF区 √
6. 积分增减和空闲动画统一  处理不了

6. 骰子页面优化， 
    顶部文字替换图标， √
    界面美化 √
    骰子动画 √
6. 卡牌样式重新设计，按照花色+点数自定义卡牌。 不好做且意义不大。
6.顶部栏设计：还有三个按钮设计
6. buff区卡牌展示优化，
    排列优化，包括现在buff文字不在中心的问题 √
    多卡牌时展示问题，是否需要限制buff数，无线叠加有什么问题
6. 增加游戏规则复杂度，提升游戏性 
    增加BUFF详细介绍页面，弹出卡牌占据整个页面左侧。右侧详细文字说明 √
    所有BUFF牌有阶数限制。在卡牌右上角标记当前剩余使用阶数。使用次数用完时，BUFF卡牌消失特效。 √
    卡牌小标题显示逻辑：每阶的无标题，直接加减乘除，一次性质的，包括最后最先结算的，增加一个小标题。
    增加BUFF牌类型：
    动物园BUFF:积分控制。
        老虎牌，每阶扣减算分最后牌积分，N阶一次性获取积分（借出去积分收利息） √
        兔子牌，一次性获得积分，N阶内扣减算B积分（借贷还利息） √
        青蛙牌，每阶限制为原限制的70%，但不能使用S花色的牌 √
        乌龟牌，S花色卡牌分数乘以K倍，N阶后扣减Q积分 √
        猩猩牌，每阶牌面积分超过S则加S分，否则减N分， N阶有效 √
        鲨鱼牌，所有BUFF有效阶数+N，青蛙/乌龟牌失效（一次性）√
        老鹰牌，兔子牌失效，老虎/猩猩牌剩余次数乘以N（一次性） √
        锦鲤牌，积分*N（一次性） √
    法术BUFF：位置控制。
        小火球术：算分牌最右边牌点数为红桃，则加A分，否则扣B分
        大火球术：公共池牌边侧牌为偶数，则加A分，否则扣B分
        黑暗咒术：手牌最大牌为J，则加A分，否则扣B分
    
7. 锦鲤牌的获取难度提升，总共玩了XX阶后有几率获取
7. 手牌未积分部分留在场上，不回收 √
7. 在菜单页加入继续游戏 是否需要加入这种存档机制，存档还是挺麻烦的 ×
7. bug:进度条最长长度S需要在每阶扣减该阶的限制积分A，并将所得值S-A作为进度条长度。下阶限制积分B，则总长度为S-A-B。 √
7. bug:buff生效时，积分不增反减 √
7. 青蛙牌bug:青蛙牌失效卡牌buff下，依然能被其他BUFF获取到失效卡牌，这个应该是buff处理逻辑没有处理失效卡牌。非青蛙牌获取阶，失效卡牌可以使用。 √
6. 光标动画优化：找下合适资源，主要以手势舞类型作为动画  暂时完成，有优化空间
7. 掷骰子功能重构：是否太过于复杂了。
    1掷骰子在同一局游戏内会累加获取金币，金币数额为每次掷骰子数，每N阶开启商店刷新，可通过金币购买商店内BUFF卡牌，购买成功后，BUFF卡牌则可在本局游戏内出现
    2原金币和税额更换结算方式
        1血条值，PASS
        2能量值，每阶标定一个目标值，积分可增加能量，达标后本阶即可成功，同时多余能量累计入下阶。图形显示，更加直观，鼠标放上去能看到具体值。

还可以做文章的内容：
 1阶数，相当于到达一定层数后，要削弱前面的难度，BUFF卡牌是一方面，让玩家在重复中有获利。
 2掷骰子，这个后面想法是减少次数，不用每阶都出，变成一个稀有或者几率性事件。
 主要是如何挂钩BUFF牌组开启限制

7.buff牌不要每次都展示buffLabel,在手牌有加成的情况下，才展示 √
7.抽牌弃牌区改为一个圆脸，点击会吐牌，拖拽到它脸上会张嘴准备吃牌（暂定）现在改成了牌堆，效果一般 √
7.bug:
    1阶结束，三张以后的牌不收回，直接移到前面继续展示，不要清空
    2每次移动卡牌，所有卡牌浮动动画都重置了
7. 卡牌规则验证，整体流程验证
7. 加入音效
7. 加入UI
7. 动物园类型卡牌还未启用，暂时考虑是通过阶数达到一定数额后，可永久开启某类型的卡组。同时在游戏中，需要通过掷骰子的方式来购买BUFF牌（加强掷骰子功能）
8. 全屏以及各种尺寸适配问题

我希望将阶结算按钮进行装饰：外围一圈又跑马灯效果，内层为紫色带有光泽，当正常情况下，跑马灯慢速顺序亮起；当鼠标移动上去后，跑马灯加速；当点击时，整体按钮有Q弹效果。

Buff部分：
动物园buff:（等级2解锁）
定位为初期buff，只有数值上的增减益，牌面积分，组合积分（没做），总积分，限制积分，阶数值积分（没做），抽牌弃牌积分（没做），换牌积分（没做），每阶积分。

法术buff：（等级3）
定位为初期buff，只有位置上增减益，（这个部分都未开发）手牌位置，公共牌位置，结算牌位置。规则：位置是5则加分，位置是黑桃则乘2，位置1比位置2大则扣分。行动点数？

机械buff：（等级4）
中期buff，主打搭配性大额增减益，（待开发）合金手：所有红桃被标记，合金脚：所有被标记的合金手乘以阶数。机械齿轮：每阶积攒齿轮数✖️2，机械发条：获得机械齿轮数额积分，切清空齿轮。钛镀甲：牌面积分为5的倍数则获得牌面分数叠甲，聚炎能量：获得叠甲乘以阶数积分，腐蚀物质：叠甲减随机数。


恶魔buff：（5）
中期buff，主打干扰，（待开发），恶魔低语：恐惧，所有红桃点数模糊，红骷髅：卡组增减益随机，




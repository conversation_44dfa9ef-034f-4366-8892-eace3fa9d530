extends CanvasLayer

signal transition_finished
signal progress_completed

# 获取节点引用
@onready var slide_panel = %SlidePanel
@onready var progress_container = %ProgressContainer
@onready var progress_bar = %ProgressBar
@onready var duck_sprite = %DuckSprite
@onready var loading_label = %LoadingLabel
@onready var vignette_mask = $Control/VignetteMask

# 动画的持续时间
var slide_animation_duration = 0.5

# 状态变量
enum TransitionState { IDLE, ENTERING, LOADING, EXITING }
var current_state = TransitionState.IDLE

# 窗口尺寸
var screen_width = 0
var screen_height = 0

# 目标场景
var target_scene_path = ""

# 是否正在被销毁
var is_being_destroyed = false

# 准备就绪
func _ready():
    # 获取窗口尺寸
    screen_width = get_viewport().size.x
    screen_height = get_viewport().size.y
    
    # 初始化滑动面板位置
    _update_panel_size()
    
    # 初始化进度条
    if is_instance_valid(progress_bar) and is_instance_valid(progress_container):
        progress_bar.value = 0
        progress_container.modulate.a = 0
    
    # 设置鸭子位置
    _update_duck_position(0)
    
    # 初始化遮罩
    if is_instance_valid(vignette_mask):
        vignette_mask.material.set_shader_parameter("vignette_opacity", 0.0)

# 更新滑动面板尺寸和位置
func _update_panel_size():
    if not is_instance_valid(slide_panel):
        return
    
    # 设置面板的初始位置（在屏幕上方）
    #slide_panel.custom_minimum_size = Vector2(screen_width, screen_height * 2)
    slide_panel.position.y = -screen_height

# 启动进入动画（从上往下滑动）
func start_enter_transition():
    if current_state != TransitionState.IDLE or is_being_destroyed:
        return
        
    current_state = TransitionState.ENTERING
    
    # 检查节点是否有效
    if not is_instance_valid(slide_panel):
        push_error("Slide panel is not valid during enter transition")
        return
    
    # 更新面板尺寸
    _update_panel_size()
    
    # 创建滑动动画
    var tween = create_tween()
    
    # 遮罩渐变
    if is_instance_valid(vignette_mask):
        tween.parallel().tween_property(vignette_mask.material, "shader_parameter/vignette_opacity", 0.5, slide_animation_duration)
    
    # 面板从上往下滑动
    tween.parallel().tween_property(slide_panel, "position:y", 0, slide_animation_duration).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_OUT)
    
    # 等待动画完成
    await tween.finished
    
    if not is_being_destroyed:
        # 显示进度条
        _show_progress_bar()
        
        # 更新状态为加载中
        current_state = TransitionState.LOADING

# 显示进度条
func _show_progress_bar():
    if is_being_destroyed:
        return
        
    # 检查节点是否有效
    if not is_instance_valid(progress_bar) or not is_instance_valid(progress_container):
        push_error("Progress bar or container is not valid during show")
        return
    
    # 重置进度条
    progress_bar.value = 0
    _update_duck_position(0)
    
    # 创建动画淡入进度条
    var tween = create_tween()
    tween.tween_property(progress_container, "modulate:a", 1.0, 0.3)
    
    await tween.finished

# 更新小鸭子位置
func _update_duck_position(progress: float):
    if is_being_destroyed:
        return
        
    # 检查节点是否有效
    if not is_instance_valid(progress_bar) or not is_instance_valid(duck_sprite):
        push_error("Progress bar or duck sprite is not valid during position update")
        return
    
    # 计算鸭子在进度条上的位置
    var progress_width = progress_bar.size.x
    var duck_x_position = progress_width * progress
    
    # 使用补间动画使鸭子移动更流畅
    var tween = create_tween()
    tween.set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
    tween.tween_property(duck_sprite, "position:x", 
                         clamp(duck_x_position - duck_sprite.size.x / 2, 0, progress_width - duck_sprite.size.x),
                         0.2)

# 更新进度条
func update_progress(value: float):
    if current_state != TransitionState.LOADING or is_being_destroyed:
        return
        
    # 检查节点是否有效
    if not is_instance_valid(progress_bar):
        push_error("Progress bar is not valid during update")
        return
    
    # 限制进度值范围
    var progress = clamp(value, 0.0, 1.0)
    
    # 创建动画
    var tween = create_tween()
    tween.tween_property(progress_bar, "value", progress, 0.2)
    
    # 更新鸭子位置 - 同步更新
    _update_duck_position(progress)
    
    # 如果加载完成，触发信号
    if progress >= 1.0:
        await tween.finished
        await get_tree().create_timer(0.5).timeout
        if not is_being_destroyed:
            progress_completed.emit()

# 开始退出动画（向上滑出）
func start_exit_transition():
    if current_state != TransitionState.LOADING or is_being_destroyed:
        return
        
    current_state = TransitionState.EXITING
    
    # 检查节点是否有效
    if not is_instance_valid(progress_container):
        push_error("Progress container is not valid during exit")
        return
    
    # 隐藏进度条
    var fade_tween = create_tween()
    fade_tween.tween_property(progress_container, "modulate:a", 0.0, 0.3)
    
    await fade_tween.finished
    
    if is_being_destroyed:
        return
        
    # 检查滑动面板节点是否有效
    if not is_instance_valid(slide_panel):
        push_error("Slide panel is not valid during exit")
        return
    
    # 创建滑动动画
    var tween = create_tween()
    
    # 遮罩渐变
    if is_instance_valid(vignette_mask):
        tween.parallel().tween_property(vignette_mask.material, "shader_parameter/vignette_opacity", 0.0, slide_animation_duration)
    
    # 面板向下滑动
    tween.parallel().tween_property(slide_panel, "position:y", -screen_height, slide_animation_duration).set_trans(Tween.TRANS_QUINT).set_ease(Tween.EASE_OUT)
    
    # 等待动画完成
    await tween.finished
    
    if not is_being_destroyed:
        # 重置状态
        current_state = TransitionState.IDLE
        
        # 发送过渡完成信号
        transition_finished.emit()

# 屏幕尺寸变化时更新
func _notification(what):
    if what == NOTIFICATION_WM_SIZE_CHANGED:
        screen_width = get_viewport().size.x
        screen_height = get_viewport().size.y
        
        # 更新面板尺寸
        _update_panel_size()
        
        # 如果正在执行入场动画，更新面板位置
        if current_state == TransitionState.ENTERING and not is_being_destroyed:
            if is_instance_valid(slide_panel):
                slide_panel.position.y = 0
    elif what == NOTIFICATION_PREDELETE:
        # 标记正在被销毁
        is_being_destroyed = true

# 处理从一个场景到另一个场景的过渡
func transition_to_scene(next_scene_path: String):
    if current_state != TransitionState.IDLE or is_being_destroyed:
        return
        
    target_scene_path = next_scene_path
    
    # 开始入场动画
    start_enter_transition()
    
    # 等待入场动画完成
    await get_tree().create_timer(slide_animation_duration).timeout
    
    if is_being_destroyed:
        return

    ResourceLoader.load_threaded_request(target_scene_path)
    
    # 等待资源加载
    var progress = [0.0]
    while true:
        if is_being_destroyed:
            return
            
        var status = ResourceLoader.load_threaded_get_status(target_scene_path, progress)
        
        match status:
            ResourceLoader.THREAD_LOAD_IN_PROGRESS:
                # 更新进度条
                update_progress(progress[0])
                await get_tree().create_timer(0.05).timeout
                
            ResourceLoader.THREAD_LOAD_LOADED:
                # 加载完成
                update_progress(1.0)
                
                # 获取场景资源
                var scene_resource = ResourceLoader.load_threaded_get(target_scene_path)
                
                # 等待进度完成信号（让进度条走完）
                await progress_completed
                
                if is_being_destroyed:
                    return
                
                # 切换场景
                get_tree().change_scene_to_packed(scene_resource)
                
                # 开始退出动画
                start_exit_transition()
                break
                
            ResourceLoader.THREAD_LOAD_FAILED:
                # 加载失败处理
                push_error("场景加载失败: " + target_scene_path)
                # 仍然继续退出动画
                update_progress(1.0)
                await progress_completed
                if not is_being_destroyed:
                    start_exit_transition()
                break
                
            _:
                # 其他状态，继续等待
                await get_tree().create_timer(0.1).timeout

# 重置过渡状态
func reset():
    if is_being_destroyed:
        return
        
    current_state = TransitionState.IDLE
    
    # 检查节点是否有效
    if is_instance_valid(progress_bar) and is_instance_valid(progress_container):
        progress_bar.value = 0
        progress_container.modulate.a = 0
    
    if is_instance_valid(slide_panel):
        slide_panel.position.y = -screen_height
    
    if is_instance_valid(vignette_mask):
        vignette_mask.material.set_shader_parameter("vignette_opacity", 0.0)
    
    _update_duck_position(0)

# 清理资源
func _exit_tree():
    is_being_destroyed = true 

[gd_scene load_steps=13 format=3 uid="uid://bvxe5g7xnqnkw"]

[ext_resource type="Script" uid="uid://mogv2dl2r8xk" path="res://script/card_node.gd" id="1_bnc4p"]
[ext_resource type="Shader" uid="uid://c4k0x0h2msvwr" path="res://shader/card_unified_effect.gdshader" id="1_qqdrm"]
[ext_resource type="Texture2D" uid="uid://d0qjemwd47w8l" path="res://noise/border_noise.tres" id="2_4w0gy"]
[ext_resource type="Texture2D" uid="uid://debmywpfr2iw0" path="res://assert/sign/shark_buff.png" id="3_cs7en"]
[ext_resource type="FastNoiseLite" uid="uid://csx6lkud8neih" path="res://noise/fast_noise_lite.tres" id="3_yxgfm"]
[ext_resource type="Shader" uid="uid://bluo2xyad6hmo" path="res://shader/card_effect5.gdshader" id="4_7resd"]
[ext_resource type="Texture2D" uid="uid://dnr5rljw2ebhf" path="res://assert/cards/club/1.svg" id="5_xvd6g"]
[ext_resource type="Shader" uid="uid://ctagmxhcvl7fq" path="res://shader/card_node_shader.gdshader" id="6_cs7en"]

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_cs7en"]
noise = ExtResource("3_yxgfm")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_cs7en"]
shader = ExtResource("1_qqdrm")
shader_parameter/enable_float_effect = true
shader_parameter/enable_hover_effect = false
shader_parameter/enable_buff_effect = false
shader_parameter/enable_dissolve_effect = false
shader_parameter/enable_ripple_effect = false
shader_parameter/enable_shine_effect = false
shader_parameter/enable_gold_flash_effect = false
shader_parameter/enable_forbidden_effect = false
shader_parameter/enable_3d_effect = false
shader_parameter/enable_border_effect = false
shader_parameter/enable_shadow_effect = false
shader_parameter/shadow_offset = Vector2(10, 10)
shader_parameter/shadow_color = Color(0, 0, 0, 0.635294)
shader_parameter/is_shadow_pass = false
shader_parameter/border_noise_texture = ExtResource("2_4w0gy")
shader_parameter/border_radius = 0.4
shader_parameter/border_effect_control = 0.3
shader_parameter/border_burn_speed = 0.1
shader_parameter/border_shape = 1.0
shader_parameter/float_amplitude = 3.0
shader_parameter/float_frequency = 1.0
shader_parameter/time_offset = 0.0
shader_parameter/forbidden_opacity = 0.4
shader_parameter/forbidden_scale = 0.7
shader_parameter/forbidden_pulse_speed = 3.0
shader_parameter/forbidden_pulse_min = 0.6
shader_parameter/forbidden_pulse_max = 1.2
shader_parameter/hover_scale = 1.2
shader_parameter/is_hovered = false
shader_parameter/transition_speed = 5.0
shader_parameter/current_scale = 1.0
shader_parameter/buff_texture = ExtResource("3_cs7en")
shader_parameter/glitter_color = Color(1, 1, 1, 1)
shader_parameter/glitter_size = 0.3
shader_parameter/glitter_hardness = 0.7
shader_parameter/highlight_speed = 2.0
shader_parameter/highlight_intensity = 1.0
shader_parameter/highlight_band = true
shader_parameter/buff_color = Color(1, 1, 1, 1)
shader_parameter/buff_tile_scale = 1.0
shader_parameter/buff_noise_influence = 0.1
shader_parameter/enable_buff_overlay = true
shader_parameter/dissolve_progress = 1.0
shader_parameter/dissolve_noise_texture = SubResource("NoiseTexture2D_cs7en")
shader_parameter/ripple_frequency = 2.6
shader_parameter/ripple_amplitude = 0.9
shader_parameter/ripple_rate = 2.0
shader_parameter/wave_amplitude = 0.03
shader_parameter/wave_frequency = 8.0
shader_parameter/ripple_color = Color(1, 1, 1, 1)
shader_parameter/flash_speed = 3.5
shader_parameter/min_intensity = 0.4
shader_parameter/max_intensity = 1.2
shader_parameter/gold_color = Color(1, 0.84, 0, 0.8)
shader_parameter/edge_width = 0.2
shader_parameter/Line_Smoothness = 0.045
shader_parameter/Line_Width = 0.12
shader_parameter/Brightness = 3.0
shader_parameter/Rotation_deg = 30.0
shader_parameter/Distortion = 1.8
shader_parameter/Speed = 0.3
shader_parameter/Position = 0.0
shader_parameter/Position_Min = 0.25
shader_parameter/Position_Max = 0.5
shader_parameter/Alpha = 1.0
shader_parameter/card_width = 120.0
shader_parameter/card_height = 180.0
shader_parameter/mouse_position = Vector2(0, 0)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_xvd6g"]
shader = ExtResource("6_cs7en")
shader_parameter/enable_float_effect = true
shader_parameter/enable_hover_effect = false
shader_parameter/enable_buff_effect = false
shader_parameter/enable_dissolve_effect = false
shader_parameter/enable_ripple_effect = false
shader_parameter/enable_shine_effect = false
shader_parameter/enable_gold_flash_effect = false
shader_parameter/enable_forbidden_effect = false
shader_parameter/enable_3d_effect = false
shader_parameter/enable_border_effect = false
shader_parameter/enable_shadow_effect = true
shader_parameter/shadow_offset = Vector2(2, 2)
shader_parameter/shadow_color = Color(0, 0, 0, 0.768627)
shader_parameter/is_shadow_pass = true
shader_parameter/border_radius = 0.45
shader_parameter/border_effect_control = 0.6
shader_parameter/border_burn_speed = 0.7
shader_parameter/border_shape = 1.0
shader_parameter/float_amplitude = 3.0
shader_parameter/float_frequency = 1.0
shader_parameter/time_offset = 0.0
shader_parameter/forbidden_opacity = 0.4
shader_parameter/forbidden_scale = 0.7
shader_parameter/forbidden_pulse_speed = 3.0
shader_parameter/forbidden_pulse_min = 0.6
shader_parameter/forbidden_pulse_max = 1.2
shader_parameter/hover_scale = 1.2
shader_parameter/is_hovered = false
shader_parameter/transition_speed = 5.0
shader_parameter/current_scale = 1.0
shader_parameter/glitter_color = Color(1, 1, 1, 1)
shader_parameter/glitter_size = 0.3
shader_parameter/glitter_hardness = 0.7
shader_parameter/highlight_speed = 2.0
shader_parameter/highlight_intensity = 1.0
shader_parameter/highlight_band = true
shader_parameter/buff_color = Color(1, 1, 1, 1)
shader_parameter/buff_tile_scale = 1.0
shader_parameter/buff_noise_influence = 0.1
shader_parameter/enable_buff_overlay = false
shader_parameter/dissolve_progress = 0.0
shader_parameter/ripple_frequency = 2.6
shader_parameter/ripple_amplitude = 0.9
shader_parameter/ripple_rate = 2.0
shader_parameter/wave_amplitude = 0.03
shader_parameter/wave_frequency = 8.0
shader_parameter/ripple_color = Color(1, 1, 1, 1)
shader_parameter/flash_speed = 3.5
shader_parameter/min_intensity = 0.4
shader_parameter/max_intensity = 1.2
shader_parameter/gold_color = Color(1, 0.84, 0, 0.8)
shader_parameter/edge_width = 0.2
shader_parameter/Line_Smoothness = 0.045
shader_parameter/Line_Width = 0.09
shader_parameter/Brightness = 3.0
shader_parameter/Rotation_deg = 30.0
shader_parameter/Distortion = 1.8
shader_parameter/Speed = 0.4
shader_parameter/Position = 0.0
shader_parameter/Position_Min = 0.25
shader_parameter/Position_Max = 0.5
shader_parameter/Alpha = 0.8
shader_parameter/card_width = 120.0
shader_parameter/card_height = 180.0
shader_parameter/mouse_position = Vector2(0, 0)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_yxgfm"]
shader = ExtResource("4_7resd")
shader_parameter/fov = 90.0
shader_parameter/cull_back = true
shader_parameter/y_rot = 0.0
shader_parameter/x_rot = 0.0
shader_parameter/inset = 0.0

[node name="CardNode" type="TextureRect"]
material = SubResource("ShaderMaterial_cs7en")
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -60.0
offset_top = -90.0
offset_right = 60.0
offset_bottom = 90.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(60, 90)
texture = ExtResource("5_xvd6g")
expand_mode = 1
stretch_mode = 5
script = ExtResource("1_bnc4p")

[node name="StaticShadow" type="TextureRect" parent="."]
modulate = Color(0, 0, 0, 1)
show_behind_parent = true
material = SubResource("ShaderMaterial_xvd6g")
layout_mode = 0
offset_left = 5.0
offset_top = 5.0
offset_right = 125.0
offset_bottom = 185.0
pivot_offset = Vector2(60, 90)
texture = ExtResource("5_xvd6g")
expand_mode = 1
stretch_mode = 5

[node name="ValueLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 10.0
offset_top = 10.0
offset_right = 50.0
offset_bottom = 40.0
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(1, 1, 1, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 24

[node name="SuitImage" type="TextureRect" parent="."]
layout_mode = 0
offset_left = 10.0
offset_top = 45.0
offset_right = 40.0
offset_bottom = 75.0
expand_mode = 1
stretch_mode = 5

[node name="ColorRect" type="ColorRect" parent="."]
visible = false
material = SubResource("ShaderMaterial_yxgfm")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

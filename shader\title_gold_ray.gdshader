shader_type canvas_item;

// 光芒颜色
uniform vec4 ray_color : source_color = vec4(1.0, 0.9, 0.5, 0.7);
// 光芒强度
uniform float ray_intensity : hint_range(0.0, 10.0) = 5;
// 光芒长度
uniform float ray_length : hint_range(0.0, 10.0) = 5;
// 光芒旋转速度
uniform float ray_speed : hint_range(0.1, 10.0) = 5.0;
// 闪烁速度
uniform float flash_speed : hint_range(0.1, 10.0) = 5.0;
// 闪烁强度
uniform float flash_intensity : hint_range(0.0, 1.0) = 0.5;

void fragment() {
    // 从原始纹理获取颜色
    vec4 original_color = texture(TEXTURE, UV);

    // 计算中心距离
    vec2 center_uv = UV - vec2(0.5);
    float dist_from_center = length(center_uv);

    // 旋转光芒效果
    float time = TIME * ray_speed;
    float angle = atan(center_uv.y, center_uv.x);
    float ray = abs(cos(angle * 8.0 + time)) * ray_intensity;

    // 光芒强度随着距离衰减
    ray *= smoothstep(0.5, 0.0, dist_from_center);

    // 添加闪烁效果
    float flash = sin(TIME * flash_speed) * 0.5 + 0.5;
    flash = flash * flash_intensity;

    // 混合光芒效果和原始颜色
    vec4 ray_effect = ray_color * ray;
    vec4 flash_effect = ray_color * flash * smoothstep(0.5, 0.1, dist_from_center);

    // 计算最终颜色
    vec4 final_color = original_color;

    // 只在有文字的地方添加光芒效果
    if (original_color.a > 0.1) {
        // 添加金色光芒
        float gold_factor = min(1.0, original_color.r * 1.2);
        final_color = mix(original_color, vec4(1.0, 0.84, 0.0, original_color.a), gold_factor * 0.7);

        // 添加光芒和闪烁效果
        final_color = mix(final_color, ray_effect + flash_effect, (ray + flash) * 0.7);

        // 确保alpha值保持不变
        final_color.a = original_color.a;
    }

    // 输出最终颜色
    COLOR = final_color;
}
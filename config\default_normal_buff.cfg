[buff_0]

type="round_score_add"
description_template={
    "zh_CN": "每阶 +%d",
    "en": "Per Round +%d",
    "ja": "毎段階 +%d",
    "ko": "매 단계 +%d",
    "ru": "За этап +%d"
}
display_name={
    "zh_CN": "洒洒水",
    "en": "Easy Peasy",
    "ja": "簡単なこと",
    "ko": "식은 죽 먹기",
    "ru": "Пустяки"
}
explanation_template={
    "zh_CN": "[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "en": "[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ja": "[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ko": "[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ru": "[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=18
min_rounds=2
max_rounds=5

[buff_1]

type="same_rank_add"
description_template={
    "zh_CN": "同点数 +%d",
    "en": "Same Rank +%d",
    "ja": "同じ数字 +%d",
    "ko": "같은 숫자 +%d",
    "ru": "Тот же ранг +%d"
}
display_name={
    "zh_CN": "同命相连",
    "en": "Rank Bond",
    "ja": "数字の絆",
    "ko": "숫자의 연결",
    "ru": "Связь рангов"
}
explanation_template={
    "zh_CN": "所有与本卡牌相同点数的牌\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "en": "All cards with the same rank as this card\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ja": "このカードと同じ数字のカード全て\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ko": "이 카드와 같은 숫자의 모든 카드\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ru": "Все карты с тем же рангом\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=16
min_rounds=2
max_rounds=4

[buff_2]

type="same_type_add"
description_template={
    "zh_CN": "同花色 +%d",
    "en": "Same Suit +%d",
    "ja": "同じスート +%d",
    "ko": "같은 무늬 +%d",
    "ru": "Та же масть +%d"
}
display_name={
    "zh_CN": "镜像复制",
    "en": "Mirror Copy",
    "ja": "鏡像複製",
    "ko": "거울 복제",
    "ru": "Зеркальная копия"
}
explanation_template={
    "zh_CN": "所有与本卡牌相同花色的牌\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "en": "All cards with the same suit as this card\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ja": "このカードと同じスートのカード全て\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ko": "이 카드와 같은 무늬의 모든 카드\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ru": "Все карты той же масти\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=10
min_rounds=2
max_rounds=4

[buff_3]

type="same_rank_multiply"
description_template={
    "zh_CN": "同点数 ×%d",
    "en": "Same Rank ×%d",
    "ja": "同じ数字 ×%d",
    "ko": "같은 숫자 ×%d",
    "ru": "Тот же ранг ×%d"
}
display_name={
    "zh_CN": "所向披靡",
    "en": "Unstoppable",
    "ja": "無敵の力",
    "ko": "막을 수 없는",
    "ru": "Неудержимый"
}
explanation_template={
    "zh_CN": "所有与本卡牌相同点数的牌\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "en": "All cards with the same rank as this card\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ja": "このカードと同じ数字のカード全て\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ko": "이 카드와 같은 숫자의 모든 카드\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ru": "Все карты с тем же рангом\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]"
}
base_value=2
weight=14
min_rounds=2
max_rounds=3

[buff_4]

type="same_type_multiply"
description_template={
    "zh_CN": "同花色 ×%d",
    "en": "Same Suit ×%d",
    "ja": "同じスート ×%d",
    "ko": "같은 무늬 ×%d",
    "ru": "Та же масть ×%d"
}
display_name={
    "zh_CN": "活络经血",
    "en": "Suit Power",
    "ja": "スートパワー",
    "ko": "무늬의 힘",
    "ru": "Сила масти"
}
explanation_template={
    "zh_CN": "所有与本卡牌相同花色的牌\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "en": "All cards with the same suit as this card\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ja": "このカードと同じスートのカード全て\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ko": "이 카드와 같은 무늬의 모든 카드\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]",
    "ru": "Все карты той же масти\n[img=32]res://assert/top/score.png[/img][outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]"
}
base_value=2
weight=10
min_rounds=2
max_rounds=3

[buff_5]

type="action_points_add"
description_template={
    "zh_CN": "行动点 +%d",
    "en": "Action Points +%d",
    "ja": "アクションポイント +%d",
    "ko": "행동 포인트 +%d",
    "ru": "Очки действий +%d"
}
display_name={
    "zh_CN": "行动加成",
    "en": "Action Boost",
    "ja": "行動力アップ",
    "ko": "행동력 증가",
    "ru": "Усиление действий"
}
explanation_template={
    "zh_CN": "[img=32]res://assert/top/ap.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "en": "[img=32]res://assert/top/ap.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ja": "[img=32]res://assert/top/ap.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ko": "[img=32]res://assert/top/ap.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ru": "[img=32]res://assert/top/ap.png[/img][outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=8
min_rounds=1
max_rounds=1

[buff_6]

type="high_card_add"
description_template={
    "zh_CN": "单张牌型 +%d",
    "en": "High Card +%d",
    "ja": "ハイカード +%d",
    "ko": "하이카드 +%d",
    "ru": "Старшая карта +%d"
}
display_name={
    "zh_CN": "单张加成",
    "en": "High Card Boost",
    "ja": "ハイカードボーナス",
    "ko": "하이카드 보너스",
    "ru": "Усиление карты"
}
explanation_template={
    "zh_CN": "单张牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "High Card\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "ハイカード\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "하이카드\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Старшая карта\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=1
min_rounds=1
max_rounds=1

[buff_7]

type="pair_add"
description_template={
    "zh_CN": "对子牌型 +%d",
    "en": "Pair +%d",
    "ja": "ペア +%d",
    "ko": "페어 +%d",
    "ru": "Пара +%d"
}
display_name={
    "zh_CN": "对子加成",
    "en": "Pair Boost",
    "ja": "ペアボーナス",
    "ko": "페어 보너스",
    "ru": "Усиление пары"
}
explanation_template={
    "zh_CN": "对子牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "Pair\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "ペア\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "페어\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Пара\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=5
weight=1
min_rounds=1
max_rounds=1

[buff_8]

type="straight_add"
description_template={
    "zh_CN": "顺子牌型 +%d",
    "en": "Straight +%d",
    "ja": "ストレート +%d",
    "ko": "스트레이트 +%d",
    "ru": "Стрит +%d"
}
display_name={
    "zh_CN": "顺子加成",
    "en": "Straight Boost",
    "ja": "ストレートボーナス",
    "ko": "스트레이트 보너스",
    "ru": "Усиление стрита"
}
explanation_template={
    "zh_CN": "顺子牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "Straight\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "ストレート\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "스트레이트\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Стрит\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=6
weight=1
min_rounds=1
max_rounds=1

[buff_9]

type="flush_add"
description_template={
    "zh_CN": "金花牌型 +%d",
    "en": "Flush +%d",
    "ja": "フラッシュ +%d",
    "ko": "플러시 +%d",
    "ru": "Флеш +%d"
}
display_name={
    "zh_CN": "金花加成",
    "en": "Flush Boost",
    "ja": "フラッシュボーナス",
    "ko": "플러시 보너스",
    "ru": "Усиление флеша"
}
explanation_template={
    "zh_CN": "金花牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "Flush\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "フラッシュ\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "플러시\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Флеш\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=8
weight=1
min_rounds=1
max_rounds=1

[buff_10]

type="straight_flush_add"
description_template={
    "zh_CN": "顺金牌型 +%d",
    "en": "Straight Flush +%d",
    "ja": "ストレートフラッシュ +%d",
    "ko": "스트레이트 플러시 +%d",
    "ru": "Стрит-флеш +%d"
}
display_name={
    "zh_CN": "顺金加成",
    "en": "Straight Flush Boost",
    "ja": "ストレートフラッシュボーナス",
    "ko": "스트레이트 플러시 보너스",
    "ru": "Усиление стрит-флеша"
}
explanation_template={
    "zh_CN": "顺金牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "Straight Flush\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "ストレートフラッシュ\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "스트레이트 플러시\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Стрит-флеш\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=8
weight=1
min_rounds=1
max_rounds=1

[buff_11]

type="three_kind_add"
description_template={
    "zh_CN": "豹子牌型 +%d",
    "en": "Three of a Kind +%d",
    "ja": "スリーカード +%d",
    "ko": "트리플 +%d",
    "ru": "Тройка +%d"
}
display_name={
    "zh_CN": "豹子加成",
    "en": "Three of a Kind Boost",
    "ja": "スリーカードボーナス",
    "ko": "트리플 보너스",
    "ru": "Усиление тройки"
}
explanation_template={
    "zh_CN": "豹子牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "Three of a Kind\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "スリーカード\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "트리플\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "Тройка\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=8
weight=1
min_rounds=1
max_rounds=1

[buff_12]

type="vip_add"
description_template={
    "zh_CN": "VIP牌型 +%d",
    "en": "VIP Hand +%d",
    "ja": "VIPハンド +%d",
    "ko": "VIP 패 +%d",
    "ru": "VIP комбинация +%d"
}
display_name={
    "zh_CN": "VIP加成",
    "en": "VIP Boost",
    "ja": "VIPボーナス",
    "ko": "VIP 보너스",
    "ru": "VIP усиление"
}
explanation_template={
    "zh_CN": "VIP牌型\n永久加成[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "en": "VIP Hand\nPermanent Bonus[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ja": "VIPハンド\n永続ボーナス[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ko": "VIP 패\n영구 보너스[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]",
    "ru": "VIP комбинация\nПостоянный бонус[outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]"
}
base_value=10
weight=1
min_rounds=1
max_rounds=1

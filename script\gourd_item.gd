extends Control

signal gourd_selected(index: int)
signal gourd_deselected(index: int)

@onready var floating_container = $FloatingContainer
@onready var gourd_item = $FloatingContainer/GourdTexture
@onready var shine = $Shine
@onready var selection_panel = $SelectionPanel
@onready var buff_name = $FloatingContainer/BuffName
@onready var tooltip = $HoverTooltip

var index: int = -1
var buff_data = null
var is_selected: bool = false
var is_hovered: bool = false

func _ready():
    # 初始隐藏选择框和提示框
    shine.visible = false
    selection_panel.visible = false
    tooltip.z_index = 150
    tooltip.hide()
    
    # 设置初始缩放
    floating_container.scale = Vector2(0.8, 0.8)
    
    # 连接鼠标事件
    floating_container.mouse_entered.connect(_on_mouse_entered)
    floating_container.mouse_exited.connect(_on_mouse_mouse_exited)
    floating_container.gui_input.connect(_on_gui_input)

# 设置BUFF数据
func setup(idx: int, data: Dictionary):
    index = idx
    buff_data = data

    if buff_data:
        gourd_item.texture = load(buff_data.card_pic)
        buff_name.text = buff_data.name
        # 动态调整文字大小以适应容器
        _adjust_text_size()
        # 更新提示框内容
        tooltip.update_tooltip(buff_data.description)

# 动态调整文字大小以适应容器
func _adjust_text_size():
    if not buff_name:
        return

    # 获取容器的可用宽度
    var container_width = buff_name.size.x
    if container_width <= 0:
        # 如果容器宽度为0，等待下一帧再尝试
        await get_tree().process_frame
        container_width = buff_name.size.x

    # 如果仍然无法获取宽度，使用默认值
    if container_width <= 0:
        container_width = 120  # 默认宽度

    # 获取当前文本
    var text = buff_name.text
    if text.is_empty():
        return

    # 设置字体大小范围
    var min_font_size = 10       # 最小字体大小
    var max_font_size = 18       # 最大字体大小
    var default_font_size = 14   # 默认字体大小

    # 获取当前字体
    var font = buff_name.get_theme_font("font")
    if not font:
        # 如果没有自定义字体，使用默认字体
        font = ThemeDB.fallback_font

    # 首先尝试默认字体大小
    var text_size = font.get_string_size(text, HORIZONTAL_ALIGNMENT_LEFT, -1, default_font_size)
    var available_width = container_width - 8  # 留8像素边距

    var best_font_size = default_font_size

    if text_size.x <= available_width:
        # 如果默认大小合适，尝试使用更大的字体
        for font_size in range(default_font_size + 1, max_font_size + 1):
            text_size = font.get_string_size(text, HORIZONTAL_ALIGNMENT_LEFT, -1, font_size)
            if text_size.x <= available_width:
                best_font_size = font_size
            else:
                break
    else:
        # 如果默认大小太大，使用更小的字体
        for font_size in range(default_font_size - 1, min_font_size - 1, -1):
            text_size = font.get_string_size(text, HORIZONTAL_ALIGNMENT_LEFT, -1, font_size)
            if text_size.x <= available_width:
                best_font_size = font_size
                break

        # 如果最小字体仍然太大，使用最小字体并启用自动换行
        if best_font_size == default_font_size:
            best_font_size = min_font_size
            buff_name.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

    # 应用最佳字体大小
    buff_name.add_theme_font_size_override("normal_font_size", best_font_size)

# 选中此葫芦
func select():
    is_selected = true
    shine.visible = true
    selection_panel.visible = true
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(floating_container, "scale", Vector2(1.0, 1.0), 0.3)

# 取消选中
func deselect():
    is_selected = false
    shine.visible = false
    selection_panel.visible = false
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(floating_container, "scale", Vector2(0.8, 0.8), 0.3)

# 缩小未选中的葫芦
func shrink():
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(floating_container, "scale", Vector2(0.6, 0.6), 0.3)

# 恢复正常大小
func restore():
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(floating_container, "scale", Vector2(0.8, 0.8), 0.3)

# 鼠标进入
func _on_mouse_entered():
    is_hovered = true
    if not is_selected:
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(floating_container, "scale", Vector2(0.9, 0.9), 0.3)
    # 显示提示框
    tooltip.show_tooltip()

# 鼠标离开
func _on_mouse_mouse_exited():
    is_hovered = false
    if not is_selected:
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(floating_container, "scale", Vector2(0.8, 0.8), 0.3)
    # 隐藏提示框
    tooltip.hide_tooltip()

# 输入处理
func _on_gui_input(event):
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        if is_selected:
            emit_signal("gourd_deselected", index)
        else:
            emit_signal("gourd_selected", index) 

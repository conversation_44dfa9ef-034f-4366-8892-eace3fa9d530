[gd_scene load_steps=6 format=3 uid="uid://bqcgbkxgv4q4k"]

[ext_resource type="Texture2D" uid="uid://bqv3c4hm5v2j5" path="res://assert/items/coin.png" id="1_1u5p4"]
[ext_resource type="Script" path="res://script/coin_vortex_particles.gd" id="2_vortex"]

[sub_resource type="Gradient" id="Gradient_a8rkx"]
offsets = PackedFloat32Array(0, 0.2, 0.8, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="Curve" id="Curve_qvjd5"]
_data = [Vector2(0, 0), 0.0, 2.0, 0, 0, Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_wgvr5"]
emission_shape = 3
emission_box_extents = Vector3(300, 200, 1)
particle_flag_disable_z = true
direction = Vector3(-1, -1, 0)
spread = 30.0
gravity = Vector3(0, 0, 0)
initial_velocity_min = 30.0
initial_velocity_max = 100.0
orbit_velocity_min = 0.0
orbit_velocity_max = 0.0
radial_accel_min = 50.0
radial_accel_max = 100.0
tangential_accel_min = 30.0
tangential_accel_max = 50.0
damping_min = 10.0
damping_max = 20.0
attractor_interaction_enabled = false
scale_min = 0.05
scale_max = 0.15
scale_curve = SubResource("Curve_qvjd5")
color = Color(1, 0.85098, 0.4, 1)
color_ramp = SubResource("Gradient_a8rkx")
hue_variation_min = -0.05
hue_variation_max = 0.05
turbulence_enabled = true
turbulence_noise_strength = 5.0
turbulence_noise_scale = 2.0
attractor_interaction_enabled = true

[node name="CoinVortexParticles" type="GPUParticles2D"]
emitting = false
amount = 30
process_material = SubResource("ParticleProcessMaterial_wgvr5")
texture = ExtResource("1_1u5p4")
lifetime = 2.0
preprocess = 0.5
speed_scale = 1.5
fixed_fps = 60
visibility_rect = Rect2(-500, -500, 1000, 1000)
local_coords = true
script = ExtResource("2_vortex") 
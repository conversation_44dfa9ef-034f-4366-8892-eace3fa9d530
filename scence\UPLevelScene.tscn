[gd_scene load_steps=13 format=3 uid="uid://bal47pdc0c2xk"]

[ext_resource type="Texture2D" uid="uid://doycu17aeh840" path="res://assert/items/RainBow.png" id="1_dsnq2"]
[ext_resource type="Script" uid="uid://kvjghd13nci6" path="res://script/up_level_scene.gd" id="1_xtw4r"]
[ext_resource type="Shader" uid="uid://da7c6bgld8c40" path="res://shader/rainbow_shadow.gdshader" id="2_333br"]
[ext_resource type="Theme" uid="uid://c5v7o2hfj6n4w" path="res://themes/game_theme.tres" id="2_ux21x"]
[ext_resource type="Texture2D" uid="uid://c1ywdgaet6hf" path="res://assert/items/duck_girta.png" id="4_333br"]
[ext_resource type="Shader" uid="uid://hbp4kqtp2bsa" path="res://shader/rain_shader.gdshader" id="5_vm4ea"]

[sub_resource type="Animation" id="Animation_8lfwr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Left:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Right2:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}

[sub_resource type="Animation" id="Animation_333br"]
resource_name = "animation"
length = 0.3
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Left:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-1344, 0), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Right2:position")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(648, 0), Vector2(0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_vm4ea"]
_data = {
&"RESET": SubResource("Animation_8lfwr"),
&"animation": SubResource("Animation_333br")
}

[sub_resource type="ShaderMaterial" id="ShaderMaterial_f3dv0"]
shader = ExtResource("5_vm4ea")
shader_parameter/rain_amount = 30.0
shader_parameter/near_rain_length = 0.2
shader_parameter/far_rain_length = 0.1
shader_parameter/near_rain_width = 1.0
shader_parameter/far_rain_width = 0.1
shader_parameter/near_rain_transparency = 0.5
shader_parameter/far_rain_transparency = 0.1
shader_parameter/rain_color = Color(1, 1, 1, 1)
shader_parameter/base_rain_speed = 0.1
shader_parameter/additional_rain_speed = 0.1
shader_parameter/slant = 0.5

[sub_resource type="CanvasTexture" id="CanvasTexture_vm4ea"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_8lfwr"]
shader = ExtResource("2_333br")
shader_parameter/strength = 0.7
shader_parameter/speed = 1.0
shader_parameter/angle = 15.0

[node name="UpLevelScene" type="CanvasLayer"]
script = ExtResource("1_xtw4r")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control"]
libraries = {
&"": SubResource("AnimationLibrary_vm4ea")
}

[node name="Left" type="Control" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TextureRect" type="TextureRect" parent="Control/Left"]
modulate = Color(1, 1, 1, 0.223529)
material = SubResource("ShaderMaterial_f3dv0")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 648.0
offset_right = -504.0
offset_bottom = 1150.0
grow_horizontal = 2
grow_vertical = 2
rotation = 4.71239
texture = SubResource("CanvasTexture_vm4ea")

[node name="RainBow" type="TextureRect" parent="Control/Left"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -152.0
offset_top = 306.0
offset_right = 180.0
offset_bottom = 306.0
grow_horizontal = 2
grow_vertical = 2
rotation = -0.261799
pivot_offset = Vector2(576, 324)
texture = ExtResource("1_dsnq2")
expand_mode = 1
stretch_mode = 5

[node name="Label" type="Label" parent="Control/Left"]
material = SubResource("ShaderMaterial_8lfwr")
layout_mode = 0
offset_left = 24.0
offset_top = 424.0
offset_right = 542.0
offset_bottom = 705.0
rotation = -0.261799
theme = ExtResource("2_ux21x")
theme_override_font_sizes/font_size = 200
text = "关卡2"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Right2" type="Control" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Shodow" type="TextureRect" parent="Control/Right2"]
modulate = Color(0, 0, 0, 0.443137)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 627.0
offset_top = 112.0
offset_right = -109.0
offset_bottom = -144.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("4_333br")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="Right" type="TextureRect" parent="Control/Right2"]
modulate = Color(0.899258, 0.899258, 0.899258, 1)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 609.0
offset_top = 128.0
offset_right = -127.0
offset_bottom = -128.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("4_333br")
expand_mode = 1
stretch_mode = 5
flip_h = true

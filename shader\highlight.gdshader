shader_type canvas_item;

uniform vec4 highlight_color : source_color = vec4(1.0, 1.0, 0.0, 0.3);
uniform float outline_width : hint_range(0, 10) = 2.0;
uniform float pulse_speed : hint_range(0.1, 5.0) = 1.0;

void fragment() {
    vec4 original_color = texture(TEXTURE, UV);
    
    // 创建脉冲效果
    float pulse = (sin(TIME * pulse_speed) + 1.0) * 0.5;
    vec4 final_highlight_color = highlight_color;
    final_highlight_color.a *= pulse;
    
    // 混合原始颜色和高亮颜色
    COLOR = mix(original_color, final_highlight_color, final_highlight_color.a);
    
    // 添加边缘高亮
    vec2 size = 1.0/TEXTURE_PIXEL_SIZE;
    float outline = 0.0;
    
    for(float x = -outline_width; x <= outline_width; x++) {
        for(float y = -outline_width; y <= outline_width; y++) {
            vec2 outline_uv = UV + vec2(x, y) * TEXTURE_PIXEL_SIZE;
            if (outline_uv != UV && texture(TEXTURE, outline_uv).a > 0.0) {
                outline = 1.0;
                break;
            }
        }
    }
    
    if (outline > 0.0 && original_color.a < 0.1) {
        COLOR = mix(COLOR, final_highlight_color, outline);
    }
} 
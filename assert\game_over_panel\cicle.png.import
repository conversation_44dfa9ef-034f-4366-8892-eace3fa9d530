[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ckkoyeo0t16c7"
path="res://.godot/imported/cicle.png-c6473e2c06b6549cad91f68a0f13ee90.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assert/game_over_panel/cicle.png"
dest_files=["res://.godot/imported/cicle.png-c6473e2c06b6549cad91f68a0f13ee90.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1

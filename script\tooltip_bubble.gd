extends Panel

# 提示气泡 - 用于显示游戏内提示信息

# 节点引用
@onready var message_label = $MarginContainer/MessageLabel
@onready var timer = $Timer

# 信号
signal tooltip_hidden

# 初始化
func _ready():
    # 初始隐藏
    visible = false
    
    # 连接计时器信号
    timer.timeout.connect(_on_timer_timeout)

# 显示提示信息
func show_tooltip(message: String, duration: float = 0.5):
    # 设置提示文本
    message_label.text = message
    
    # 显示面板
    visible = true
    
    # 播放错误音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/error.wav")
    
    # 设置计时器
    timer.wait_time = duration
    timer.start()
    
    # 添加动画效果
    modulate.a = 0
    var tween = create_tween()
    tween.tween_property(self, "modulate:a", 1.0, 0.1)

# 隐藏提示
func hide_tooltip():
    # 创建淡出动画
    var tween = create_tween()
    tween.tween_property(self, "modulate:a", 0.0, 0.1)
    tween.tween_callback(func(): 
        visible = false
        emit_signal("tooltip_hidden")
    )

# 计时器超时处理
func _on_timer_timeout():
    hide_tooltip()

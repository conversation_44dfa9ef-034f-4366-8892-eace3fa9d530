[gd_scene load_steps=3 format=3 uid="uid://c8k2j5x7n6m4d"]

[ext_resource type="Script" uid="uid://s2igmml3b56f" path="res://script/tutorial_overlay.gd" id="1_overlay"]
[ext_resource type="Script" uid="uid://bth8ido62n4gm" path="res://script/dim_background.gd" id="2_dim_bg"]

[node name="TutorialOverlay" type="CanvasLayer"]
layer = 10
script = ExtResource("1_overlay")

[node name="DimBackground" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.7)
script = ExtResource("2_dim_bg")

[node name="TutorialPanel" type="Panel" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -150.0
offset_right = 250.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="MarginContainer" type="MarginContainer" parent="TutorialPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20

[node name="VBoxContainer" type="VBoxContainer" parent="TutorialPanel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TutorialText" type="RichTextLabel" parent="TutorialPanel/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(200, 20)
layout_mode = 2
size_flags_horizontal = 3
bbcode_enabled = true
text = "欢迎来到游戏！让我们开始教程吧！"
fit_content = true
scroll_active = false
text_direction = 1

[node name="NextButton" type="Button" parent="TutorialPanel/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10
text = "下一步"

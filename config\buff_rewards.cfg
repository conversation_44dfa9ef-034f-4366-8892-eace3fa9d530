[extra_dice]
name={
    "zh_CN": "额外骰子",
    "en": "Extra Dice",
    "ja": "追加ダイス",
    "ko": "추가 주사위",
    "ru": "Дополнительные кости"
}
description={
    "zh_CN": "掷出骰子数量 +%s",
    "en": "Number of dice rolled +%s",
    "ja": "ダイスの数 +%s",
    "ko": "주사위 개수 +%s",
    "ru": "Количество костей +%s"
}
icon="res://assert/sign/dice_count_or_rolls.png"
card_pic="res://assert/victory_panel/card_dice.png"
type="dice_count"
value=1
# 权重配置
initial_weight=10
max_selections=7
weight_decrease_start=4
weight_decrease_rate=2

[hand_limit]
name={
    "zh_CN": "手牌容量",
    "en": "Hand Capacity",
    "ja": "手札容量",
    "ko": "핸드 용량",
    "ru": "Вместимость руки"
}
description={
    "zh_CN": "保留手牌上限 +%s",
    "en": "Hand card limit +%s",
    "ja": "手札上限 +%s",
    "ko": "핸드 카드 한계 +%s",
    "ru": "Лимит карт в руке +%s"
}
icon="res://assert/sign/hand_limit.png"
card_pic="res://assert/victory_panel/card_poke.png"
type="hand_limit"
value=1
# 权重配置
initial_weight=10
max_selections=8
weight_decrease_start=999  # 永不降低权重
weight_decrease_rate=0

[start_score_20]
name={
    "zh_CN": "额外金币",
    "en": "Extra Coins",
    "ja": "追加コイン",
    "ko": "추가 코인",
    "ru": "Дополнительные монеты"
}
description={
    "zh_CN": "初始额外金币 +%s",
    "en": "Initial extra coins +%s",
    "ja": "初期追加コイン +%s",
    "ko": "초기 추가 코인 +%s",
    "ru": "Начальные дополнительные монеты +%s"
}
icon="res://assert/sign/score_add.png"
card_pic="res://assert/victory_panel/card_coin.png"
type="start_score"
value=20
# 权重配置
initial_weight=10
max_selections=999  # 无限制
weight_decrease_start=999  # 永不降低权重
weight_decrease_rate=0

[dice_chance]
name={
    "zh_CN": "骰子机会",
    "en": "Dice Chance",
    "ja": "ダイス機会",
    "ko": "주사위 기회",
    "ru": "Шанс костей"
}
description={
    "zh_CN": "掷骰子机会 +%s",
    "en": "Dice roll chances +%s",
    "ja": "ダイス振り機会 +%s",
    "ko": "주사위 굴리기 기회 +%s",
    "ru": "Шансы броска костей +%s"
}
icon="res://assert/sign/dice_count_or_rolls.png"
card_pic="res://assert/victory_panel/card_key.png"
type="dice_chance"
value=1
# 权重配置
initial_weight=10
max_selections=3
weight_decrease_start=999  # 权重不降低，第3次后直接为0
weight_decrease_rate=0

[double_gunner]
name={
    "zh_CN": "双枪手",
    "en": "Double Gunner",
    "ja": "ダブルガンナー",
    "ko": "더블 거너",
    "ru": "Двойной стрелок"
}
description={
    "zh_CN": "卡牌值翻倍概率 +%s%%",
    "en": "Card value double chance +%s%%",
    "ja": "カード値2倍確率 +%s%%",
    "ko": "카드 값 2배 확률 +%s%%",
    "ru": "Шанс удвоения значения карты +%s%%"
}
icon="res://assert/sign/score_add.png"
card_pic="res://assert/victory_panel/two_guns.png"
type="double_gunner"
value=20
# 权重配置
initial_weight=10
max_selections=5
weight_decrease_start=3
weight_decrease_rate=3

[demon_whisper]
name={
    "zh_CN": "恶魔低语",
    "en": "Demon Whisper",
    "ja": "悪魔の囁き",
    "ko": "악마의 속삭임",
    "ru": "Шепот демона"
}
description={
    "zh_CN": "每阶-%s分，每5阶+%s分",
    "en": "-%s points per stage, +%s points every 5 stages",
    "ja": "毎段階-%sポイント、5段階毎+%sポイント",
    "ko": "매 단계 -%s점, 5단계마다 +%s점",
    "ru": "-%s очков за этап, +%s очков каждые 5 этапов"
}
icon="res://assert/sign/score_add.png"
card_pic="res://assert/victory_panel/demon_card.png"
type="demon_whisper"
value=10
# 权重配置
initial_weight=10
max_selections=5
weight_decrease_start=1
weight_decrease_rate=2

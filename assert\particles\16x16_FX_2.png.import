[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://2w2uye3mhnr4"
path="res://.godot/imported/16x16_FX_2.png-23f8e2e23a968a0a46f11dd3a690516c.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assert/particles/16x16_FX_2.png"
dest_files=["res://.godot/imported/16x16_FX_2.png-23f8e2e23a968a0a46f11dd3a690516c.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1

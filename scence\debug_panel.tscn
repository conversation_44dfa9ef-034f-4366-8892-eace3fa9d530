[gd_scene load_steps=2 format=3 uid="uid://cqsdvuoy51up4"]

[ext_resource type="Script" uid="uid://beyu86ewwvvlp" path="res://script/debug_panel.gd" id="1_xxxxx"]

[node name="DebugPanel" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_xxxxx")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 10

[node name="Title" type="Label" parent="Panel/VBoxContainer"]
layout_mode = 2
text = "调试面板"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="LevelContainer" type="HBoxContainer" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Panel/VBoxContainer/LevelContainer"]
layout_mode = 2
text = "关卡数："

[node name="LevelInput" type="LineEdit" parent="Panel/VBoxContainer/LevelContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="LevelButton" type="Button" parent="Panel/VBoxContainer/LevelContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "确认"

[node name="RoundContainer" type="HBoxContainer" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Panel/VBoxContainer/RoundContainer"]
layout_mode = 2
text = "阶数："

[node name="RoundInput" type="LineEdit" parent="Panel/VBoxContainer/RoundContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="RoundButton" type="Button" parent="Panel/VBoxContainer/RoundContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "确认"

[node name="ActionPointsContainer" type="HBoxContainer" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Panel/VBoxContainer/ActionPointsContainer"]
layout_mode = 2
text = "行动点数："

[node name="ActionPointsInput" type="LineEdit" parent="Panel/VBoxContainer/ActionPointsContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="ActionPointsButton" type="Button" parent="Panel/VBoxContainer/ActionPointsContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "确认"

[node name="TotalScoreContainer" type="HBoxContainer" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Panel/VBoxContainer/TotalScoreContainer"]
layout_mode = 2
text = "总积分值："

[node name="TotalScoreInput" type="LineEdit" parent="Panel/VBoxContainer/TotalScoreContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="TotalScoreButton" type="Button" parent="Panel/VBoxContainer/TotalScoreContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "确认"

[node name="LimitScoreContainer" type="HBoxContainer" parent="Panel/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Panel/VBoxContainer/LimitScoreContainer"]
layout_mode = 2
text = "目标值："

[node name="LimitScoreInput" type="LineEdit" parent="Panel/VBoxContainer/LimitScoreContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="LimitScoreButton" type="Button" parent="Panel/VBoxContainer/LimitScoreContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "确认"

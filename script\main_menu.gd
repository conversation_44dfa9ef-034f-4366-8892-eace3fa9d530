extends Control

# 主菜单 - 处理游戏开始界面的逻辑

# 节点引用
@onready var start_button = $CenterContainer/VBoxContainer/StartButton
@onready var settings_button = $CenterContainer/VBoxContainer/SettingsButton
@onready var exit_button = $CenterContainer/VBoxContainer/ExitButton
@onready var settings_panel = $SettingsPanel
@onready var background = $"MainMenu#Background"
@onready var title_image = $CenterContainer/VBoxContainer/TitleContainer/TitleImage
@onready var title_shadow = $CenterContainer/VBoxContainer/TitleContainer/TitleShadow

# 卡牌落下效果相关变量
var card_textures = []
var falling_cards = []
var spawn_timer = 0.0
var spawn_interval = 0.8  # 生成卡牌的间隔时间
var max_cards = 30  # 最大卡牌数量

# 标题动画相关变量
var title_animation_timer = 0.0
var title_animation_interval = 10.0  # 标题动画间隔
var is_title_animating = false
var title_tween: Tween

# 按钮相关变量
var button_hover_sounds = {}
var button_click_sounds = {}

# 类型提示
@onready var UIManager := get_node("/root/UIManager") as Node

# 初始化
func _ready():
    # 清理可能残留的动画（从游戏场景返回时）
    var animation_manager = get_node_or_null("/root/AnimationManager")
    if animation_manager:
        animation_manager.stop_all_loop_animations()

    # 设置初始语言（可以从保存的设置中读取）
    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")
    if err == OK:
        var saved_locale = config.get_value("language", "locale", "en")
        TranslationServer.set_locale(saved_locale)
    else:
        TranslationServer.set_locale("en")

    # 清理可能存在的进度条
    _cleanup_progress_bar()
    
    # 初始化背景音乐播放器
    _setup_background_music()
    
    # 连接信号
    start_button.pressed.connect(_on_start_button_pressed)
    settings_button.pressed.connect(_on_settings_button_pressed)
    exit_button.pressed.connect(_on_exit_button_pressed)
    
    # 连接设置面板的返回信号
    if settings_panel and settings_panel.has_signal("return_pressed"):
        settings_panel.return_pressed.connect(_on_settings_panel_return)
    
    # 应用UI管理器主题和动画
    UIManager.init_scene_ui(self)
    
    # 播放背景动画
    _play_background_animation()
    
    # 加载卡牌纹理
    _load_card_textures()
    
    # 初始生成一些卡牌
    for i in range(10):
        _spawn_falling_card(true)
    
    # 设置按钮样式和交互效果
    _setup_buttons()
    
    # 更新按钮文本
    _update_button_texts()

# 处理输入事件
func _input(event):
    if event is InputEventKey and event.pressed and OS.is_debug_build():
        if event.keycode == KEY_F2:
            _toggle_fullscreen()
        elif event.keycode == KEY_F3:
            _toggle_main_wish_scenes()

# F3在主菜单和愿望清单之间切换
func _toggle_main_wish_scenes():
    # 通过场景文件名判断当前场景
    var current_scene_file = get_tree().current_scene.scene_file_path
    print("当前场景文件: ", current_scene_file)

    if current_scene_file.ends_with("main_menu.tscn"):
        # 当前在主菜单，切换到愿望清单
        _switch_to_wish_list()
    elif current_scene_file.ends_with("wish_list.tscn"):
        # 当前在愿望清单，切换到主菜单
        _switch_to_main_menu()
    else:
        # 如果在其他场景，默认切换到主菜单
        _switch_to_main_menu()

# 切换到愿望清单场景
func _switch_to_wish_list():
    print("从主菜单切换到愿望清单场景")

    # 停止背景音乐
    # if AudioManager:
    #     AudioManager.stop_music()

    # 切换到愿望清单场景
    get_tree().change_scene_to_file("res://scence/wish_list.tscn")

# 切换到主菜单场景
func _switch_to_main_menu():
    print("从愿望清单切换到主菜单场景")

    # 停止背景音乐
    # if AudioManager:
    #     AudioManager.stop_music()

    # 切换到主菜单场景
    get_tree().change_scene_to_file("res://scence/main_menu.tscn")

# 切换全屏显示
func _toggle_fullscreen():
    if DisplayServer.window_get_mode() == DisplayServer.WINDOW_MODE_FULLSCREEN:
        # 当前是全屏，切换到窗口模式
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
        print("切换到窗口模式")
    else:
        # 当前是窗口模式，切换到全屏
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
        print("切换到全屏模式")

# 更新按钮文本
func _update_button_texts():
    if start_button:
        start_button.text = tr("START_GAME")
    if settings_button:
        settings_button.text = tr("SETTINGS")
    if exit_button:
        exit_button.text = tr("EXIT_GAME")
    print("主菜单文本已更新为: ", TranslationServer.get_locale())  # 添加调试输出

# 物理过程更新
func _process(delta):
    # 更新卡牌生成计时器
    spawn_timer += delta
    if spawn_timer >= spawn_interval and falling_cards.size() < max_cards:
        spawn_timer = 0
        _spawn_falling_card()
    
    # 更新所有落下的卡牌
    for i in range(falling_cards.size() - 1, -1, -1):
        var card = falling_cards[i]
        if card != null and is_instance_valid(card):
            # 更新卡牌位置
            card.position.y += card.get_meta("fall_speed") * delta
            
            # 添加轻微的水平漂移
            card.position.x += sin(Time.get_ticks_msec() * 0.001 + card.get_instance_id()) * 0.5 * delta
            
            # 更新卡牌旋转 (只使用Z轴旋转，因为TextureRect只支持2D旋转)
            card.rotation_degrees += card.get_meta("rotation_speed").z * delta
            
            # 使用缩放和倾斜来模拟X和Y轴的旋转效果
            var skew = card.get_meta("skew")
            skew.x += card.get_meta("rotation_speed").x * 0.01 * delta
            skew.y += card.get_meta("rotation_speed").y * 0.01 * delta
            card.set_meta("skew", skew)
            
            # 应用倾斜效果 (通过变换属性模拟3D效果)
            # 注意：TextureRect没有直接的transform属性，使用其他属性模拟
            # 使用scale和rotation来模拟倾斜效果
            card.scale.y = 1.0 + skew.x * 0.1
            card.scale.x = 1.0 + skew.y * 0.1
            # 可以使用pivot_offset调整变换中心点
            card.pivot_offset = card.custom_minimum_size / 2
            
            # 更新卡牌翻转动画
            if card.has_meta("flip_progress"):
                card.set_meta("flip_progress", card.get_meta("flip_progress") + card.get_meta("flip_speed") * delta)
                var flip_progress = card.get_meta("flip_progress")
                
                # 翻转动画 - 使用正弦函数创建平滑的翻转效果
                var flip_factor = sin(flip_progress)
                card.scale.x = abs(flip_factor) * card.get_meta("original_scale").x
                
                # 当翻转到背面时切换纹理
                if flip_factor < 0 and not card.get_meta("is_flipped"):
                    # 加载圆角处理的卡牌背面
                    var back_texture = load("res://assert/cards/cardback02.png")
                    card.texture = back_texture
                    
                    # 为卡牌背面添加圆角效果
                    if not card.material:
                        var corner_material = ShaderMaterial.new()
                        corner_material.shader = load("res://shader/card_corner.gdshader")
                        corner_material.set_shader_parameter("corner_radius", 0.15)
                        card.material = corner_material
                    
                    card.set_meta("is_flipped", true)
                elif flip_factor >= 0 and card.get_meta("is_flipped"):
                    # 加载原始纹理并移除圆角效果
                    card.texture = load(card.get_meta("original_texture"))
                    card.material = null
                    card.set_meta("is_flipped", false)
                
                # 如果完成一个完整的翻转周期，重置或继续
                if flip_progress >= PI * 2:
                    card.set_meta("flip_progress", 0)
            
            # 检查是否超出屏幕底部
            if card.position.y > get_viewport_rect().size.y + 100:
                card.queue_free()
                falling_cards.remove_at(i)
        else:
            # 移除无效的卡牌引用
            falling_cards.remove_at(i)
    

# 播放背景动画
func _play_background_animation():
    if background and background.material:
        # 背景动画通过Shader实现，无需额外代码
        pass

# 播放标题动画
func _play_title_animation():
    if is_title_animating:
        return
    
    is_title_animating = true
    
    var title = title_image
    if title:
        # 设置标题为金色
        title.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))
        title.add_theme_font_size_override("font_size", 48)
        title.add_theme_constant_override("outline_size", 5)
        title.add_theme_color_override("font_outline_color", Color(1.0, 1.0, 1.0, 1.0))
        
        # 重置标题状态
        title.modulate.a = 0.0
        title.scale = Vector2(0.5, 0.5)
        title.pivot_offset = title.size / 2
        
        # 创建动画
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_ELASTIC)
        tween.tween_property(title, "scale", Vector2(1.2, 1.2), 0.8)
        tween.parallel().tween_property(title, "modulate:a", 1.0, 0.5)
        tween.tween_property(title, "scale", Vector2(1.0, 1.0), 0.4)
        tween.tween_callback(func(): is_title_animating = false)

# 设置按钮样式和交互效果
func _setup_buttons():
    # 创建卡通样式按钮
    _setup_button_style(start_button, "", Color(0.2, 0.4, 0.8, 0.8))
    _setup_button_style(settings_button, "", Color(0.2, 0.4, 0.8, 0.8))
    _setup_button_style(exit_button, "", Color(0.2, 0.4, 0.8, 0.8))
    
    # 添加按钮鼠标交互效果
    _setup_button_hover_effects(start_button)
    _setup_button_hover_effects(settings_button)
    _setup_button_hover_effects(exit_button)

# 设置按钮样式
func _setup_button_style(button, text, color):
    # 设置按钮大小
    button.custom_minimum_size = Vector2(170, 50)
    
    # 创建按钮样式
    var normal_style = StyleBoxFlat.new()
    normal_style.bg_color = color
    normal_style.corner_radius_top_left = 15
    normal_style.corner_radius_top_right = 15
    normal_style.corner_radius_bottom_left = 15
    normal_style.corner_radius_bottom_right = 15
    normal_style.border_width_left = 4
    normal_style.border_width_top = 4
    normal_style.border_width_right = 4
    normal_style.border_width_bottom = 4
    normal_style.border_color = Color(color.r + 0.2, color.g + 0.2, color.b + 0.2, 1.0)

    # 添加阴影效果
    normal_style.shadow_color = Color(0.0, 0.0, 0.0, 0.5)  # 半透明黑色阴影
    normal_style.shadow_size = 8  # 阴影大小
    normal_style.shadow_offset = Vector2(3, 3)  # 阴影偏移（右下方向）
    
    # 创建悬停样式
    var hover_style = normal_style.duplicate()
    hover_style.bg_color = Color(color.r + 0.1, color.g + 0.1, color.b + 0.1, 1.0)
    hover_style.border_color = Color(1.0, 1.0, 1.0, 1.0)
    # 悬停时阴影稍微增强
    hover_style.shadow_color = Color(0.0, 0.0, 0.0, 0.7)  # 更深的阴影
    hover_style.shadow_size = 10  # 稍大的阴影
    hover_style.shadow_offset = Vector2(4, 4)  # 稍大的偏移
    
    # 创建按下样式
    var pressed_style = normal_style.duplicate()
    pressed_style.bg_color = Color(color.r - 0.1, color.g - 0.1, color.b - 0.1, 1.0)
    pressed_style.border_color = Color(0.8, 0.8, 0.8, 1.0)
    pressed_style.border_width_bottom = 2  # 按下时底部边框变细
    # 按下时阴影减小，模拟按钮被压下的效果
    pressed_style.shadow_color = Color(0.0, 0.0, 0.0, 0.3)  # 更淡的阴影
    pressed_style.shadow_size = 4  # 更小的阴影
    pressed_style.shadow_offset = Vector2(1, 1)  # 更小的偏移
    
    # 应用样式到按钮
    button.add_theme_stylebox_override("normal", normal_style)
    button.add_theme_stylebox_override("hover", hover_style)
    button.add_theme_stylebox_override("pressed", pressed_style)
    
    # 设置字体样式
    button.add_theme_font_size_override("font_size", 24)
    button.add_theme_color_override("font_color", Color(1.0, 1.0, 1.0, 1.0))
    button.add_theme_color_override("font_hover_color", Color(1.0, 1.0, 1.0, 1.0))
    button.add_theme_color_override("font_pressed_color", Color(0.9, 0.9, 0.9, 1.0))
    button.add_theme_constant_override("outline_size", 2)
    button.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 0.5))
    
    # 设置旋转中心点
    button.pivot_offset = button.size / 2

# 设置按钮悬停和点击效果
func _setup_button_hover_effects(button):
    # 连接鼠标信号 (保留悬停效果)
    if not button.is_connected("mouse_entered", _on_button_mouse_entered):
        button.mouse_entered.connect(_on_button_mouse_entered.bind(button))
    if not button.is_connected("mouse_exited", _on_button_mouse_exited):
        button.mouse_exited.connect(_on_button_mouse_exited.bind(button))
    
    # 移除: UIManager 会处理 button_down
    #if not button.is_connected("button_down", _on_button_pressed):
    #   button.button_down.connect(_on_button_pressed.bind(button))
    # 移除: UIManager 不再处理 button_up 的恢复动画
    #if not button.is_connected("button_up", _on_button_released):
    #   button.button_up.connect(_on_button_released.bind(button))

# 加载卡牌纹理
func _load_card_textures():
    # 加载所有花色的卡牌
    var suits = ["club", "diamond", "heart", "spade"]
    var values = ["A", "2", "3", "4", "5", "6", "8", "9", "10", "J", "Q", "K"]
    
    for suit in suits:
        for value in values:
            var path = "res://assert/cards/" + suit + "/" + value + ".svg"
            if ResourceLoader.exists(path):
                card_textures.append(path)
    
    # 添加卡牌背面
    card_textures.append("res://assert/cards/cardback02.png")

# 生成落下的卡牌
func _spawn_falling_card(random_y = false):
    if card_textures.size() == 0:
        return
    
    # 创建TextureRect作为卡牌
    var card = TextureRect.new()
    var texture_path = card_textures[randi() % card_textures.size()]
    card.texture = load(texture_path)
    card.expand_mode = 1  # TextureRect.EXPAND_KEEP_SIZE
    card.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    
    # 如果是卡牌背面，应用圆角效果
    if texture_path.ends_with("cardback02.png"):
        var corner_material = ShaderMaterial.new()
        corner_material.shader = load("res://shader/card_corner.gdshader")
        corner_material.set_shader_parameter("corner_radius", 0.15)
        card.material = corner_material
    
    # 设置卡牌大小 (随机缩放)
    var scale_factor = randf_range(0.15, 0.3)
    card.custom_minimum_size = Vector2(120, 180) * scale_factor
    
    # 设置卡牌初始位置
    var screen_size = get_viewport_rect().size
    card.position.x = randf_range(0, screen_size.x - card.custom_minimum_size.x)
    card.position.y = randf_range(-screen_size.y, 0) if random_y else -card.custom_minimum_size.y
    
    # 设置卡牌旋转中心
    card.pivot_offset = card.custom_minimum_size / 2
    
    # 设置卡牌旋转角度 (只使用Z轴旋转，因为TextureRect只支持2D旋转)
    card.rotation_degrees = randf_range(-180, 180)
    
    # 设置卡牌旋转速度 (使用自定义属性存储)
    card.set_meta("rotation_speed", {
        "x": randf_range(-20, 20),
        "y": randf_range(-20, 20),
        "z": randf_range(-20, 20)
    })
    
    # 使用缩放和倾斜来模拟X和Y轴的旋转效果
    card.set_meta("skew", Vector2(0, 0))
    
    # 设置卡牌下落速度
    card.set_meta("fall_speed", randf_range(30, 80))
    
    # 设置卡牌翻转动画参数
    card.set_meta("original_texture", texture_path)
    card.set_meta("original_scale", Vector2(1.0, 1.0))
    card.set_meta("flip_progress", randf() * PI * 2) # 随机初始翻转进度
    card.set_meta("flip_speed", randf_range(0.5, 2.0)) # 随机翻转速度
    card.set_meta("is_flipped", false)
    
    # 添加阴影效果
    card.modulate = Color(1.0, 1.0, 1.0, 0.8)
    
    # 设置随机深度 (z_index)
    card.z_index = randi() % 10 - 5
    
    # 添加到场景
    add_child(card)
    falling_cards.append(card)

# 按钮鼠标进入事件
func _on_button_mouse_entered(button):
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.3)
    
    # 添加发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 10
    style.shadow_color = Color(1.0, 1.0, 1.0, 0.3)
    button.add_theme_stylebox_override("hover", style)
    
    # 播放悬停音效
    _play_button_hover_sound()

# 按钮鼠标离开事件
func _on_button_mouse_exited(button):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 移除发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 0
    button.add_theme_stylebox_override("hover", style)

# 按钮按下事件
func _on_button_pressed(button):
    # 创建按下动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BOUNCE)
    tween.tween_property(button, "scale", Vector2(0.9, 0.9), 0.1)
    
    # 播放点击音效
    _play_button_click_sound()
    
    # 添加粒子效果
    _play_button_particles(button)

# 按钮释放事件
func _on_button_released(button):
    # 创建释放动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)

# 播放按钮悬停音效
func _play_button_hover_sound():
    # 这里可以添加音效播放代码
    pass

# 播放按钮点击音效
func _play_button_click_sound():
    # 获取音频管理器
    AudioManager.play_button_click_sfx()

# 播放按钮粒子效果
func _play_button_particles(button):
    # 这里可以添加粒子效果代码
    # 简单的替代方案：创建一个快速的闪光效果
    var flash = ColorRect.new()
    flash.color = Color(1, 1, 1, 0.5)
    flash.size = button.size
    flash.position = Vector2(0, 0)
    button.add_child(flash)
    
    # 创建淡出动画
    var tween = create_tween()
    tween.tween_property(flash, "modulate:a", 0.0, 0.2)
    
    # 动画完成后删除闪光
    await tween.finished
    flash.queue_free()

# 开始游戏按钮点击
func _on_start_button_pressed():
    # 获取场景管理器，使用过渡动画切换到游戏场景
    var scene_manager = get_node("/root/SceneManager")
    if scene_manager:
        scene_manager.change_scene_with_transition("res://scence/game_scene_full.tscn")

# 设置按钮点击
func _on_settings_button_pressed():
    # 如果设置面板有show_panel方法，直接调用
    if $SettingsPanel.has_method("show_panel"):
        $SettingsPanel.show_panel()
    else:
        # 后备方案：使用UI管理器显示面板
        UIManager.create_panel_popup($SettingsPanel)

# 退出按钮点击
func _on_exit_button_pressed():
    # 退出游戏
    get_tree().quit()

# 处理设置面板返回按钮点击
func _on_settings_panel_return():
    # 隐藏设置面板 - 使用UI管理器的淡出动画
    UIManager.create_panel_fade_out($SettingsPanel)

func _setup_background_music():
    AudioManager.play_main_menu_music()

# 清理进度条
func _cleanup_progress_bar():
    # 查找所有可能的进度条
    var progress_nodes_to_check = [
        "/root/ProgressContainer",
        "/root/ProgressBar",
        "/root/RoundProgressContainer",
        "/root/RoundProgressBar"
    ]
    
    # 尝试直接清理已知路径的进度条
    for path in progress_nodes_to_check:
        var node = get_node_or_null(path)
        if node:
            node.queue_free()
            print("清理进度条: ", path)
    
    # 深度搜索场景树中的所有可能的进度条节点
    var scene_root = get_tree().root
    _recursively_find_and_remove_progress_bars(scene_root)
    
    # 等待一帧以确保清理已完成
    await get_tree().process_frame

# 递归搜索并删除进度条
func _recursively_find_and_remove_progress_bars(node):
    # 检查节点名称
    var node_name = node.name.to_lower()
    var is_progress_node = (
        "progress" in node_name or 
        "loading" in node_name or 
        "loadbar" in node_name or
        node.get_class() == "ProgressBar"
    )
    
    # 如果是进度条相关节点
    if is_progress_node:
        print("发现并清理进度条: ", node.name)
        node.queue_free()
        return
    
    # 检查所有子节点
    for i in range(node.get_child_count()):
        var child = node.get_child(i)
        _recursively_find_and_remove_progress_bars(child)

func _exit_tree():
    if title_tween:
        title_tween.kill()

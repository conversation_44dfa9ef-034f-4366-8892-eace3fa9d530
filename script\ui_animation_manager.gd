extends Node

# UI动画管理器 - 提供统一的UI动画效果

# 动画类型枚举
enum AnimationType {
    FADE_IN,
    FADE_OUT,
    POPUP,
    BUTTON_PRESS,
    SCALE_IN,
    SCALE_OUT,
    SLIDE_IN,
    SLIDE_OUT
}

# 按钮悬停效果
func apply_button_hover_effect(button):
    if not button:
        return
        
    # 为按钮添加鼠标进入/离开事件处理
    if not button.is_connected("mouse_entered", _on_button_mouse_entered):
        button.mouse_entered.connect(_on_button_mouse_entered.bind(button))
    if not button.is_connected("mouse_exited", _on_button_mouse_exited):
        button.mouse_exited.connect(_on_button_mouse_exited.bind(button))
    if not button.is_connected("pressed", _on_button_pressed):
        button.pressed.connect(_on_button_pressed.bind(button))

# 按钮鼠标进入事件
func _on_button_mouse_entered(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.3)
    
    # 播放声音效果
    _play_hover_sound()

# 按钮鼠标离开事件
func _on_button_mouse_exited(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)

# 按钮按下事件
func _on_button_pressed(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(0.95, 0.95), 0.1)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 播放按钮粒子效果
    _play_button_particles(button)
    
    # 播放声音效果
    _play_click_sound()

# 播放按钮粒子效果
func _play_button_particles(button):
    # 简单的替代方案：创建一个快速的闪光效果
    var flash = ColorRect.new()
    flash.color = Color(1, 1, 1, 0.5)
    flash.size = button.size
    flash.position = Vector2(0, 0)
    button.add_child(flash)
    
    # 创建淡出动画
    var tween = create_tween()
    tween.tween_property(flash, "modulate:a", 0.0, 0.2)
    
    # 动画完成后删除闪光
    await tween.finished
    flash.queue_free()

# 播放悬停声音
func _play_hover_sound():
    # 这里可以添加声音播放代码
    pass

# 播放点击声音
func _play_click_sound():
    # 获取音频管理器
    var audio_manager = get_node("/root/AudioManager")
    if audio_manager:
        audio_manager.play_button_click_sfx()

# 淡入动画
func fade_in(node, duration: float = 0.3):
    if not node:
        return
        
    # 设置初始透明度
    node.modulate.a = 0.0
    node.visible = true
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_property(node, "modulate:a", 1.0, duration)
    
    return tween

# 淡出动画
func fade_out(node, duration: float = 0.3):
    if not node:
        return
        
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_property(node, "modulate:a", 0.0, duration)
    
    # 动画完成后隐藏节点
    await tween.finished
    node.visible = false
    
    return tween

# 弹出动画
func popup(node, duration: float = 0.5):
    if not node:
        return
        
    # 设置初始状态
    node.modulate.a = 0.0
    node.scale = Vector2(0.5, 0.5)
    node.visible = true
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(node, "scale", Vector2(1.1, 1.1), duration * 0.6)
    tween.parallel().tween_property(node, "modulate:a", 1.0, duration * 0.4)
    tween.tween_property(node, "scale", Vector2(1.0, 1.0), duration * 0.4)
    
    return tween

# 关闭动画
func close(node, duration: float = 0.3):
    if not node:
        return
        
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(node, "scale", Vector2(0.8, 0.8), duration)
    tween.parallel().tween_property(node, "modulate:a", 0.0, duration)
    
    # 动画完成后隐藏节点
    await tween.finished
    node.visible = false
    node.scale = Vector2(1.0, 1.0)
    
    return tween

# 应用主题到节点及其所有子节点
func apply_theme_to_node(node, theme_resource):
    if not node or not theme_resource:
        return
        
    # 应用主题到当前节点
    node.theme = theme_resource
    
    # 递归应用到所有子节点
    for child in node.get_children():
        apply_theme_to_node(child, theme_resource)

# 初始化UI动画
func init_ui_animations(root_node, theme_resource):
    if not root_node:
        return
        
    # 应用主题
    apply_theme_to_node(root_node, theme_resource)
    
    # 为所有按钮添加悬停效果
    _setup_buttons(root_node)

# 设置所有按钮的效果
func _setup_buttons(node):
    # 如果是按钮，添加悬停效果
    if node is Button:
        apply_button_hover_effect(node)
        
        # 确保按钮有正确的缩放中心
        node.pivot_offset = node.size / 2
    
    # 递归处理所有子节点
    for child in node.get_children():
        _setup_buttons(child) 
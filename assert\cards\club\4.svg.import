[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://p3ph1cuht3f6"
path="res://.godot/imported/4.svg-575b15755a4f357ab3213d633916e06a.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://assert/cards/club/4.svg"
dest_files=["res://.godot/imported/4.svg-575b15755a4f357ab3213d633916e06a.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
svg/scale=1.0
editor/scale_with_editor_scale=false
editor/convert_colors_with_editor_theme=false

# 代码重构计划

## 当前问题

目前`game_scene_full.gd`文件过于庞大（超过1900行代码），包含了多种不同的功能逻辑混杂在一起，导致：

1. 代码难以维护和理解
2. 功能扩展困难
3. 逻辑耦合度高
4. 难以实现新功能（如卡牌附加效果和筹码卡牌）

## 重构目标

将`game_scene_full.gd`拆分为多个功能模块，实现关注点分离，提高代码可维护性和可扩展性。

## 重构方案

### 1. 核心游戏流程控制器 (game_scene_controller.gd)

保留在`game_scene_full.gd`中，但大幅精简，只负责：

- 游戏状态管理
- 阶流程控制
- 组件间通信协调
- UI元素的基本引用和初始化

### 2. 动画管理器 (animation_manager.gd)

新建文件，负责所有动画效果：

- 卡牌移动动画
- 卡牌交换动画
- 卡牌落地特效
- 积分变化动画
- 弃牌动画
- 抽牌动画
- 结算动画

### 3. 卡牌操作系统 (card_operation_system.gd)

扩展现有的`card_system.gd`或新建文件，负责：

- 卡牌拖拽逻辑
- 卡牌交换逻辑
- 卡牌位置管理
- 手牌和公共牌池的显示更新
- 新增：卡牌附加效果逻辑
- 新增：筹码卡牌逻辑

### 4. BUFF管理器 (buff_manager.gd)

扩展现有的`buff_manager.gd`，增强其功能：

- BUFF效果应用逻辑
- BUFF显示管理
- 新增：支持卡牌附加效果的BUFF类型

### 5. UI交互控制器 (ui_controller.gd)

新建文件，负责：

- 按钮事件处理
- 提示信息显示
- 面板切换逻辑
- 游戏界面元素更新

### 6. 骰子系统 (dice_system.gd)

从`dice_panel.gd`扩展或新建文件，负责：

- 骰子逻辑
- 骰子动画
- 骰子结果处理

## 实现步骤

1. 创建新的模块文件
2. 逐步将相关功能从`game_scene_full.gd`移至对应模块
3. 在`game_scene_full.gd`中引用这些模块并协调它们的工作
4. 确保所有信号连接正确
5. 测试每个模块的功能

## 新功能实现计划

### 卡牌附加效果

在`card_operation_system.gd`中实现：

- 添加卡牌附加机制（加法或乘法）
- 实现卡牌附加的视觉效果
- 在积分计算中考虑附加效果

### 筹码卡牌

在`card_operation_system.gd`中实现：

- 添加筹码卡牌类型
- 实现下阶积分翻倍但本阶积分减少的逻辑
- 添加筹码卡牌的视觉效果

## 预期收益

1. 代码更加模块化，易于维护
2. 功能扩展更加灵活
3. 逻辑清晰，减少bug
4. 为新功能提供清晰的实现路径
5. 提高游戏性能
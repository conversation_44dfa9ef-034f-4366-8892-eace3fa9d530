extends Node

signal language_changed

# 当前支持的语言列表
const SUPPORTED_LANGUAGES = ["en", "zh_CN", "ja", "ko", "ru"]

func _ready():
    # 初始化时发送一次信号，确保所有节点都能获得正确的语言设置
    language_changed.emit()

# 切换语言
func change_language(locale: String) -> void:
    if locale in SUPPORTED_LANGUAGES:
        TranslationServer.set_locale(locale)
        language_changed.emit()

# 获取当前语言
func get_current_language() -> String:
    return TranslationServer.get_locale() 

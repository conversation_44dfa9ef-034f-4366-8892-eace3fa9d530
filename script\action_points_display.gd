extends HBoxContainer

@onready var action_points_label = $ActionPointsLabel
@onready var reduction_hint = $ReductionHint
var max_points = 0
var current_points = 0

func _ready():
    # 初始化标签
    action_points_label.text = "0"
    # 初始化隐藏扣减提示
    reduction_hint.visible = false

# 设置最大行动点数并初始化显示
func set_max_points(points: int):
    max_points = points
    current_points = points
    _update_display()

# 更新行动点显示
func update_points(new_points: int):
    current_points = new_points
    _update_display()

# 更新显示
func _update_display():
    action_points_label.text = "%d" % [current_points]

# 显示扣减提示
func show_reduction_hint(amount: int):
    if amount <= 0:
        reduction_hint.visible = false
        return
        
    reduction_hint.text = "(-%d)" % amount
    reduction_hint.visible = true
    
    # 创建一个简单的出现动画
    reduction_hint.modulate.a = 0
    var tween = create_tween()
    tween.tween_property(reduction_hint, "modulate:a", 1.0, 0.2)

# 隐藏扣减提示
func hide_reduction_hint():
    reduction_hint.visible = false

# 播放消耗动画
func play_consume_animation(from_points: int, to_points: int):
    var tween = create_tween()
    
    # 创建文字颜色变化动画
    tween.tween_property(action_points_label, "modulate", Color(1, 0, 0, 1), 0.2)  # 变红
    tween.tween_property(action_points_label, "modulate", Color(1, 1, 1, 1), 0.3)  # 恢复正常
    
    # 更新显示的数值
    current_points = to_points
    _update_display()
    
    return tween 

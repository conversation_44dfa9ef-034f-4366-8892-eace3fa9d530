extends CanvasLayer

signal animation_completed

@onready var animation_player = $Control/AnimationPlayer
@onready var levelLabel = $Control/Left/Label

func _ready():
    # 初始化时隐藏场景
    visible = false

func play_level_transition(level: int):
    levelLabel.text = tr("LvlLabel") % level
    # 显示场景
    visible = true
    
    # 播放正向动画
    animation_player.play("animation")
    
    # 等待动画完成
    await animation_player.animation_finished
    
    AudioManager.play_sfx("res://assert/audio/sfx/successful3.wav")

    # 等待2秒
    await get_tree().create_timer(1.5).timeout
    
    # 反向播放动画
    animation_player.play_backwards("animation")
    
    # 等待反向动画完成
    await animation_player.animation_finished
    
    # 隐藏场景
    visible = false
    
    # 发出动画完成信号
    animation_completed.emit() 

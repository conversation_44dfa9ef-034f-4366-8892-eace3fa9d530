extends Node

# 光标管理器 - 实现卡通手形状的自定义光标及其动画效果

# 光标状态枚举
enum CursorState {
    NORMAL,      # 普通状态 - 指向左上角的小手
    HOVER,       # 悬停状态 - 在交互元素上方的小手
    CLICK,       # 点击状态 - 手指点击瞬间的动画
    PRESS,       # 按压状态 - 手指按住不放的状态
    GRAB,        # 抓取状态 - 准备抓取卡牌
    GRABBING,    # 抓住状态 - 正在抓取卡牌
    IDLE,        # 空闲状态 - 敲击桌面动画
    THINKING,    # 思考状态 - 手指点着下巴思考
    BEER         # 庆祝状态 - 啤酒瓶
}

# 光标区域类型
enum CursorArea {
    DEFAULT,      # 默认区域
    UI_ELEMENT,   # UI元素
    CARD_AREA,    # 卡牌区域
    HAND_AREA,    # 手牌区域
    DECK_AREA,    # 牌库区域
    BEER_AREA     # 啤酒区域
}

# 当前光标状态和区域
var current_state = CursorState.NORMAL
var current_area = CursorArea.DEFAULT
var is_mouse_pressed = false
var is_dragging = false

# 节点引用
var cursor_container: Node2D
var cursor_canvas_layer: CanvasLayer
var cursor_node: Sprite2D
var cursor_shadow: Sprite2D
var cursor_particles: GPUParticles2D
var cursor_light: PointLight2D

# 光标资源
var cursor_textures = {
    CursorState.NORMAL: preload("res://assert/cursor/one5.png"),
    CursorState.HOVER: preload("res://assert/cursor/five5.png"),  # 
    CursorState.CLICK: preload("res://assert/cursor/clickone5.png"),
    CursorState.PRESS: preload("res://assert/cursor/clickone5.png"),  # 新增按压状态的光标纹理
    CursorState.GRAB: preload("res://assert/cursor/catch5.png"),  # 暂时使用normal代替grab
    CursorState.GRABBING: preload("res://assert/cursor/catch5.png"),  # 使用原始的drag代替grabbing
    CursorState.BEER: preload("res://assert/items/beer02.png")
}

# 空闲动画帧 - 暂时使用已有资源
var idle_animation_frames = [
    preload("res://assert/cursor/two5.png")
]

# 思考动画帧 - 暂时使用已有资源
var thinking_animation_frames = [
    preload("res://assert/cursor/one5.png"),
    preload("res://assert/cursor/clickone5.png")
]

# 空闲计时器
var idle_timer = 0.0
var idle_timeout = 60.0  # N秒无操作后进入空闲状态
var thinking_timeout = 10.0  # N秒无操作后进入思考状态

# 动画计时器
var animation_timer = 0.0
var animation_frame = 0
var animation_speed = 1  # 每帧持续时间

# 浮动动画参数
var float_offset = Vector2.ZERO
var float_time = 0.0
var float_speed = 2.0
var float_amplitude = 3.0  # 增大浮动幅度

# 热点位置 - 不同状态下的光标中心点位置偏移
var hotspot = {
    CursorState.NORMAL: Vector2(3, 37),
    CursorState.HOVER: Vector2(3, 37),
    CursorState.CLICK: Vector2(3, 37),
    CursorState.PRESS: Vector2(3, 37),
    CursorState.GRAB: Vector2(3, 37),
    CursorState.GRABBING: Vector2(3, 37),
    CursorState.IDLE: Vector2(3, 37),
    CursorState.THINKING: Vector2(3, 37),
    CursorState.BEER: Vector2(16, 150)  # 为啤酒状态添加合适的热点位置
}

# 鼠标跟踪
var mouse_position = Vector2.ZERO
var last_mouse_position = Vector2.ZERO
var mouse_velocity = Vector2.ZERO
var mouse_idle_time = 0.0

# 动画补间对象
var active_tween: Tween

func _ready():
    # 隐藏系统光标
    Input.set_mouse_mode(Input.MOUSE_MODE_HIDDEN)
    
    # 创建光标容器节点
    cursor_container = Node2D.new()
    cursor_container.name = "CursorContainer"
    
    # 创建专门的光标画布层，确保永远在最上层
    cursor_canvas_layer = CanvasLayer.new()
    cursor_canvas_layer.name = "CursorLayer"
    cursor_canvas_layer.layer = 100  # 设置一个很高的层级值
    add_child(cursor_canvas_layer)
    cursor_canvas_layer.add_child(cursor_container)
    
    # 创建光标阴影
    cursor_shadow = Sprite2D.new()
    cursor_shadow.name = "CursorShadow"
    cursor_shadow.texture = cursor_textures[CursorState.NORMAL]
    cursor_shadow.centered = true
    cursor_shadow.offset = hotspot[CursorState.NORMAL]
    cursor_shadow.modulate = Color(0, 0, 0, 0.3)  # 半透明黑色
    cursor_shadow.position = Vector2(2, 2)  # 阴影偏移
    cursor_shadow.z_index = 0
    cursor_container.add_child(cursor_shadow)
    
    # 创建光标光效
    # cursor_light = PointLight2D.new()
    # cursor_light.name = "CursorLight"
    # cursor_light.texture = cursor_textures[CursorState.NORMAL]
    # cursor_light.color = Color(1, 1, 1, 0.2)
    # cursor_light.energy = 0.5
    # cursor_light.texture_scale = 1.2
    # cursor_light.position = Vector2(-5, -5)
    # cursor_light.z_index = 1
    # cursor_container.add_child(cursor_light)
    
    # 创建自定义光标节点
    cursor_node = Sprite2D.new()
    cursor_node.name = "CursorSprite"
    cursor_node.texture = cursor_textures[CursorState.NORMAL]
    cursor_node.centered = true
    cursor_node.offset = hotspot[CursorState.NORMAL]
    cursor_node.z_index = 2
    cursor_container.add_child(cursor_node)
    
    # 创建粒子效果
    cursor_particles = GPUParticles2D.new()
    cursor_particles.name = "CursorParticles"
    cursor_particles.z_index = 1
    cursor_particles.position = Vector2(16, 16)
    cursor_particles.emitting = false
    cursor_container.add_child(cursor_particles)
    
    # 设置默认状态
    set_cursor_state(CursorState.NORMAL)
    
    # 创建啤酒粒子效果
    # _setup_beer_particles()

# 添加啤酒粒子效果设置
func _setup_beer_particles():
    var beer_particles = GPUParticles2D.new()
    beer_particles.name = "BeerParticles"
    beer_particles.z_index = 3
    beer_particles.emitting = false
    beer_particles.amount = 50
    beer_particles.lifetime = 1.5
    beer_particles.explosiveness = 0.8
    beer_particles.randomness = 0.5
    beer_particles.one_shot = true
    
    var particle_material = ParticleProcessMaterial.new()
    particle_material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
    particle_material.emission_sphere_radius = 100.0
    particle_material.particle_flag_disable_z = true
    particle_material.direction = Vector3(0, -1, 0)
    particle_material.spread = 45.0
    particle_material.gravity = Vector3(0, 980, 0)
    particle_material.initial_velocity_min = 800.0
    particle_material.initial_velocity_max = 1000.0
    particle_material.scale_min = 5.0
    particle_material.scale_max = 8.0
    particle_material.color = Color(1, 0.9, 0.5, 0.8)  # 啤酒泡沫的颜色
    
    beer_particles.process_material = particle_material
    cursor_node.add_child(beer_particles)

# 播放啤酒特效
func play_beer_effect():
    var beer_particles = cursor_node.get_node("BeerParticles")
    if beer_particles and !beer_particles.emitting:
        beer_particles.emitting = true
    # 播放音效
    AudioManager.play_sfx("res://assert/audio/sfx/cheers.wav")
        

# 处理物理更新
func _process(delta):
    # 更新鼠标位置和速度
    last_mouse_position = mouse_position
    mouse_position = get_viewport().get_mouse_position()
    mouse_velocity = (mouse_position - last_mouse_position) / delta
    
    # 更新光标位置
    if cursor_container:
        cursor_container.global_position = mouse_position
    
    # 处理空闲计时
    if mouse_velocity.length() > 1.0 or Input.is_anything_pressed():
        idle_timer = 0.0
        mouse_idle_time = 0.0
        
        # 如果当前是空闲状态，切换回普通状态
        if current_state == CursorState.IDLE or current_state == CursorState.THINKING:
            set_cursor_state(CursorState.NORMAL)
    else:
        mouse_idle_time += delta
        idle_timer += delta
        
        # 根据空闲时间切换状态
        if idle_timer > thinking_timeout and idle_timeout > idle_timer:
            set_cursor_state(CursorState.THINKING)
        elif idle_timer > idle_timeout:
            set_cursor_state(CursorState.IDLE)
    
    # 处理鼠标按下/释放事件
    if Input.is_mouse_button_pressed(MOUSE_BUTTON_LEFT):
        if !is_mouse_pressed:
            is_mouse_pressed = true
            
            # 根据当前区域设置状态
            if current_area == CursorArea.CARD_AREA || current_area == CursorArea.HAND_AREA:
                set_cursor_state(CursorState.GRABBING)
            elif current_area == CursorArea.BEER_AREA:
                play_beer_effect()
            else:
                # 点击瞬间使用CLICK状态
                set_cursor_state(CursorState.CLICK)
                _play_click_animation()
                
                # 添加定时器，在短暂的动画后切换到PRESS状态
                await get_tree().create_timer(0.1).timeout
                if is_mouse_pressed && current_state == CursorState.CLICK:
                    set_cursor_state(CursorState.PRESS)
    elif is_mouse_pressed:
        is_mouse_pressed = false
        
        # 根据当前区域恢复状态
        if current_area == CursorArea.CARD_AREA || current_area == CursorArea.HAND_AREA:
            set_cursor_state(CursorState.HOVER)
        elif current_area == CursorArea.BEER_AREA:
            set_cursor_state(CursorState.BEER)
        else:
            set_cursor_state(CursorState.NORMAL)
    
    # 应用状态特定的动画效果
    _apply_state_animation(delta)

# 设置光标状态
func set_cursor_state(state):
    # 如果状态相同，不做改变
    if state == current_state:
        return
    
    # 取消当前活动的动画
    if active_tween and active_tween.is_valid():
        active_tween.kill()
    
    # 更新状态
    var previous_state = current_state
    current_state = state
    
    # 重置动画参数
    animation_timer = 0.0
    animation_frame = 0
    
    var cursor_scale = Vector2(1,1)
    var rotation = 0
    # 更新光标纹理和热点
    match state:
        CursorState.NORMAL, CursorState.HOVER, CursorState.CLICK, CursorState.PRESS, CursorState.GRAB, CursorState.GRABBING:
            if cursor_textures.has(state):
                cursor_node.texture = cursor_textures[state]
                cursor_shadow.texture = cursor_textures[state]
        CursorState.BEER:
            if cursor_textures.has(state):
                cursor_node.texture = cursor_textures[state]
                cursor_shadow.texture = cursor_textures[state]
                cursor_scale = Vector2(0.2, 0.2)
        CursorState.IDLE:
            if idle_animation_frames.size() > 0:
                cursor_node.texture = idle_animation_frames[0]
                cursor_shadow.texture = idle_animation_frames[0]
        CursorState.THINKING:
            if thinking_animation_frames.size() > 0:
                cursor_node.texture = thinking_animation_frames[0]
                cursor_shadow.texture = thinking_animation_frames[0]
    
    # 更新热点位置
    if hotspot.has(state):
        if state == CursorState.IDLE:
            # 为IDLE状态设置特殊的旋转中心点
            # 通过调整position和offset的组合来设置旋转中心
            cursor_node.position = Vector2(0, 37)  # 向下移动以设置旋转中心
            cursor_node.offset = Vector2(0, 0)     # 保持图像位置不变
            cursor_shadow.position = Vector2(2, 39) # 阴影位置相应调整
            cursor_shadow.offset = Vector2(2, 2)
        else:
            # 其他状态使用正常的热点位置
            cursor_node.position = Vector2.ZERO
            cursor_node.offset = hotspot[state]
            cursor_shadow.position = Vector2(2, 2)
            cursor_shadow.offset = hotspot[state]
    
    # 播放状态转换动画
    _play_state_transition_animation(previous_state, state)
    
    # 重置缩放和旋转
    cursor_node.scale = cursor_scale
    cursor_node.rotation = rotation
    cursor_shadow.scale = cursor_scale
    cursor_shadow.rotation = rotation

# 设置光标所在区域
func set_cursor_area(area):
    # 如果区域相同，不做改变
    if area == current_area:
        return
    
    # 更新区域
    var previous_area = current_area
    current_area = area
    
    # 根据区域更新状态
    match area:
        CursorArea.DEFAULT:
            if is_mouse_pressed and !is_dragging:
                # 如果鼠标正在按下，根据状态设置为CLICK或PRESS
                if current_state == CursorState.CLICK:
                    # 点击瞬间保持CLICK状态
                    pass
                else:
                    # 点击持续中设置为PRESS状态
                    set_cursor_state(CursorState.PRESS)
            elif !is_mouse_pressed and !is_dragging:
                set_cursor_state(CursorState.NORMAL)
        CursorArea.BEER_AREA:
                set_cursor_state(CursorState.BEER)
        CursorArea.UI_ELEMENT:
            if is_mouse_pressed and !is_dragging:
                # 如果鼠标正在按下，根据状态设置为CLICK或PRESS
                if current_state == CursorState.CLICK:
                    # 点击瞬间保持CLICK状态
                    pass
                else:
                    # 点击持续中设置为PRESS状态
                    set_cursor_state(CursorState.PRESS)
            elif !is_mouse_pressed:
                set_cursor_state(CursorState.HOVER)
        CursorArea.CARD_AREA:
            if is_mouse_pressed:
                print("CARD_AREA grabbing")
                set_cursor_state(CursorState.GRABBING)
            else:
                set_cursor_state(CursorState.GRAB)
        CursorArea.HAND_AREA:
            if is_mouse_pressed:
                set_cursor_state(CursorState.GRABBING)
            else:
                set_cursor_state(CursorState.HOVER)
        CursorArea.DECK_AREA:
            if is_mouse_pressed and !is_dragging:
                # 如果鼠标正在按下，根据状态设置为CLICK或PRESS
                if current_state == CursorState.CLICK:
                    # 点击瞬间保持CLICK状态
                    pass
                else:
                    # 点击持续中设置为PRESS状态
                    set_cursor_state(CursorState.PRESS)
            elif !is_mouse_pressed:
                set_cursor_state(CursorState.HOVER)

# 开始拖拽卡牌
func start_drag(_card_node = null):
    is_dragging = true
    set_cursor_state(CursorState.GRABBING)
    
    # 播放抓取粒子效果
    _play_particle_effect("grab")

# 结束拖拽卡牌
func end_drag(_card_node = null):
    is_dragging = false
    
    # 根据当前区域设置状态
    if current_area == CursorArea.CARD_AREA || current_area == CursorArea.HAND_AREA:
        set_cursor_state(CursorState.GRAB)
    else:
        set_cursor_state(CursorState.NORMAL)

# 应用状态特定的动画效果
func _apply_state_animation(delta):
    match current_state:
        CursorState.NORMAL, CursorState.BEER:
            # 取消浮动效果，将偏移重置为零
            float_offset = Vector2.ZERO
            
            # 应用偏移（现在为零）
            cursor_node.position = float_offset
            cursor_shadow.position = float_offset
            
            # 修复光标旋转问题
            var speed_factor = min(mouse_velocity.length() / 500.0, 1.0)
            # 使用鼠标速度的方向来决定旋转角度
            var direction = Vector2.ZERO
            if mouse_velocity.length() > 50.0:  # 添加阈值，避免微小移动引起旋转
                direction = mouse_velocity.normalized()
                var angle = direction.angle()
                var target_rotation = direction.x * 0.2 * speed_factor  # 使用x方向分量决定旋转方向
                cursor_node.rotation = lerp(cursor_node.rotation, target_rotation, 0.2)  # 增大插值因子使旋转更明显
                cursor_shadow.rotation = cursor_node.rotation
            else:
                # 速度太小时逐渐恢复到零旋转
                cursor_node.rotation = lerp(cursor_node.rotation, 0.0, 0.1)
                cursor_shadow.rotation = cursor_node.rotation
            
        CursorState.HOVER:
            # 悬停脉动效果
            float_time += delta * float_speed * 1.5
            var scale_factor = 1.0 + sin(float_time) * 0.05
            cursor_node.scale = Vector2(scale_factor, scale_factor)
            cursor_shadow.scale = Vector2(scale_factor, scale_factor)
            
            # 调整光效
            # cursor_light.energy = 0.8 + sin(float_time) * 0.2
        
        CursorState.CLICK:
            # 点击瞬间，动画由_play_click_animation处理
            pass
            
        CursorState.PRESS:
            # 按压状态 - 保持轻微缩小状态
            cursor_node.scale = Vector2(0.9, 0.9)
            cursor_shadow.scale = Vector2(0.9, 0.9)
            # 轻微下移，模拟按下效果
            cursor_node.position.y = 2
            cursor_shadow.position.y = 4
            
        CursorState.GRAB, CursorState.GRABBING:
            # 抓取时的脉动效果
            float_time += delta * float_speed * 2.0
            var pulse_factor = 1.0 + sin(float_time * 2.0) * 0.1
            cursor_node.scale = Vector2(pulse_factor, pulse_factor) * 0.9  # 稍微缩小
            cursor_shadow.scale = Vector2(pulse_factor, pulse_factor) * 0.9
            
            # 轻微旋转
            cursor_node.rotation = sin(float_time) * 0.05
            cursor_shadow.rotation = cursor_node.rotation
            
        CursorState.IDLE:
            # 不倒翁摇晃效果
            animation_timer += delta
            
            # 每2秒触发一次摇晃动画
            if animation_timer >= 2.0:
                animation_timer = 0.0
                
                # 创建不倒翁摇晃动画，以底部为中心点
                var wobble_tween = create_tween()
                wobble_tween.set_ease(Tween.EASE_IN_OUT)
                wobble_tween.set_trans(Tween.TRANS_SINE)
                
                # 从一侧到另一侧的多次摇晃，保持底部固定
                wobble_tween.tween_property(cursor_node, "rotation", 0.2, 0.2)
                wobble_tween.parallel().tween_property(cursor_shadow, "rotation", 0.2, 0.2)
                
                wobble_tween.tween_property(cursor_node, "rotation", -0.2, 0.4)
                wobble_tween.parallel().tween_property(cursor_shadow, "rotation", -0.2, 0.4)
                
                wobble_tween.tween_property(cursor_node, "rotation", 0.15, 0.3)
                wobble_tween.parallel().tween_property(cursor_shadow, "rotation", 0.15, 0.3)
                
                wobble_tween.tween_property(cursor_node, "rotation", -0.1, 0.2)
                wobble_tween.parallel().tween_property(cursor_shadow, "rotation", -0.1, 0.2)
                
                wobble_tween.tween_property(cursor_node, "rotation", 0, 0.1)
                wobble_tween.parallel().tween_property(cursor_shadow, "rotation", 0, 0.1)

        CursorState.THINKING:
            # 更新动画帧，加入间隔时间
            animation_timer += delta
            var thinking_frame_duration = animation_speed * 1.5  # 思考动画基础持续时间
            var thinking_pause_duration = 0.8  # 每帧之间的停顿时间
            
            # 计算当前帧的总持续时间（包括动画和停顿）
            var total_frame_duration = thinking_frame_duration + thinking_pause_duration
            
            if animation_timer >= total_frame_duration:
                animation_timer = 0.0
                animation_frame = (animation_frame + 1) % thinking_animation_frames.size()
                cursor_node.texture = thinking_animation_frames[animation_frame]
                cursor_shadow.texture = thinking_animation_frames[animation_frame]
                
                # 思考时的头部轻微晃动
                var tween = create_tween()
                tween.set_ease(Tween.EASE_IN_OUT)
                tween.set_trans(Tween.TRANS_SINE)
                tween.tween_property(cursor_node, "rotation", randf_range(-0.1, 0.1), 0.3)
                tween.parallel().tween_property(cursor_shadow, "rotation", randf_range(-0.1, 0.1), 0.3)

# 播放点击动画
func _play_click_animation():
    active_tween = create_tween()
    active_tween.set_ease(Tween.EASE_OUT)
    active_tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 缩小然后恢复
    active_tween.tween_property(cursor_node, "scale", Vector2(0.8, 0.8), 0.1)
    active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(0.8, 0.8), 0.1)
    active_tween.tween_property(cursor_node, "scale", Vector2(1.0, 1.0), 0.3)
    active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 播放点击粒子效果
    _play_particle_effect("click")

# 播放敲击动画
func _play_tap_animation(tap_distance = 10.0):
    active_tween = create_tween()
    active_tween.set_ease(Tween.EASE_OUT)
    active_tween.set_trans(Tween.TRANS_BOUNCE)
    
    # 下降然后弹回，使用参数控制下降距离
    active_tween.tween_property(cursor_node, "position:y", cursor_node.position.y + tap_distance, 0.1)
    active_tween.parallel().tween_property(cursor_shadow, "position:y", cursor_shadow.position.y + tap_distance, 0.1)
    active_tween.tween_property(cursor_node, "position:y", cursor_node.position.y, 0.2)
    active_tween.parallel().tween_property(cursor_shadow, "position:y", cursor_shadow.position.y, 0.2)
    
    # 播放敲击粒子效果
    _play_particle_effect("tap")

# 播放状态转换动画
func _play_state_transition_animation(from_state, to_state):
    # 从任何状态到NORMAL的转换
    if to_state == CursorState.NORMAL:
        active_tween = create_tween()
        active_tween.set_ease(Tween.EASE_OUT)
        active_tween.set_trans(Tween.TRANS_BACK)
        
        # 如果是从PRESS状态转换过来，添加弹起动画
        if from_state == CursorState.PRESS:
            active_tween.tween_property(cursor_node, "position:y", 0, 0.1)
            active_tween.parallel().tween_property(cursor_shadow, "position:y", 0, 0.1)
            active_tween.tween_property(cursor_node, "scale", Vector2(1.1, 1.1), 0.1)
            active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.1, 1.1), 0.1)
            active_tween.tween_property(cursor_node, "scale", Vector2(1.0, 1.0), 0.2)
            active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.0, 1.0), 0.2)
        else:
            # 轻微弹跳效果
            active_tween.tween_property(cursor_node, "scale", Vector2(1.2, 1.2), 0.2)
            active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.2, 1.2), 0.2)
            active_tween.tween_property(cursor_node, "scale", Vector2(1.0, 1.0), 0.3)
            active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 从NORMAL到PRESS的转换 (点击后切换到PRESS)
    elif to_state == CursorState.PRESS and from_state == CursorState.CLICK:
        # 这个转换由_process中的延时处理，不需要额外动画
        pass
    
    # 从任何状态到GRAB的转换
    elif to_state == CursorState.GRAB:
        active_tween = create_tween()
        active_tween.set_ease(Tween.EASE_OUT)
        active_tween.set_trans(Tween.TRANS_ELASTIC)
        
        # 手指张开的动画
        active_tween.tween_property(cursor_node, "scale", Vector2(1.3, 1.3), 0.2)
        active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.3, 1.3), 0.2)
        active_tween.tween_property(cursor_node, "scale", Vector2(1.0, 1.0), 0.3)
        active_tween.parallel().tween_property(cursor_shadow, "scale", Vector2(1.0, 1.0), 0.3)

# 播放粒子效果
func _play_particle_effect(effect_type):
    # 根据效果类型设置粒子参数
    match effect_type:
        "click":
            # 点击效果 - 小星星
            pass
        "grab":
            # 抓取效果 - 尘土
            pass
        "tap":
            # 敲击效果 - 圆环波纹
            pass
    
    # 重新启动粒子发射
    cursor_particles.restart()
    cursor_particles.emitting = true

# 将此脚本添加到游戏场景中并连接信号
func integrate_with_game_scene():
    # 尝试连接卡牌拖拽信号
    var parent = get_parent()
    if parent and parent.has_signal("card_drag_started") and parent.has_signal("card_drag_ended"):
        parent.card_drag_started.connect(start_drag)
        parent.card_drag_ended.connect(end_drag)
        print("光标管理器: 已连接到游戏场景的拖拽信号")
    else:
        print("光标管理器: 未找到游戏场景的拖拽信号")
        # 尝试查找游戏场景节点
        var game_scenes = get_tree().get_nodes_in_group("game_scene")
        if game_scenes.size() > 0:
            var game_scene = game_scenes[0]
            if game_scene.has_signal("card_drag_started") and game_scene.has_signal("card_drag_ended"):
                game_scene.card_drag_started.connect(start_drag)
                game_scene.card_drag_ended.connect(end_drag)
                print("光标管理器: 已连接到游戏场景组的拖拽信号")

# 检测光标是否在UI元素上方
func detect_ui_hover():
    # 在实际游戏中，这可以通过ray cast或控制节点的signal实现
    # 这里是一个简化的示例
    pass

# 注册区域
func register_area(node, area_type):
    if node:
        # 为节点添加鼠标进入/离开事件
        if !node.is_connected("mouse_entered", _on_area_mouse_entered):
            node.mouse_entered.connect(_on_area_mouse_entered.bind(area_type))
        if !node.is_connected("mouse_exited", _on_area_mouse_exited):
            node.mouse_exited.connect(_on_area_mouse_exited.bind(area_type))

# 区域鼠标进入事件
func _on_area_mouse_entered(area_type):
    set_cursor_area(area_type)

# 区域鼠标离开事件
func _on_area_mouse_exited(area_type):
    # 只有当前区域与离开的区域相同时才重置为默认
    if current_area == area_type:
        set_cursor_area(CursorArea.DEFAULT)

[gd_scene load_steps=4 format=3 uid="uid://riqfmggkqpfd"]

[ext_resource type="Theme" uid="uid://c5v7o2hfj6n4w" path="res://themes/game_theme.tres" id="1_uo5io"]
[ext_resource type="Script" uid="uid://d324go8ttsxmf" path="res://script/settings_menu_panel.gd" id="2_xxxxx"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.2, 0.2, 0.2, 0.8)
corner_radius_top_left = 20
corner_radius_top_right = 20
corner_radius_bottom_right = 20
corner_radius_bottom_left = 20
shadow_color = Color(0, 0, 0, 0.4)
shadow_size = 8

[node name="SettingsMenuPanel" type="Control"]
z_index = 80
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_uo5io")
script = ExtResource("2_xxxxx")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -128.0
offset_top = -128.0
offset_right = 128.0
offset_bottom = 120.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="PanelContainer" type="Panel" parent="CenterContainer"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_panel")

[node name="MarginContainer" type="MarginContainer" parent="CenterContainer/PanelContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = -100.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="SettingLabel" type="Label" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "SettingLabel"
horizontal_alignment = 1

[node name="RegionHintCheckBox" type="CheckBox" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
visible = false
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 4
button_pressed = true
text = "RegionHintCheckBox"
alignment = 1

[node name="QuickSettlementCheckBox" type="CheckBox" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "QuickSettlementCheckBox"
alignment = 1

[node name="RuleButton" type="Button" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "RuleButton"

[node name="SettingsButton" type="Button" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "SettingsButton"

[node name="ExitButton" type="Button" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "ExitButton"

[node name="ContinueButton" type="Button" parent="CenterContainer/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "ContinueButton"

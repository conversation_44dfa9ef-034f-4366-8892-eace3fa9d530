extends GPUParticles2D

@onready var wave_timer = $WaveTimer

var rng = RandomNumberGenerator.new()
var time_passed = 0.0
var current_color_shift = 0.0
var target_color_shift = 0.0
var color_shift_speed = 0.05

# 原始颜色值
var base_colors = [
    Color(0.0, 0.455, 0.463, 0.0),   # 透明青色
    Color(0.063, 0.616, 0.690, 0.725), # 亮青色
    Color(0.122, 0.545, 0.620, 0.553), # 中青色
    Color(0.137, 0.239, 0.482, 0.0)    # 透明蓝色
]

# 颜色变化目标
var target_colors = []
var current_colors = []

# 运动相关参数
var original_velocity_min = 20.0
var original_velocity_max = 40.0
var target_velocity_min = 20.0
var target_velocity_max = 40.0

func _ready():
    rng.randomize()
    wave_timer.timeout.connect(_on_wave_timer_timeout)
    
    # 初始化颜色数组
    for color in base_colors:
        current_colors.append(color)
        target_colors.append(color)
    
    # 开始波动
    _start_new_wave()

func _process(delta):
    time_passed += delta
    
    # 平滑过渡到目标颜色
    _update_colors(delta)
    
    # 调整速度参数
    _update_velocity(delta)

func _update_colors(delta):
    var material = process_material as ParticleProcessMaterial
    if material and material.color_ramp:
        var gradient = material.color_ramp.gradient as Gradient
        
        # 对每个颜色进行插值
        for i in range(current_colors.size()):
            current_colors[i] = current_colors[i].lerp(target_colors[i], color_shift_speed * delta * 10)
            
            # 更新梯度中的颜色
            if i < gradient.colors.size():
                gradient.colors[i] = current_colors[i]

func _update_velocity(delta):
    var material = process_material as ParticleProcessMaterial
    if material:
        # 平滑过渡速度
        material.initial_velocity_min = lerp(material.initial_velocity_min, target_velocity_min, delta)
        material.initial_velocity_max = lerp(material.initial_velocity_max, target_velocity_max, delta)
        
        # 随机调整湍流参数
        material.turbulence_noise_speed.x = lerp(material.turbulence_noise_speed.x, 
            rng.randf_range(0.1, 0.3), delta * 0.5)
        material.turbulence_noise_speed.y = lerp(material.turbulence_noise_speed.y, 
            rng.randf_range(0.3, 0.7), delta * 0.5)

func _on_wave_timer_timeout():
    _start_new_wave()

func _start_new_wave():
    # 随机选择新的颜色偏移
    var hue_shift = rng.randf_range(-0.2, 0.2)
    var saturation_shift = rng.randf_range(-0.1, 0.1)
    var value_shift = rng.randf_range(-0.1, 0.1)
    
    # 为每个颜色设定新的目标
    for i in range(base_colors.size()):
        var new_color = base_colors[i]
        var h = 0.0
        var s = 0.0
        var v = 0.0
        
        # 获取当前HSV值
        var temp_color = Color(new_color.r, new_color.g, new_color.b)
        h = temp_color.h
        s = temp_color.s
        v = temp_color.v
        
        # 应用偏移
        h = fmod(h + hue_shift, 1.0)
        s = clamp(s + saturation_shift, 0.0, 1.0)
        v = clamp(v + value_shift, 0.0, 1.0)
        
        # 转换回RGB并设置透明度
        temp_color = Color.from_hsv(h, s, v)
        new_color = Color(temp_color.r, temp_color.g, temp_color.b, new_color.a)
        
        target_colors[i] = new_color
    
    # 随机改变运动速度
    target_velocity_min = rng.randf_range(15.0, 25.0)
    target_velocity_max = rng.randf_range(35.0, 50.0)
    
    # 调整颜色变化速度
    color_shift_speed = rng.randf_range(0.03, 0.08)
    
    # 随机调整粒子发射数量
    amount = rng.randi_range(150, 250)
    
    # 随机调整粒子生命周期
    lifetime = rng.randf_range(8.0, 12.0)
    
    # 随机调整波动计时器时间
    wave_timer.wait_time = rng.randf_range(1.5, 3.5) 

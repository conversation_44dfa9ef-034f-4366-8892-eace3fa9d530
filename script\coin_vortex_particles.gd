extends GPUParticles2D

# 粒子效果控制脚本 - 控制金币漩涡效果

# 目标节点 - 金币将会被吸入这个节点
var target_node: Control = null
var is_active: bool = false
var particle_material: ParticleProcessMaterial

# 初始化
func _ready():
    # 获取粒子材质
    particle_material = process_material
    
    # 默认不发射粒子
    emitting = false
    
    # 确保节点可见但粒子不发射
    visible = true
    
# 设置目标节点 - 金币将被吸入的目标
func set_target(node: Control):
    target_node = node
    
    # 如果目标节点有效，更新粒子系统的参数
    if is_instance_valid(target_node):
        # 计算目标节点在粒子系统局部坐标系中的位置
        var local_target = to_local(target_node.global_position + target_node.size / 2)
        
        # 更新粒子系统的引力点位置
        if particle_material:
            # 设置引力点参数
            particle_material.attractor_interaction_enabled = true
            
            # 设置粒子的吸引力参数
            # 注意：Godot 4.x 中使用 radial_accel 和 tangential_accel 来模拟吸引效果
            # 负值的 radial_accel 会使粒子向中心点移动
            particle_material.radial_accel_min = -100.0
            particle_material.radial_accel_max = -50.0
            
            # 更新粒子运动方向，使其朝向目标
            particle_material.direction = Vector3(local_target.x, local_target.y, 0).normalized()

# 激活粒子效果
func activate():
    if not is_active:
        is_active = true
        emitting = true
        
        # 更新目标位置
        if is_instance_valid(target_node):
            set_target(target_node)

# 停用粒子效果
func deactivate():
    if is_active:
        is_active = false
        # 停止发射新粒子，但让现有粒子完成生命周期
        emitting = false

# 处理每一帧的更新
func _process(_delta):
    # 如果粒子系统处于活动状态且目标节点有效
    if is_active and is_instance_valid(target_node):
        # 持续更新目标位置，以便粒子能够跟随移动的目标
        set_target(target_node) 

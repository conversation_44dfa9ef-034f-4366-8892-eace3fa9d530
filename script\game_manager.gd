extends Node

# 游戏管理器 - 负责管理游戏的核心逻辑和状态

# 游戏状态枚举
enum GameState {
    MAIN_MENU,    # 主菜单
    PREPARATION,  # 准备阶段
    ACTION,       # 行动阶段
    SETTLEMENT,   # 结算阶段
    GAME_OVER     # 游戏结束
}

# 游戏配置
var current_state: int = GameState.MAIN_MENU
var current_round: int = 1
var total_score: int = 0
var reward_score: int = 0
var limit_score: int = 0
var action_points: int = 0
var max_action_points: int = 5
var dice_rolls_left: int = 3
var dice_rolls_chance: int = 3
var last_dice_result: Array = [0, 0, 0]
var dice_count: int = 3  # 默认骰子数量为3
const MAX_DICE_COUNT: int = 10  # 最大骰子数量限制
var hand_limit: int = 5  # 手牌数量限制
var has_rolled_for_level: bool = false  # 新增：跟踪当前关卡是否已经掷过骰子
var first_round_score: int = 20 # 第一阶的分数
var max_hand_limit: int = 11  # 手牌数量限制

# 关卡相关变量
var current_level: int = 1
var rounds_per_level: int = 10 # 第一关的阶数10
var additional_rounds_per_level: int = 3 # 每关增加的阶数3

var accumulated_limit_score: int = 0  # 累积扣减的限制积分总量
var current_limit_score: int = 0 # 本阶限制积分
var draw_card_aciton: int = 1 # 抽牌行动点数消耗
var change_pool_action: int = 5 # 替换公共牌（燃烧）行动点数消耗
var consume_score: bool = false # 是否消耗积分

# 禁用花色相关
var forbidden_suits: Array = []  # 存储被禁用的花色

# 双枪手相关
var double_gunner_chance: float = 0.0  # 双枪手概率（百分比，如5.0表示5%）

# 恶魔低语相关
var demon_whisper_stacks: int = 0  # 恶魔低语叠加次数
var demon_whisper_per_stage_penalty: int = 0  # 每阶扣分
var demon_whisper_bonus_per_5_stages: int = 0  # 每5阶奖励分

# 添加禁用花色
func add_forbidden_suit(suit: int):
    if not forbidden_suits.has(suit):
        forbidden_suits.append(suit)
        print("添加禁用花色: ", suit)

# 清理禁用花色
func clear_forbidden_suits():
    forbidden_suits.clear()
    print("清理所有禁用花色")

# 添加恶魔低语叠加
func add_demon_whisper_stack():
    demon_whisper_stacks += 1
    demon_whisper_per_stage_penalty = demon_whisper_stacks * 10
    demon_whisper_bonus_per_5_stages = demon_whisper_stacks * 100
    print("恶魔低语叠加次数: %d, 每阶扣分: %d, 每5阶奖励: %d" % [demon_whisper_stacks, demon_whisper_per_stage_penalty, demon_whisper_bonus_per_5_stages])

# 计算恶魔低语效果
func calculate_demon_whisper_effect() -> int:
    if demon_whisper_stacks == 0:
        return 0

    # 每阶扣分
    var stage_penalty = current_round * demon_whisper_per_stage_penalty

    # 每5阶奖励分
    var bonus_stages = current_round / 5  # 整数除法，获得完整的5阶组数
    var stage_bonus = bonus_stages * demon_whisper_bonus_per_5_stages

    var total_effect = stage_bonus - stage_penalty
    print("恶魔低语效果计算 - 当前阶: %d, 扣分: %d, 奖励: %d, 总效果: %d" % [current_round, stage_penalty, stage_bonus, total_effect])

    return total_effect

# 信号
signal game_state_changed(new_state)
signal round_changed(new_round)
signal score_changed(new_score, total_level_score, progress)
signal limit_score_changed(new_limit)
signal action_points_changed(new_points)
signal dice_rolled(dice_values)
signal dice_count_changed(new_count)
signal hand_limit_changed(new_limit)
signal dice_rolls_changed(rolls_left, max_rolls)  # 新增：骰子次数变化信号
signal level_changed(new_level)  # 新增: 关卡变化信号
signal level_completed(completed_level)  # 新增: 关卡完成信号
signal reset_game_rules  # 新增: 重置游戏规则信号

# 初始化
func _ready():
    pass

# 设置骰子数量
func set_dice_count(new_count: int):
    # 确保骰子数量在1到最大值之间
    new_count = clampi(new_count, 1, MAX_DICE_COUNT)
    
    # 更新骰子数量
    dice_count = new_count
    
    # 如果当前阶处于准备阶段，同时刷新可用次数
    if current_state == GameState.PREPARATION:
        dice_rolls_left = dice_rolls_chance
        # 发送骰子次数变化信号
        dice_rolls_changed.emit(dice_rolls_left, dice_rolls_chance)
    
    # 发出骰子数量变化信号
    dice_count_changed.emit(dice_count)

# 开始新游戏
func start_new_game():
    # 停止所有循环动画，防止Tween无限循环错误
    var animation_manager = get_node_or_null("/root/AnimationManager")
    if animation_manager:
        animation_manager.stop_all_loop_animations()

    current_round = 1
    current_level = 1
    total_score = 0
    action_points = 0  # 初始行动点数为0
    set_dice_count(3)  # 重置骰子数量为默认值3

    # 重置恶魔低语状态
    demon_whisper_stacks = 0
    demon_whisper_per_stage_penalty = 0
    demon_whisper_bonus_per_5_stages = 0

    # 重置奖励权重
    RewardWeightManager.reset_all_weights()

    # 重置游戏规则奖励分数
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager:
        buff_manager.reset_game_rules_bonus()

    has_rolled_for_level = false  # 重置掷骰子状态
    clear_forbidden_suits()  # 清理禁用花色
    double_gunner_chance = 0.0  # 重置双枪手概率

    # 获取游戏场景并清空卡牌
    var game_scene = get_tree().current_scene
    if game_scene and game_scene.has_method("_clear_cards"):
        game_scene._clear_cards()

    # 清除永久BUFF展示区
    if game_scene:
        var forever_buff_area = game_scene.get_node_or_null("ForeverBuffArea")
        if forever_buff_area and forever_buff_area.has_method("clear_buffs"):
            forever_buff_area.clear_buffs()

    # 重置BUFF解锁状态
    # var buff_manager = get_node("/root/BuffManager")
    # if buff_manager:
    #     buff_manager.reset_all_buffs()

    # 发出关卡变化信号
    level_changed.emit(current_level)

    # 发出阶变化信号
    round_changed.emit(current_round)

    # 发出初始积分变化信号，确保进度条显示
    var level_total_score = calc_total_score(current_level)
    score_changed.emit(total_score, level_total_score, 0.0)

    # 发出重置游戏规则信号
    reset_game_rules.emit()

    # 重新启动循环动画
    if game_scene and game_scene.has_method("restart_loop_animations"):
        game_scene.restart_loop_animations()

    change_game_state(GameState.PREPARATION)

# 改变游戏状态
func change_game_state(new_state: int):
    current_state = new_state
    game_state_changed.emit(new_state)
    
    match new_state:
        GameState.PREPARATION:
            _start_preparation_phase()
        GameState.ACTION:
            _start_action_phase()
        GameState.SETTLEMENT:
            _start_settlement_phase()
        GameState.GAME_OVER:
            _handle_game_over()
        GameState.MAIN_MENU:
            _handle_game_over()

# 开始准备阶段
func _start_preparation_phase():
    # 只在关卡开始时重置骰子次数
    if current_round == 1 or not has_rolled_for_level:
        dice_rolls_left = dice_rolls_chance
        has_rolled_for_level = false  # 确保可以在新关卡开始时掷骰子
    else:
        # 非第一阶，直接设置固定积分限制
        var fixed_limit = first_round_score + (current_round - 1) * 5
        limit_score = fixed_limit
        # 发出限制积分变化信号
        limit_score_changed.emit(limit_score)
    
    # 发出骰子数量变化信号，确保UI更新
    dice_count_changed.emit(dice_count)
    
    # 发送骰子次数变化信号
    dice_rolls_changed.emit(dice_rolls_left, dice_rolls_chance)

# 开始行动阶段
func _start_action_phase():
    pass
    # 设置行动点数：保留上一阶剩余的行动点数，每阶固定增加2点，但最大累计为5点
    # action_points += 2
    # if action_points > max_action_points:
    #     action_points = max_action_points
    # action_points_changed.emit(action_points)
    # 这里会触发发牌等操作

# 开始结算阶段
func _start_settlement_phase():
    # 这里会计算分数并判断胜负
    pass

# 处理游戏结束
func _handle_game_over():
    dice_rolls_chance = 3
    hand_limit = 5
    double_gunner_chance = 0.0
    reward_score = 0
    demon_whisper_stacks = 0
    demon_whisper_per_stage_penalty = 0
    demon_whisper_bonus_per_5_stages = 0
    BuffManager.clear_active_buffs()

    # 重置奖励权重
    RewardWeightManager.reset_all_weights()
    print("游戏失败，已重置奖励权重")

    # 重置游戏规则奖励分数
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager:
        buff_manager.reset_game_rules_bonus()
    

# 掷骰子
func roll_dice():
    if dice_rolls_left <= 0:
        return
    
    # 根据当前骰子数量生成骰子点数
    var total_dice_value = 0
    last_dice_result = []  # 重置骰子结果数组
    
    # 初始化数组大小以匹配骰子数量
    for i in range(dice_count):
        last_dice_result.append(0)
    
    # 生成骰子点数
    for i in range(dice_count):
        var dice_value = randi() % 6 + 1
        last_dice_result[i] = dice_value
        total_dice_value += dice_value
    
    # 如果是第一阶，计算限制积分但不立即显示
    if current_round == 1:
        var fixed_limit = first_round_score
        # 计算限制积分 = 固定积分限制 - 骰子总分数
        # limit_score = fixed_limit - total_dice_value
        # 骰子总数不再影响限制积分
        limit_score = fixed_limit
        # 注意：这里不再发送限制积分变化信号，而是等到确认后再发送
    
    # 减少剩余掷骰子次数
    dice_rolls_left -= 1
    has_rolled_for_level = true  # 标记当前关卡已经掷过骰子
    
    # 发出信号
    dice_rolled.emit(last_dice_result)
    dice_rolls_changed.emit(dice_rolls_left, dice_rolls_chance)

    # 如果有骰子次数增加的永久BUFF，播放膨胀动画
    if dice_rolls_chance > 3:  # 默认是3次，如果大于3说明有BUFF
        AnimationManager.play_permanent_buff_bounce_animation("dice_chance")
    
    return last_dice_result

# 确认骰子结果，进入行动阶段
func confirm_dice_result(dice_sum: int):
    # 如果是第一阶，在这里发送限制积分变化信号
    if current_round == 1:
        limit_score_changed.emit(limit_score)
        action_points_changed.emit(dice_sum)
    
    change_game_state(GameState.ACTION)

# 消耗行动点
func consume_action_points(amount: int) -> bool:
    if action_points >= amount:
        action_points -= amount
        action_points_changed.emit(action_points)
        return true
    return false

func consume_score_points(amount: int) -> bool:
    if total_score >= amount:
        total_score -= amount
        score_changed.emit(total_score, calc_total_score(current_level), (current_round - 1) / (rounds_per_level + (current_level - 1) * additional_rounds_per_level))
        return true
    return false

# 增加行动点
func add_action_points(amount: int):
    # 增加行动点数，但不超过最大值
    action_points = action_points + amount
    # 发出行动点数变化信号
    action_points_changed.emit(action_points)

# 检查关卡是否完成
func check_level_completion() -> bool:
    # 计算当前关卡所需阶数
    var current_level_rounds = rounds_per_level + (current_level - 1) * additional_rounds_per_level
    
    # 如果当前阶数超过了当前关卡所需阶数，标记关卡完成
    if current_round > current_level_rounds:
        # 发送关卡完成信号
        emit_signal("level_completed", current_level)
        return true
    return false

# 结束阶
func end_round(score: int):
    print("end_round_score: ", score, " limit_score: ", limit_score)
    # 判断是否达到积分限制
    if score >= limit_score:
        # 成功，进入下一阶
        total_score = score - limit_score  # 扣减积分限制，剩余积分累积到下一阶
        score_changed.emit(total_score, calc_total_score(current_level), (current_round - 1) / (rounds_per_level + (current_level - 1) * additional_rounds_per_level))
        current_round += 1
        round_changed.emit(current_round)
        
        # 检查是否完成当前关卡
        if check_level_completion():
            return # 如果关卡完成，直接返回，不执行后续代码
        
        change_game_state(GameState.PREPARATION)
        
        # 更新BUFF阶数
        var buff_manager = get_node("/root/BuffManager")
        if buff_manager:
            buff_manager.update_buff_rounds()
    else:
        # 失败，游戏结束
        change_game_state(GameState.GAME_OVER)

# 返回主菜单
func return_to_main_menu():
    # 停止所有循环动画，防止Tween无限循环错误
    var animation_manager = get_node_or_null("/root/AnimationManager")
    if animation_manager:
        animation_manager.stop_all_loop_animations()

    # 重置游戏相关数据
    double_gunner_chance = 0.0  # 重置双枪手概率

    # 清除永久BUFF展示区
    var game_scene = get_tree().current_scene
    if game_scene:
        var forever_buff_area = game_scene.get_node_or_null("ForeverBuffArea")
        if forever_buff_area and forever_buff_area.has_method("clear_buffs"):
            forever_buff_area.clear_buffs()

    change_game_state(GameState.MAIN_MENU)

# 获取关卡所需总阶数
func get_total_rounds_for_level(level: int) -> int:
    return rounds_per_level + additional_rounds_per_level * (level - 1)

# 计算关卡总积分
func calc_total_score(level: int) -> int:
    # 关卡k的阶数：R(k) = 10 + (k-1) × 5
    var rounds = get_total_rounds_for_level(level)
    
    # 计算总积分：TotalScore(k) = first_round_score × R(k) + 5 × ((R(k)-1) × R(k))/2
    var total_score = first_round_score * rounds + additional_rounds_per_level * ((rounds - 1) * rounds) / 2
    
    return int(total_score)

# 提升到下一关
func level_up():
    # 保存旧关卡数
    var old_level = current_level

    # 停止所有循环动画，防止Tween无限循环错误
    var animation_manager = get_node_or_null("/root/AnimationManager")
    if animation_manager:
        animation_manager.stop_all_loop_animations()

    # 增加关卡数
    current_level += 1

    # 解锁成就
    if current_level == 10:
        SteamInfo.achieve("tier_ten")
    if current_level == 20:
        SteamInfo.achieve("tier_20")
    if current_level == 30:
        SteamInfo.achieve("tier_30")
    if current_level == 40:
        SteamInfo.achieve("tier_40")
    if current_level == 50:
        SteamInfo.achieve("tier_50")

    # 重置阶数为1
    current_round = 1
    round_changed.emit(current_round)

    # 重置积分(附带奖励积分)
    total_score = reward_score

    # 发送关卡变化信号
    level_changed.emit(current_level)

    # 发出初始积分变化信号，确保进度条显示
    var level_total_score = calc_total_score(current_level)
    score_changed.emit(total_score, level_total_score, 0.0)

    # 获取游戏场景并清空卡牌
    var game_scene = get_tree().current_scene
    if game_scene and game_scene.has_method("_clear_cards"):
        game_scene._clear_cards()

    # 清空BUFF显示区域
    if game_scene:
        var buff_display = game_scene.get_node_or_null("BuffDisplay")
        if buff_display:
            buff_display.clear_all_buffs()

    BuffManager.clear_active_buffs()

    # 重置抽牌消耗
    draw_card_aciton = 1
    change_pool_action = 5
    consume_score = false

     # 获取升级场景
    if game_scene:
        var up_level_scene = game_scene.get_node_or_null("UpLevelScene")
        if up_level_scene:
            # 播放升级场景动画
            await up_level_scene.play_level_transition(GameManager.current_level)

    # # 重新启动循环动画
    if game_scene and game_scene.has_method("restart_loop_animations"):
        game_scene.restart_loop_animations()

    # 重置进入准备阶段
    change_game_state(GameState.PREPARATION)

# 更新积分
func update_score(new_score: int):
    total_score = new_score
    
    # 计算当前关卡总积分
    var level_total_score = calc_total_score(current_level)
    
    # 计算实时进度
    var progress = float(total_score) / level_total_score if level_total_score > 0 else 0.0
    progress = min(progress, 1.0)  # 确保进度不超过1.0
    
    # 发送积分变化信号
    score_changed.emit(total_score, level_total_score, progress, false)
    
    return total_score

# 进入下一关
func enter_next_level():
    current_level += 1
    current_round = 1
    has_rolled_for_level = false  # 重置掷骰子状态，确保新关卡开始时可以掷骰子
    
    # 发出关卡变化信号
    level_changed.emit(current_level)
    # 发出阶变化信号
    round_changed.emit(current_round)
    
    # 重置游戏状态为准备阶段
    change_game_state(GameState.PREPARATION)

# 添加调试相关的setter方法
func set_level(new_level: int):
    current_level = new_level
    emit_signal("level_changed", current_level)

func set_round(new_round: int):
    current_round = new_round
    emit_signal("round_changed", current_round)

func set_action_points(new_points: int):
    action_points = new_points
    emit_signal("action_points_changed", action_points)

func set_total_score(new_score: int):
    total_score = new_score
    emit_signal("score_changed", total_score)

func set_limit_score(new_limit: int):
    limit_score = new_limit
    emit_signal("limit_changed", limit_score)

shader_type canvas_item;

// 阴影模糊参数
uniform float blur_amount : hint_range(0.0, 30.0) = 5.0;
// 阴影颜色参数
uniform vec4 shadow_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);
// 圆角半径 (0.0-0.5)
uniform float corner_radius : hint_range(0.0, 0.5) = 0.5;

// 简单的高斯模糊函数
void fragment() {
    // 采样点数量 - 增加采样点会使模糊效果更平滑但会降低性能
    const int samples = 36;
    
    // 初始化颜色
    vec4 color = texture(TEXTURE, UV);
    
    // 高斯模糊
    for(int i = 0; i < samples; i++) {
        // 创建环形采样模式
        float angle = float(i) * 6.28318 / float(samples);
        vec2 offset = vec2(cos(angle), sin(angle)) * blur_amount * TEXTURE_PIXEL_SIZE;
        
        // 对当前方向采样多个点
        for(int j = 1; j <= 4; j++) {
            // 采样点的位置
            vec2 sample_pos = UV + offset * float(j) / 4.0;
            
            // 采样并累加颜色
            color += texture(TEXTURE, sample_pos);
        }
    }
    
    // 平均所有采样的颜色
    color /= float(samples * 4 + 1);
    
    // 改进的圆角效果
    vec2 center_distance = abs(UV - vec2(0.5));
    float corner_distance = length(max(center_distance - vec2(0.5 - corner_radius), vec2(0.0)));
    float corner_mask = 1.0 - smoothstep(corner_radius - 0.01, corner_radius + 0.01, corner_distance);
    
    // 柔和的边缘淡出
    float edge_smoothness = 0.1;
    float edge_fade = min(
        min(smoothstep(0.0, edge_smoothness, UV.x), 
            smoothstep(0.0, edge_smoothness, UV.y)),
        min(smoothstep(0.0, edge_smoothness, 1.0 - UV.x),
            smoothstep(0.0, edge_smoothness, 1.0 - UV.y))
    );
    
    // 结合圆角和边缘淡出
    float final_mask = corner_mask * edge_fade;
    
    // 使用阴影的alpha作为蒙版，但颜色使用shadow_color
    float alpha = color.a * final_mask * 0.8; // 降低整体不透明度
    
    // 应用黑色阴影颜色，保留计算出的alpha值
    COLOR = vec4(shadow_color.rgb, alpha);
} 
extends Panel

# 设置面板 - 负责游戏设置界面的逻辑

# 节点引用
@onready var resolution_option = $VBoxContainer/ResolutionContainer/ResolutionOption
@onready var fullscreen_check = $VBoxContainer/FullscreenContainer/FullscreenCheck
@onready var borderless_check = $VBoxContainer/BorderlessContainer/BorderlessCheck
@onready var music_slider = $VBoxContainer/MusicContainer/MusicSlider
@onready var sfx_slider = $VBoxContainer/SFXContainer/SFXSlider
@onready var return_button = $VBoxContainer/ReturnButton
@onready var title_label = $VBoxContainer/TitleLabel
@onready var sound_title = $VBoxContainer/SoundTitle
@onready var music_label = $VBoxContainer/MusicContainer/Label
@onready var sfx_label = $VBoxContainer/SFXContainer/Label
@onready var language_option = $VBoxContainer/LanguageContainer/LanguageOption
@onready var language_title = $VBoxContainer/LanguageTitle

# 分辨率选项
const RESOLUTIONS = [
    Vector2(1280, 720),
    Vector2(1366, 768),
    Vector2(1600, 900),
    Vector2(1920, 1080)
]

# 信号
signal return_to_main_menu
signal display_changed  # 新增信号
signal return_pressed

const SETTING_PATH = "user://settings.cfg"

# 初始化
func _ready():
    # 确保节点引用正确
    if !is_instance_valid(language_option):
        print("错误: 找不到语言选择节点 VBoxContainer/LanguageContainer/LanguageOption")
        return
        
    title_label = $VBoxContainer/TitleLabel
    if !is_instance_valid(title_label):
        print("错误: 找不到标题节点 VBoxContainer/TitleLabel")
        
    sound_title = $VBoxContainer/SoundTitle
    if !is_instance_valid(sound_title):
        print("错误: 找不到音频标题节点 VBoxContainer/SoundTitle")
    
    # 初始化分辨率选项
    _init_resolution_options()
    
    # 加载当前设置
    _load_settings()
    
    # 连接信号
    resolution_option.item_selected.connect(_on_resolution_selected)
    fullscreen_check.toggled.connect(_on_fullscreen_toggled)
    borderless_check.toggled.connect(_on_borderless_toggled)
    music_slider.value_changed.connect(_on_music_volume_changed)
    sfx_slider.value_changed.connect(_on_sfx_volume_changed)
    return_button.pressed.connect(_on_return_button_pressed)
    
    # 设置面板样式
    _setup_panel_style()
    
    # 初始化语言选项
    _setup_language_options()
    
    # 连接语言选择信号（确保在设置选项后连接）
    if language_option:
        language_option.item_selected.connect(_on_language_selected)
    
    # 设置当前选中的语言并更新所有文本
    _set_current_language()
    _update_all_texts()
    
    # 设置标题样式
    _setup_title_style()
    
    # 设置按钮和控件样式
    _setup_button_style()
    _setup_option_controls_style()
    
    # 设置滑块样式
    _setup_slider_style(music_slider)
    _setup_slider_style(sfx_slider)
    
    # 设置初始的输入处理模式
    mouse_filter = Control.MOUSE_FILTER_STOP
    
    # 如果一开始是可见的，播放显示动画
    if visible:
        _play_show_animation()
    else:
        # 如果初始是隐藏的，设置适当的初始状态
        modulate.a = 0.0
        scale = Vector2(0.8, 0.8)
        
    sfx_label.custom_minimum_size = music_label.size

# 设置面板样式
func _setup_panel_style():
    # 创建面板样式
    var panel_style = StyleBoxFlat.new()
    panel_style.bg_color = Color(0.1, 0.1, 0.2, 0.95)
    panel_style.corner_radius_top_left = 25
    panel_style.corner_radius_top_right = 25
    panel_style.corner_radius_bottom_left = 25
    panel_style.corner_radius_bottom_right = 25
    panel_style.border_width_left = 4
    panel_style.border_width_top = 4
    panel_style.border_width_right = 4
    panel_style.border_width_bottom = 4
    panel_style.border_color = Color(0.3, 0.3, 0.7, 1.0)
    panel_style.shadow_color = Color(0, 0, 0, 0.5)
    panel_style.shadow_size = 15
    panel_style.shadow_offset = Vector2(2, 2)
    
    # 应用样式
    add_theme_stylebox_override("panel", panel_style)
    
    # 设置背景颜色
    $Background.color = Color(0.1, 0.1, 0.3, 0.0)  # 透明背景，因为面板已经有自己的样式
    
    # 调整面板大小
    custom_minimum_size = Vector2(500, 550)
    
    # 设置旋转中心点
    pivot_offset = size / 2

# 设置标题样式
func _setup_title_style():
    # 检查title_label是否存在
    if !is_instance_valid(title_label):
        print("警告: 标题节点未找到")
        return
    
    # 设置标题样式
    title_label.add_theme_font_size_override("font_size", 36)
    title_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2, 1.0))  # 金色
    title_label.add_theme_constant_override("outline_size", 6)
    title_label.add_theme_color_override("font_outline_color", Color(0.7, 0.0, 0.0, 1.0))
    title_label.add_theme_constant_override("shadow_size", 4)
    title_label.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.5))
    
    # 设置旋转中心点
    title_label.pivot_offset = title_label.size / 2
    
    # 播放标题动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    title_label.scale = Vector2(0.1, 0.1)
    tween.tween_property(title_label, "scale", Vector2(1.2, 1.2), 0.5)
    tween.tween_property(title_label, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 设置音频标题样式
    if is_instance_valid(sound_title):
        sound_title.add_theme_font_size_override("font_size", 28)
        sound_title.add_theme_color_override("font_color", Color(0.3, 0.7, 1.0, 1.0))  # 蓝色
        sound_title.add_theme_constant_override("outline_size", 3)
        sound_title.add_theme_color_override("font_outline_color", Color(0.0, 0.2, 0.5, 1.0))
        sound_title.add_theme_constant_override("shadow_size", 2)
        sound_title.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.5))
        
        # 播放子标题动画
        var sound_tween = create_tween()
        sound_tween.set_ease(Tween.EASE_OUT)
        sound_tween.set_trans(Tween.TRANS_ELASTIC)
        sound_tween.tween_property(sound_title, "scale", Vector2(1.2, 1.2), 0.3)
        sound_tween.tween_property(sound_title, "scale", Vector2(1.0, 1.0), 0.2)
        
    # 设置音频标题样式
    if is_instance_valid(language_title):
        language_title.add_theme_font_size_override("font_size", 28)
        language_title.add_theme_color_override("font_color", Color(0.3, 0.7, 1.0, 1.0))  # 蓝色
        language_title.add_theme_constant_override("outline_size", 3)
        language_title.add_theme_color_override("font_outline_color", Color(0.0, 0.2, 0.5, 1.0))
        language_title.add_theme_constant_override("shadow_size", 2)
        language_title.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.5))
        
        # 播放子标题动画
        var sound_tween = create_tween()
        sound_tween.set_ease(Tween.EASE_OUT)
        sound_tween.set_trans(Tween.TRANS_ELASTIC)
        sound_tween.tween_property(language_title, "scale", Vector2(1.2, 1.2), 0.3)
        sound_tween.tween_property(language_title, "scale", Vector2(1.0, 1.0), 0.2)

# 设置选项控件样式
func _setup_option_controls_style():
    # 设置标签样式
    var labels = {
        $VBoxContainer/ResolutionContainer/Label: "RESOLUTION",
        $VBoxContainer/FullscreenContainer/Label: "FULLSCREEN",
        $VBoxContainer/BorderlessContainer/Label: "BORDERLESS",
        $VBoxContainer/MusicContainer/Label: "BGM_VOLUME",
        $VBoxContainer/SFXContainer/Label: "SFX_VOLUME",
        $VBoxContainer/LanguageContainer/Label: "LANGUAGE"
    }
    
    for label in labels:
        if label:
            label.text = tr(labels[label])
            label.add_theme_font_size_override("font_size", 18)
            label.add_theme_color_override("font_color", Color(0.9, 0.9, 1.0, 1.0))
            label.add_theme_constant_override("outline_size", 2)
            label.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.3, 0.7))
    
    # 设置下拉选项样式
    if resolution_option:
        _setup_option_button_style(resolution_option)
    
    if language_option:
        _setup_option_button_style(language_option)
    
    # 设置复选框样式
    var checkboxes = [fullscreen_check, borderless_check]
    for checkbox in checkboxes:
        if checkbox:
            checkbox.add_theme_font_size_override("font_size", 16)
            checkbox.custom_minimum_size.y = 26

# 设置下拉选项按钮样式
func _setup_option_button_style(option_button: OptionButton):
    # 设置基本大小和对齐
    option_button.custom_minimum_size = Vector2(100, 26)
    option_button.pivot_offset = option_button.custom_minimum_size / 2
    option_button.alignment = HORIZONTAL_ALIGNMENT_CENTER
    
    # 设置字体大小
    option_button.add_theme_font_size_override("font_size", 16)
    
    # 主选项框样式
    var normal_style = StyleBoxFlat.new()
    normal_style.bg_color = Color(0.2, 0.2, 0.5, 1.0)
    normal_style.corner_radius_top_left = 8
    normal_style.corner_radius_top_right = 8
    normal_style.corner_radius_bottom_left = 8
    normal_style.corner_radius_bottom_right = 8
    normal_style.border_width_left = 2
    normal_style.border_width_top = 2
    normal_style.border_width_right = 2
    normal_style.border_width_bottom = 2
    normal_style.border_color = Color(0.3, 0.3, 0.7, 1.0)
    option_button.add_theme_stylebox_override("normal", normal_style)
    
    # 悬停样式
    var hover_style = normal_style.duplicate()
    hover_style.bg_color = Color(0.25, 0.25, 0.55, 1.0)
    hover_style.border_color = Color(0.4, 0.4, 0.8, 1.0)
    option_button.add_theme_stylebox_override("hover", hover_style)
    
    # 按下样式
    var pressed_style = normal_style.duplicate()
    pressed_style.bg_color = Color(0.15, 0.15, 0.45, 1.0)
    pressed_style.border_color = Color(0.2, 0.2, 0.6, 1.0)
    option_button.add_theme_stylebox_override("pressed", pressed_style)
    
    # 禁用样式
    var disabled_style = normal_style.duplicate()
    disabled_style.bg_color = Color(0.1, 0.1, 0.3, 0.5)
    disabled_style.border_color = Color(0.2, 0.2, 0.5, 0.5)
    option_button.add_theme_stylebox_override("disabled", disabled_style)
    
    # 设置下拉面板样式
    var popup = option_button.get_popup()
    if popup:
        # 面板背景样式
        var popup_style = StyleBoxFlat.new()
        popup_style.bg_color = Color(0.15, 0.15, 0.35, 1.0)
        popup_style.corner_radius_top_left = 8
        popup_style.corner_radius_top_right = 8
        popup_style.corner_radius_bottom_left = 8
        popup_style.corner_radius_bottom_right = 8
        popup_style.border_width_left = 2
        popup_style.border_width_top = 2
        popup_style.border_width_right = 2
        popup_style.border_width_bottom = 2
        popup_style.border_color = Color(0.3, 0.3, 0.7, 1.0)
        popup_style.shadow_color = Color(0, 0, 0, 0.3)
        popup_style.shadow_size = 4
        popup.add_theme_stylebox_override("panel", popup_style)
        
        # 选项悬停样式
        var item_hover_style = StyleBoxFlat.new()
        item_hover_style.bg_color = Color(0.3, 0.3, 0.6, 1.0)
        item_hover_style.corner_radius_top_left = 4
        item_hover_style.corner_radius_top_right = 4
        item_hover_style.corner_radius_bottom_left = 4
        item_hover_style.corner_radius_bottom_right = 4
        popup.add_theme_stylebox_override("hover", item_hover_style)
        
        # 选中项样式
        var selected_style = StyleBoxFlat.new()
        selected_style.bg_color = Color(0.4, 0.4, 0.7, 1.0)
        selected_style.corner_radius_top_left = 4
        selected_style.corner_radius_top_right = 4
        selected_style.corner_radius_bottom_left = 4
        selected_style.corner_radius_bottom_right = 4
        popup.add_theme_stylebox_override("selected", selected_style)
        
        # 设置字体颜色
        popup.add_theme_color_override("font_color", Color(0.9, 0.9, 1.0, 1.0))
        popup.add_theme_color_override("font_hover_color", Color(1.0, 1.0, 1.0, 1.0))
        popup.add_theme_color_override("font_selected_color", Color(1.0, 1.0, 1.0, 1.0))
        popup.add_theme_font_size_override("font_size", 16)
        
        # 设置选项间距
        popup.add_theme_constant_override("v_separation", 8)
        popup.add_theme_constant_override("h_separation", 8)
        popup.add_theme_constant_override("item_padding", 8)
    
    # 连接鼠标信号以添加悬停效果
    if !option_button.mouse_entered.is_connected(_on_option_button_mouse_entered):
        option_button.mouse_entered.connect(_on_option_button_mouse_entered.bind(option_button))
    if !option_button.mouse_exited.is_connected(_on_option_button_mouse_exited):
        option_button.mouse_exited.connect(_on_option_button_mouse_exited.bind(option_button))

# 选项按钮鼠标进入事件
func _on_option_button_mouse_entered(button: OptionButton):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.2)
    
    # 添加发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 8
    style.shadow_color = Color(1.0, 1.0, 1.0, 0.3)
    button.add_theme_stylebox_override("hover", style)

# 选项按钮鼠标离开事件
func _on_option_button_mouse_exited(button: OptionButton):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 移除发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 0
    button.add_theme_stylebox_override("hover", style)

# 初始化分辨率选项
func _init_resolution_options():
    resolution_option.clear()
    
    # 连接下拉菜单弹出和关闭信号
    if !resolution_option.get_popup().about_to_popup.is_connected(_on_popup_about_to_show):
        resolution_option.get_popup().about_to_popup.connect(_on_popup_about_to_show)
    if !resolution_option.get_popup().popup_hide.is_connected(_on_popup_hide):
        resolution_option.get_popup().popup_hide.connect(_on_popup_hide)
    
    for res in RESOLUTIONS:
        resolution_option.add_item("%dx%d" % [res.x, res.y])
    
    # 添加当前分辨率（如果不在列表中）
    var current_res = Vector2(DisplayServer.window_get_size().x, DisplayServer.window_get_size().y)
    var found = false
    
    for i in range(RESOLUTIONS.size()):
        if RESOLUTIONS[i] == current_res:
            found = true
            resolution_option.select(i)
            break
    
    # if !found:
    #     resolution_option.add_item("%dx%d (当前)" % [current_res.x, current_res.y])
    #     resolution_option.select(resolution_option.get_item_count() - 1)

# 加载当前设置
func _load_settings():
    # 尝试从配置文件加载设置
    var config = ConfigFile.new()
    var err = config.load(SETTING_PATH)
    
    # 获取音频总线索引
    var music_bus_idx = AudioServer.get_bus_index("Music")
    var sfx_bus_idx = AudioServer.get_bus_index("SFX")
    
    if err == OK:
        # 从配置文件加载设置
        # 全屏设置
        var fullscreen = config.get_value("display", "fullscreen", true)
        fullscreen_check.button_pressed = fullscreen
        if fullscreen:
            DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
        else:
            DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
        
        # 无边框设置
        var borderless = config.get_value("display", "borderless", false)
        borderless_check.button_pressed = borderless
        DisplayServer.window_set_flag(DisplayServer.WINDOW_FLAG_BORDERLESS, borderless)
        
        # 窗口大小设置
        var width = config.get_value("display", "width", 1280)
        var height = config.get_value("display", "height", 720)
        if !fullscreen:
            DisplayServer.window_set_size(Vector2i(width, height))
            
            # 居中窗口
            var screen_size = DisplayServer.screen_get_size()
            var window_size = DisplayServer.window_get_size()
            var centered_pos = (screen_size - window_size) / 2
            DisplayServer.window_set_position(centered_pos)
        
        # 音量设置
        if music_bus_idx >= 0:
            var music_volume_db = config.get_value("audio", "music_volume", -10.0) # 默认 -10.0 dB
            AudioServer.set_bus_volume_db(music_bus_idx, music_volume_db)
            # 将分贝值转换为线性值（0-1）用于滑块显示
            music_slider.value = _db_to_linear_custom(music_volume_db)
        
        if sfx_bus_idx >= 0:
            var sfx_volume_db = config.get_value("audio", "sfx_volume", -10.0) # 默认 -10.0 dB
            AudioServer.set_bus_volume_db(sfx_bus_idx, sfx_volume_db)
            # 将分贝值转换为线性值（0-1）用于滑块显示
            sfx_slider.value = _db_to_linear_custom(sfx_volume_db)
            
        print("已成功加载设置")
    else:
        print("没有找到设置文件或加载失败，使用默认设置")
        # 使用默认值
        # 全屏设置
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
        fullscreen_check.button_pressed = true
        
        # 无边框设置
        DisplayServer.window_set_flag(DisplayServer.WINDOW_FLAG_BORDERLESS, true)
        borderless_check.button_pressed = true
        
        # 音量设置 (默认值调整为更合理的水平)
        var default_volume_db = -10.0  # 默认音量调整为-10dB
        
        if music_bus_idx >= 0:
            AudioServer.set_bus_volume_db(music_bus_idx, default_volume_db)
            music_slider.value = _db_to_linear_custom(default_volume_db)
        
        if sfx_bus_idx >= 0:
            AudioServer.set_bus_volume_db(sfx_bus_idx, default_volume_db)
            sfx_slider.value = _db_to_linear_custom(default_volume_db)
            
        # 首次运行时保存默认设置
        _save_settings()

# 保存设置
func _save_settings():
    # 创建配置文件
    var config = ConfigFile.new()
    var err = config.load(SETTING_PATH)
    if err != OK:
        # 如果配置文件不存在，创建新的
        config = ConfigFile.new()
    
    # 保存窗口设置
    config.set_value("display", "fullscreen", fullscreen_check.button_pressed)
    config.set_value("display", "borderless", borderless_check.button_pressed)
    config.set_value("display", "width", DisplayServer.window_get_size().x)
    config.set_value("display", "height", DisplayServer.window_get_size().y)
    
    # 保存音量设置
    var music_bus_idx = AudioServer.get_bus_index("Music")
    var sfx_bus_idx = AudioServer.get_bus_index("SFX")
    
    if music_bus_idx >= 0:
        var music_volume_db = AudioServer.get_bus_volume_db(music_bus_idx)
        config.set_value("audio", "music_volume", music_volume_db)
    
    if sfx_bus_idx >= 0:
        var sfx_volume_db = AudioServer.get_bus_volume_db(sfx_bus_idx)
        config.set_value("audio", "sfx_volume", sfx_volume_db)
    
    # 保存语言设置
    config.set_value("language", "locale", "en")
    
    # 保存教程状态（如果存在）
    if config.has_section_key("Tutorial", "completed"):
        var tutorial_completed = config.get_value("Tutorial", "completed")
        config.set_value("Tutorial", "completed", tutorial_completed)
    
    # 保存配置文件
    err = config.save(SETTING_PATH)
    SteamInfo.fileUpload("Settings",config)

# 分辨率选项被选择
func _on_resolution_selected(index):
    if index < 0 or index >= resolution_option.get_item_count():
        return
    
    var resolution_text = resolution_option.get_item_text(index)
    var dimensions = resolution_text.split("x")
    
    if dimensions.size() >= 2:
        var width = int(dimensions[0])
        var height = int(dimensions[1])
        
        # 设置窗口大小
        DisplayServer.window_set_size(Vector2i(width, height))
        
        # 居中窗口
        var screen_size = DisplayServer.screen_get_size()
        var window_size = DisplayServer.window_get_size()
        var centered_pos = (screen_size - window_size) / 2
        DisplayServer.window_set_position(centered_pos)
        
        # 保存设置
        _save_settings()
        
        # 发出显示改变信号
        emit_signal("display_changed")

# 全屏选项被切换
func _on_fullscreen_toggled(button_pressed):
    if button_pressed:
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
    else:
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
    
    # 保存设置
    _save_settings()
    
    # 发出显示改变信号
    emit_signal("display_changed")

# 无边框选项被切换
func _on_borderless_toggled(button_pressed):
    DisplayServer.window_set_flag(DisplayServer.WINDOW_FLAG_BORDERLESS, button_pressed)
    
    # 保存设置
    _save_settings()

# 音乐音量被改变
func _on_music_volume_changed(value):
    var music_bus_idx = AudioServer.get_bus_index("Music")
    if music_bus_idx >= 0:
        var volume_db = _linear_to_db_custom(value)
        AudioServer.set_bus_volume_db(music_bus_idx, volume_db)
        # 保存设置
        _save_settings()
        
        # 播放背景音乐示例音效
        var audio_manager = get_node("/root/AudioManager")
        if audio_manager:
            audio_manager.play_sfx("res://assert/audio/sfx/bubble_one.mp3")

# 音效音量被改变
func _on_sfx_volume_changed(value):
    var sfx_bus_idx = AudioServer.get_bus_index("SFX")
    if sfx_bus_idx >= 0:
        var volume_db = _linear_to_db_custom(value)
        AudioServer.set_bus_volume_db(sfx_bus_idx, volume_db)
        # 保存设置
        _save_settings()
        
        # 播放音效示例
        var audio_manager = get_node("/root/AudioManager")
        if audio_manager:
            audio_manager.play_sfx("res://assert/audio/sfx/bubble_one.mp3")

# 返回按钮被点击
func _on_return_button_pressed():
    # 播放按钮动画
    _play_button_animation(return_button)
    
    # 先触发返回信号，让主界面知道设置面板要关闭
    emit_signal("return_pressed")
    
    # 播放退出动画
    _play_hide_animation()

# 播放按钮动画
func _play_button_animation(button):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BOUNCE)
    tween.tween_property(button, "scale", Vector2(0.9, 0.9), 0.1)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 创建闪光效果
    var flash = ColorRect.new()
    flash.color = Color(1, 1, 1, 0.5)
    flash.size = button.size
    flash.position = Vector2(0, 0)
    button.add_child(flash)
    
    # 创建淡出动画
    var flash_tween = create_tween()
    flash_tween.tween_property(flash, "modulate:a", 0.0, 0.2)
    
    # 动画完成后删除闪光
    await flash_tween.finished
    flash.queue_free()

# 播放显示动画
func _play_show_animation():
    # 设置初始状态
    modulate.a = 0.0
    scale = Vector2(0.8, 0.8)
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(1.0, 1.0), 0.5)

# 播放隐藏动画
func _play_hide_animation():
    # 立即将处理输入设为false，防止多次点击
    self.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 0.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(0.8, 0.8), 0.3)
    
    # 等待动画完成后隐藏面板
    await tween.finished
    visible = false
    
    # 恢复输入处理
    self.mouse_filter = Control.MOUSE_FILTER_STOP

# 设置按钮样式
func _setup_button_style():
    # 设置按钮大小
    return_button.custom_minimum_size = Vector2(271, 60)
    
    # 设置旋转中心点
    return_button.pivot_offset = return_button.custom_minimum_size / 2
    
    # 创建按钮样式
    var normal_style = StyleBoxFlat.new()
    normal_style.bg_color = Color(0.2, 0.4, 0.8, 1.0)
    normal_style.corner_radius_top_left = 15
    normal_style.corner_radius_top_right = 15
    normal_style.corner_radius_bottom_left = 15
    normal_style.corner_radius_bottom_right = 15
    normal_style.border_width_left = 4
    normal_style.border_width_top = 4
    normal_style.border_width_right = 4
    normal_style.border_width_bottom = 4
    normal_style.border_color = Color(0.4, 0.6, 1.0, 1.0)
    
    # 创建悬停样式
    var hover_style = normal_style.duplicate()
    hover_style.bg_color = Color(0.3, 0.5, 0.9, 1.0)
    hover_style.border_color = Color(0.5, 0.7, 1.0, 1.0)
    
    # 创建按下样式
    var pressed_style = normal_style.duplicate()
    pressed_style.bg_color = Color(0.1, 0.3, 0.7, 1.0)
    pressed_style.border_color = Color(0.4, 0.6, 1.0, 1.0)
    pressed_style.border_width_bottom = 2  # 按下时底部边框变细
    
    # 应用样式
    return_button.add_theme_stylebox_override("normal", normal_style)
    return_button.add_theme_stylebox_override("hover", hover_style)
    return_button.add_theme_stylebox_override("pressed", pressed_style)
    
    # 设置字体样式
    return_button.add_theme_font_size_override("font_size", 24)
    return_button.add_theme_color_override("font_color", Color(1.0, 1.0, 1.0, 1.0))
    return_button.add_theme_color_override("font_hover_color", Color(1.0, 1.0, 1.0, 1.0))
    return_button.add_theme_color_override("font_pressed_color", Color(0.9, 0.9, 0.9, 1.0))
    return_button.add_theme_constant_override("outline_size", 2)
    return_button.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 0.5))
    
    # 更新按钮文本
    return_button.text = tr("RETURN")
    
    # 连接鼠标信号
    return_button.mouse_entered.connect(_on_button_mouse_entered.bind(return_button))
    return_button.mouse_exited.connect(_on_button_mouse_exited.bind(return_button))

# 按钮鼠标进入事件
func _on_button_mouse_entered(button):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.3)
    
    # 添加发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 10
    style.shadow_color = Color(1.0, 1.0, 1.0, 0.3)
    button.add_theme_stylebox_override("hover", style)

# 按钮鼠标离开事件
func _on_button_mouse_exited(button):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 移除发光效果
    var style = button.get_theme_stylebox("hover").duplicate()
    style.shadow_size = 0
    button.add_theme_stylebox_override("hover", style)

# 设置滑块样式
func _setup_slider_style(slider):
    if not slider:
        return
        
    # 设置滑块样式
    var grabber_area_style = StyleBoxFlat.new()
    grabber_area_style.bg_color = Color(0.3, 0.5, 0.9, 0.7)
    grabber_area_style.corner_radius_top_left = 6
    grabber_area_style.corner_radius_top_right = 6
    grabber_area_style.corner_radius_bottom_left = 6
    grabber_area_style.corner_radius_bottom_right = 6
    
    var slider_style = StyleBoxFlat.new()
    slider_style.bg_color = Color(0.15, 0.15, 0.3, 0.8)
    slider_style.corner_radius_top_left = 6
    slider_style.corner_radius_top_right = 6
    slider_style.corner_radius_bottom_left = 6
    slider_style.corner_radius_bottom_right = 6
    
    # 创建grabber样式
    var grabber_style = StyleBoxFlat.new()
    grabber_style.bg_color = Color(0.9, 0.9, 1.0, 1.0)
    grabber_style.corner_radius_top_left = 8
    grabber_style.corner_radius_top_right = 8
    grabber_style.corner_radius_bottom_left = 8
    grabber_style.corner_radius_bottom_right = 8
    grabber_style.shadow_color = Color(0, 0, 0, 0.3)
    grabber_style.shadow_size = 2
    
    # 应用样式
    slider.add_theme_stylebox_override("grabber_area", grabber_area_style)
    #slider.add_theme_stylebox_override("slider", slider_style)
    slider.add_theme_stylebox_override("grabber_area_highlight", grabber_area_style)
    slider.add_theme_stylebox_override("grabber", grabber_style)
    
    # 设置滑块大小
    slider.custom_minimum_size.y = 24
    
    # 设置滑块交互
    slider.mouse_entered.connect(_on_slider_mouse_entered.bind(slider))
    slider.mouse_exited.connect(_on_slider_mouse_exited.bind(slider))

# 滑块鼠标进入事件
func _on_slider_mouse_entered(slider):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(slider, "scale:y", 1.1, 0.2)
    
    # 增强滑块样式
    var grabber_area_style = slider.get_theme_stylebox("grabber_area").duplicate()
    grabber_area_style.bg_color = Color(0.4, 0.6, 1.0, 0.8)
    slider.add_theme_stylebox_override("grabber_area", grabber_area_style)
    slider.add_theme_stylebox_override("grabber_area_highlight", grabber_area_style)

# 滑块鼠标离开事件
func _on_slider_mouse_exited(slider):
    # 创建Q弹动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(slider, "scale:y", 1.0, 0.2)
    
    # 恢复滑块样式
    var grabber_area_style = slider.get_theme_stylebox("grabber_area").duplicate()
    grabber_area_style.bg_color = Color(0.3, 0.5, 0.9, 0.7)
    slider.add_theme_stylebox_override("grabber_area", grabber_area_style)
    slider.add_theme_stylebox_override("grabber_area_highlight", grabber_area_style)

# 自定义线性值到分贝的转换函数
func _linear_to_db_custom(linear_value: float) -> float:
    # 确保输入值在0到1之间
    linear_value = clamp(linear_value, 0.0, 1.0)
    
    # 设置分贝范围
    var min_db = -40.0  # 最小分贝值
    var max_db = 0.0    # 最大分贝值
    
    if linear_value == 0:
        return -80.0  # 静音
    
    # 使用对数函数进行更自然的音量变化
    # 这里使用自定义的对数曲线来实现更好的音量控制
    var db = min_db + (max_db - min_db) * pow(linear_value, 2.0)
    return db

# 自定义分贝到线性值的转换函数
func _db_to_linear_custom(db_value: float) -> float:
    # 设置分贝范围
    var min_db = -40.0  # 最小分贝值
    var max_db = 0.0    # 最大分贝值
    
    # 处理静音情况
    if db_value <= -80.0:
        return 0.0
    
    # 反向转换
    var linear = pow((db_value - min_db) / (max_db - min_db), 0.5)
    return clamp(linear, 0.0, 1.0)

# 公开方法：显示设置面板
func show_panel():
    # 如果面板已经可见，不做任何操作
    if visible:
        return
    
    # 确保面板样式设置了圆角
    _setup_panel_style()
    
    # 设置初始状态
    modulate.a = 0.0
    scale = Vector2(0.8, 0.8)
    mouse_filter = Control.MOUSE_FILTER_STOP  # 确保可以接收输入
    
    # 显示面板
    visible = true
    
    # 播放显示动画
    _play_show_animation()
    
    # 更新所有文本为当前语言
    _update_all_texts()
    
    # 设置当前选中的语言
    _set_current_language()

# 下拉菜单即将显示
func _on_popup_about_to_show():
    # 暂时禁用自定义光标并启用系统光标
    var cursor_manager = _get_cursor_manager()
    if cursor_manager:
        # 显示系统光标
        Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
        
        # 临时隐藏自定义光标
        if cursor_manager.cursor_container:
            cursor_manager.cursor_container.visible = false

# 下拉菜单隐藏
func _on_popup_hide():
    # 获取光标管理器
    var cursor_manager = _get_cursor_manager()
    
    # 延迟一小段时间后恢复自定义光标
    # 延迟是必要的，因为下拉菜单可能还有一些关闭动画
    get_tree().create_timer(0.05).timeout.connect(func():
        if cursor_manager:
            # 先启用自定义光标
            if cursor_manager.cursor_container:
                cursor_manager.cursor_container.visible = true
            
            # 再隐藏系统光标
            Input.set_mouse_mode(Input.MOUSE_MODE_HIDDEN)
    )

# 获取光标管理器节点
func _get_cursor_manager():
    # 尝试获取全局光标管理器节点
    if has_node("/root/CursorManager"):
        return get_node("/root/CursorManager")
    
    # 尝试在同级节点查找
    var root = get_tree().root
    for child in root.get_children():
        if child.name == "CursorManager":
            return child
    
    return null

func _setup_language_options():
    if !language_option:
        print("错误: 语言选择节点未找到")
        return
        
    language_option.clear()
    
    # 获取所有可用的语言代码
    var available_locales = TranslationServer.get_loaded_locales()
    
    # 为每个语言添加选项
    for locale in available_locales:
        var lang_name_key = "LANG_NAME_" + locale.to_upper()
        var lang_name = tr(lang_name_key)
        language_option.add_item(lang_name, available_locales.find(locale))

    # 连接下拉菜单弹出和关闭信号
    if !language_option.get_popup().about_to_popup.is_connected(_on_popup_about_to_show):
        language_option.get_popup().about_to_popup.connect(_on_popup_about_to_show)
    if !language_option.get_popup().popup_hide.is_connected(_on_popup_hide):
        language_option.get_popup().popup_hide.connect(_on_popup_hide)

func _set_current_language():
    if !language_option:
        print("错误: 语言选择节点未找到")
        return
        
    var current_locale = TranslationServer.get_locale()
    var available_locales = TranslationServer.get_loaded_locales()
    var locale_index = available_locales.find(current_locale)
    
    if locale_index != -1:
        language_option.selected = locale_index
    else:
        print("警告: 当前语言 ", current_locale, " 未在可用语言列表中找到")

func _on_language_selected(index):
    if !language_option:
        print("错误: 语言选择节点未找到")
        return
        
    var available_locales = TranslationServer.get_loaded_locales()
    if index >= 0 and index < available_locales.size():
        var locale = available_locales[index]
        print("切换语言到: ", locale)  # 添加调试输出
        
        # 设置新的语言
        TranslationServer.set_locale(locale)
        
        # 保存语言设置
        var config = ConfigFile.new()
        var err = config.load(SETTING_PATH)
        if err != OK:
            # 如果文件不存在，创建新的
            config = ConfigFile.new()
        
        config.set_value("language", "locale", locale)
        config.save(SETTING_PATH)
        
        # 更新所有设置面板的文本
        _update_all_texts()
        LanguageManager.change_language(locale)
        
        # 更新主菜单文本
        var main_menu = get_tree().get_root().get_node("MainMenu")
        if main_menu and main_menu.has_method("_update_button_texts"):
            main_menu._update_button_texts()
        else:
            print("警告: 无法找到主菜单节点或更新方法")  # 添加调试输出
    else:
        print("错误: 无效的语言索引: ", index)

# 更新所有文本
func _update_all_texts():
    if title_label:
        title_label.text = tr("SETTINGS_TITLE")
    if sound_title:
        sound_title.text = tr("SOUND_SETTINGS")
    if language_title:
        language_title.text = tr("LANGUAGE_SETTINGS")
    
    # 更新所有标签文本
    var labels = {
        $VBoxContainer/ResolutionContainer/Label: "RESOLUTION",
        $VBoxContainer/FullscreenContainer/Label: "FULLSCREEN",
        $VBoxContainer/BorderlessContainer/Label: "BORDERLESS",
        $VBoxContainer/MusicContainer/Label: "BGM_VOLUME",
        $VBoxContainer/SFXContainer/Label: "SFX_VOLUME",
        $VBoxContainer/LanguageContainer/Label: "LANGUAGE"
    }
    
    for label in labels:
        if label:
            label.text = tr(labels[label])
    
    # 更新返回按钮文本
    if return_button:
        return_button.text = tr("RETURN")

func hide_panel():
    visible = false

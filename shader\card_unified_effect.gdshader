shader_type canvas_item;

// 效果开关
uniform bool enable_float_effect = false;      // 浮动效果开关
uniform bool enable_hover_effect = false;      // 悬停效果开关
uniform bool enable_buff_effect = false;       // BUFF特效开关
uniform bool enable_dissolve_effect = false;   // 溶解效果开关
uniform bool enable_ripple_effect = false;     // 波纹效果开关
uniform bool enable_shine_effect = false;      // 闪光效果开关
uniform bool enable_gold_flash_effect = false; // 金色闪光效果开关
uniform bool enable_forbidden_effect = false;  // 禁止效果开关
uniform bool enable_3d_effect = false;        // 3D效果开关
uniform bool enable_border_effect = false;     // 边框效果开关
uniform bool enable_shadow_effect = false;     // 阴影效果开关

// ===== 阴影效果参数 =====
uniform vec2 shadow_offset = vec2(10.0, 10.0);  // 阴影偏移（像素）
uniform vec4 shadow_color : source_color = vec4(0.0, 0.0, 0.0, 0.3);  // 阴影颜色（RGBA）
uniform bool is_shadow_pass = false;  // 是否是阴影渲染过程
varying float sprite_rotation;  // 用于存储精灵旋转角度

// ===== 边框效果参数 =====
uniform sampler2D border_noise_texture: repeat_enable;  // 边框噪声纹理
uniform float border_radius: hint_range(0.0, 1.0) = 0.45;  // 边框半径
uniform float border_effect_control: hint_range(0.0, 1.0) = 0.6;  // 边框效果控制
uniform float border_burn_speed: hint_range(0.0, 1.0) = 0.7;  // 边框燃烧速度
uniform float border_shape: hint_range(0.0, 1.0) = 1.0;  // 边框形状混合

// ===== 浮动效果参数 =====
uniform float float_amplitude : hint_range(0.0, 10.0) = 3.0;  // 浮动幅度
uniform float float_frequency : hint_range(0.1, 5.0) = 1.0;   // 浮动频率
uniform float time_offset = 0.0;                            // 时间偏移，用于错开多个卡牌的浮动

// ===== 禁止效果参数 =====
uniform sampler2D forbidden_texture;       // 禁止图标纹理
uniform float forbidden_opacity = 0.4;     // 禁止图标不透明度
uniform float forbidden_scale = 0.7;       // 禁止图标缩放
uniform float forbidden_pulse_speed = 3.0; // 禁止图标脉动速度
uniform float forbidden_pulse_min = 0.6;   // 最小脉动强度
uniform float forbidden_pulse_max = 1.2;   // 最大脉动强度

// ===== 悬停效果参数 =====
uniform float hover_scale : hint_range(1.0, 1.5) = 1.2;       // 悬停时的放大系数
uniform bool is_hovered = false;                            // 是否被悬停
uniform float transition_speed : hint_range(1.0, 10.0) = 5.0; // 过渡速度
uniform float current_scale = 1.0;                          // 当前缩放值（在代码中更新）

// ===== BUFF特效参数 =====
/** Noise used for the glitter shape */
uniform sampler2D buff_noise_texture: repeat_enable, filter_linear_mipmap;
uniform sampler2D buff_texture;
uniform vec4 glitter_color : source_color = vec4(1.0,1.0,1.0,1.0);
uniform float glitter_size: hint_range(0.0, 1.0, 0.01) = 0.3;
uniform float glitter_hardness: hint_range(0.0, 1.0, 0.01) = 0.7;
uniform float highlight_speed = 2.0;
uniform float highlight_intensity: hint_range(0.0, 1.0, 0.05) = 1;
uniform bool highlight_band = true;

// --- NEW BUFF Overlay Params (Added earlier) ---
uniform vec4 buff_color : source_color = vec4(1.0, 1.0, 1.0, 1.0); // Buff 颜色 (包含 alpha)
uniform sampler2D buff_noise;         // Buff 噪声纹理
uniform float buff_tile_scale = 1.0;    // Buff 纹理平铺缩放
uniform float buff_noise_influence = 0.1; // Buff 噪声影响强度
uniform bool enable_buff_overlay = false; // BUFF 叠加效果开关

// ===== 溶解效果参数 =====
uniform float dissolve_progress : hint_range(0.0, 1.0) = 0.0;
uniform sampler2D dissolve_noise_texture : source_color, filter_linear_mipmap;

// ===== 波纹效果参数 =====
uniform float ripple_frequency: hint_range(0, 15, 0.01) = 2.6;
uniform float ripple_amplitude: hint_range(0, 3, 0.1) = 0.9;
uniform float ripple_rate : hint_range(0, 20.0, 1) = 2;
uniform float wave_amplitude: hint_range(0.001, 0.1, 0.001) = 0.03;
uniform float wave_frequency: hint_range(0, 15, 0.01) = 8.0;
uniform sampler2D ripple_noise;
uniform sampler2D SCREEN_TEXTURE: hint_screen_texture, filter_linear_mipmap;
uniform vec4 ripple_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);

// ===== 金色闪光效果参数 =====
uniform float flash_speed : hint_range(0.1, 10.0) = 3.5;  // 闪烁速度
uniform float min_intensity : hint_range(0.0, 1.0) = 0.4;  // 最小闪烁强度
uniform float max_intensity : hint_range(0.5, 2.0) = 1.2;  // 最大闪烁强度
uniform vec4 gold_color : source_color = vec4(1.0, 0.84, 0.0, 0.8);  // 金色
uniform float edge_width : hint_range(0.0, 0.5) = 0.2;  // 边缘宽度

// ====== 闪光效果参数 =====
uniform sampler2D surface: source_color;  // 闪光效果表面纹理
uniform float Line_Smoothness : hint_range(0, 0.1) = 0.045;
uniform float Line_Width : hint_range(0, 0.2) = 0.09;
uniform float Brightness = 3.0;
uniform float Rotation_deg : hint_range(-90, 90) = 30;
uniform float Distortion : hint_range(1, 2) = 1.8;
uniform float Speed = 0.4;
uniform float Position : hint_range(0, 1) = 0;
uniform float Position_Min = 0.25;
uniform float Position_Max = 0.5;
uniform float Alpha : hint_range(0, 1) = 0.8;

// ===== 3D效果参数 =====
uniform float card_width = 120;              // 卡牌宽度
uniform float card_height = 180;             // 卡牌高度
uniform vec2 mouse_position = vec2(0, 0);    // 鼠标位置

// 用于BUFF效果的变量
varying vec2 local_vert;

// 阴影效果辅助函数
vec2 rotate_point(vec2 point, float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return vec2(
        point.x * c - point.y * s,
        point.x * s + point.y * c
    );
}

// 波纹效果辅助函数
vec2 wave(vec2 uv, float time) {
    return vec2(
        uv.x + sin(uv.y * wave_frequency + time) * wave_amplitude,
        uv.y + sin(uv.x * wave_frequency + time) * wave_amplitude
    );
}

void vertex() {
    // 保存原始顶点位置（用于BUFF效果）
    local_vert = VERTEX;

    // 计算精灵旋转角度（用于阴影效果）
    sprite_rotation = atan(MODEL_MATRIX[0][1], MODEL_MATRIX[0][0]);

    // 阴影效果处理
    if (enable_shadow_effect && is_shadow_pass) {
        // 只在阴影渲染过程中应用偏移
        VERTEX.xy += rotate_point(shadow_offset, -sprite_rotation);
    }

    // 浮动效果
    if (enable_float_effect) {
        float time = TIME * float_frequency + time_offset;
        float offset = sin(time) * float_amplitude;

        // 只在Y轴上应用浮动
        VERTEX.y += offset;

        // 添加轻微的旋转效果
        float rotation = sin(time) * 0.01; // 非常轻微的旋转
        vec2 center = vec2(0.5, 0.5);
        vec2 centered_vertex = VERTEX - center;
        float s = sin(rotation);
        float c = cos(rotation);
        VERTEX.x = centered_vertex.x * c - centered_vertex.y * s + center.x;
        VERTEX.y = centered_vertex.x * s + centered_vertex.y * c + center.y;
    }

    // 悬停效果
    if (enable_hover_effect && current_scale > 1.0) {
        // 从中心点缩放
        vec2 center = vec2(0.5, 0.5);
        VERTEX = (VERTEX - center) * current_scale + center;
    }
}

vec2 rotate_uv(vec2 uv, vec2 center, float rotation, bool use_degrees){
		float _angle = rotation;
		if(use_degrees){
			_angle = rotation * (3.1415926/180.0);
		}
		mat2 _rotation = mat2(
			vec2(cos(_angle), -sin(_angle)),
			vec2(sin(_angle), cos(_angle))
		);
		vec2 _delta = uv - center;
		_delta = _rotation * _delta;
		return _delta + center;
	}

void fragment() {
    // 获取原始纹理颜色
    vec4 original_color = texture(TEXTURE, UV);
    vec4 final_color = original_color;
    vec2 final_uv = UV;

    // 阴影效果处理
    if (enable_shadow_effect && is_shadow_pass) {
        // 在阴影渲染过程中，将所有非透明像素转换为阴影颜色
        final_color = vec4(shadow_color.rgb, original_color.a * shadow_color.a);
    } else {
        // 边框效果
        if (enable_border_effect) {
            vec2 centerDistVec = vec2(0.5) - final_uv;

            float distToCircleEdge = length(centerDistVec) * border_radius;
            float distToSquareEdge = 0.5*(0.5 - min(min(final_uv.x, 1.0 - final_uv.x), min(final_uv.y, 1.0 - final_uv.y)));
            float distToEdge = mix(distToCircleEdge, distToSquareEdge, border_shape);

            float gradient = smoothstep(0.5, 0.5 - border_radius, distToEdge);

            vec2 direction = vec2(0, 1) * border_burn_speed;
            float noiseValue = texture(border_noise_texture, final_uv + direction * TIME).r;

            float opacity = step(border_radius, mix(gradient, noiseValue, border_effect_control) - distToEdge);
            final_color *= vec4(1.0, 1.0, 1.0, opacity);
        }

        // 3D效果处理
        if (enable_3d_effect) {
            // map skew to [-0.5, 0.5]
            float skew_x = mouse_position.x / card_width;
            float skew_y = mouse_position.y / card_height;

            // map to [-0.5, 0.5]
            vec2 uv = UV;
            uv.x = (uv.x - 0.5);
            uv.y = (uv.y - 0.5);

            // ratio - how far are current point from mouse position
            float sx = 1.0 - (uv.x * skew_x);
            float sy = 1.0 - (uv.y * skew_y);

            // calculate z (depth) depending on ratio
            float z = 1.0 + (sx * sy) / 2.0;
            // correct perspective for given point
            uv.x = uv.x / z;
            uv.y = uv.y / z;

            // scale a bit down and reset mapping from [-0.5, 0.5] to [0, 1]
            uv.x = uv.x / 0.45 + 0.5;
            uv.y = uv.y / 0.45 + 0.5;

            // 如果uv超出范围，设置为透明
            if (uv.x < 0.0 || uv.x > 1.0 || uv.y < 0.0 || uv.y > 1.0) {
                final_color.a = 0.0;
            } else {
                // 应用亮度调整
                float brightness = 1.0 - mouse_position.y / (card_height / 2.0) * 0.2;
                final_color = texture(TEXTURE, uv);
                final_color.rgb *= brightness;
            }

            // 更新UV坐标用于后续效果
            final_uv = uv;
        }

        // 溶解效果
        if (enable_dissolve_effect) {
            vec4 noise = texture(dissolve_noise_texture, final_uv);

            // dissolve_progress从1到0时，卡牌从完全显示到完全消失
            float dissolve_edge_width = 0.05; // 边缘宽度
            float edge_threshold = dissolve_progress - dissolve_edge_width;

            if (noise.r < dissolve_progress) {
                // 在边缘附近添加发光效果
                if (noise.r > edge_threshold && edge_threshold >= 0.0) {
                    // 计算边缘强度（0到1之间）
                    float edge_intensity = 1.0 - (dissolve_progress - noise.r) / dissolve_edge_width;

                    // 添加边缘发光（使用橙色作为发光颜色）
                    vec3 edge_color = vec3(0.0, 1.0, 1.0); // 橙色发光
                    final_color.rgb = mix(final_color.rgb, edge_color, edge_intensity * 0.7);
                    final_color.rgb *= 1.2; // 增加亮度
                }
            } else {
                // 完全透明的部分
                final_color.a = 0.0;
            }
        }

        // BUFF特效（闪光效果） 暂时禁用
        if (enable_buff_effect && false) {
            float glitter_highlight_map = mix(
                texture(buff_noise_texture, (local_vert * 0.005) + TIME * highlight_speed * 0.01).r,
                sin((local_vert.x + local_vert.y) * 0.05 + TIME * highlight_speed) * 0.5 + 1.0,
                float(highlight_band)
            );

            float glitter = smoothstep(
                (1.0 - glitter_size),
                1.0 - (glitter_size * glitter_hardness),
                texture(buff_noise_texture, final_uv).r
            ) * (1.0 - (glitter_highlight_map * clamp(highlight_intensity, 0.0, 0.65)));

            final_color = mix(final_color, glitter_color, glitter * final_color.a);
        }

        // 悬停效果（高亮部分）
        if (enable_hover_effect && is_hovered) {
            float highlight = 0.1;
            final_color.rgb += vec3(highlight, highlight, highlight);
        }

        // 金色闪光效果
        if (enable_gold_flash_effect) {
            // 计算闪烁强度
            float intensity = mix(min_intensity, max_intensity, (sin(TIME * flash_speed) + 1.0) / 2.0);

            // 计算到边缘的距离
            vec2 center_dist = abs(final_uv - vec2(0.5, 0.5)) * 2.0;
            float dist = max(center_dist.x, center_dist.y);

            // 创建边缘效果 - 只在卡片边缘显示金色框
            float edge_effect = smoothstep(1.0 - edge_width, 1.0, dist) * intensity * 2.0;

            // 创建整体轻微闪光效果 - 减小整体效果，突出边缘
            float overall_effect = intensity * 0.05;

            // 混合金色效果 - 优先应用边缘效果，使边缘更加明亮
            vec3 gold_effect = mix(final_color.rgb, gold_color.rgb * 1.2, edge_effect);
            gold_effect = mix(gold_effect, final_color.rgb, overall_effect);

            // 更新最终颜色
            final_color.rgb = gold_effect;
        }

        // 波纹效果 (旧逻辑，将被整合到 BUFF Overlay)
        // if (enable_ripple_effect) {
        //     vec2 center_position = -1.0 + 2.0 * UV / (1.0 / TEXTURE_PIXEL_SIZE);
        //     float center_distance = length(center_position);

        //     float ripple = sin(center_distance * -ripple_frequency * PI + ripple_rate * TIME) * ripple_amplitude / (center_distance + 1.0);

        //     vec2 uv = FRAGCOORD.xy / (1.0 / SCREEN_PIXEL_SIZE).xy + (center_position/center_distance) * ripple * wave_amplitude;
        //     vec2 background_wave = wave(uv, TIME);
        //     vec4 background_texture = texture(SCREEN_TEXTURE, background_wave) * sqrt(ripple_amplitude);

        //     float alpha_scalar = (1.0 - min(center_distance, 1.0)) * background_texture.x * 2.5;

        //     background_texture.a *= 1.0 * alpha_scalar * (ripple + background_texture.x * background_texture.y);
        //     background_texture.a = max(background_texture.a - (background_texture.y * 0.45), 0.0);

        //     // 混合波纹效果
        //     if (background_texture.a > 0.0) {
        //         final_color = vec4(background_texture.xyz * ripple_color.rgb, background_texture.a * ripple_color.a);
        //     }
        // }

        // 闪光效果
        if (enable_shine_effect) {
            vec2 center_uv = final_uv - vec2(0.5, 0.5);
            float gradient_to_edge = max(abs(center_uv.x), abs(center_uv.y));
            gradient_to_edge = gradient_to_edge * Distortion;
            gradient_to_edge = 1.0 - gradient_to_edge;
            vec2 rotaded_uv = rotate_uv(final_uv, vec2(0.5, 0.5), Rotation_deg, true);

            float remapped_position;
            {
                float output_range = Position_Max - Position_Min;
                remapped_position = Position_Min + output_range * Position;
            }

            float remapped_time = TIME * Speed + remapped_position;
            remapped_time = fract(remapped_time);
            {
                remapped_time = -2.0 + 4.0 * remapped_time;
            }

            vec2 offset_uv = vec2(rotaded_uv.xy) + vec2(remapped_time, 0.0);
            float line = vec3(offset_uv, 0.0).x;
            line = abs(line);
            line = gradient_to_edge * line;
            line = sqrt(line);

            float line_smoothness = clamp(Line_Smoothness, 0.001, 1.0);
            float offset_plus = Line_Width + line_smoothness;
            float offset_minus = Line_Width - line_smoothness;

            float remapped_line;
            {
                float input_range = offset_minus - offset_plus;
                remapped_line = (line - offset_plus) / input_range;
            }
            remapped_line *= Brightness;
            remapped_line = clamp(remapped_line, 0.0, Alpha);

            // 使用screen混合模式添加白色闪光
            vec3 shine = vec3(remapped_line);
            final_color.rgb = vec3(1.0) - (vec3(1.0) - final_color.rgb) * (vec3(1.0) - shine);
        }

        // 应用边缘模糊（浮动效果的一部分）
        if (enable_float_effect) {
            float edge = smoothstep(0.0, 0.01, final_uv.x) * smoothstep(0.0, 0.01, final_uv.y) *
                         smoothstep(0.0, 0.01, 1.0 - final_uv.x) * smoothstep(0.0, 0.01, 1.0 - final_uv.y);
            final_color.a *= edge;
        }

        // --- NEW: BUFF Overlay Blending with Ripple and Sunburst ---
        if (enable_buff_overlay) {
            // 1. 计算基础 BUFF UV
            vec2 base_buff_uv = final_uv * buff_tile_scale;

            // 2. 应用波纹扰动到 BUFF UV
            vec2 distorted_buff_uv = vec2(
                base_buff_uv.x + sin(base_buff_uv.y * wave_frequency * 2.0 + TIME * ripple_rate * 0.5) * wave_amplitude * 0.5,
                base_buff_uv.y + sin(base_buff_uv.x * wave_frequency * 2.0 + TIME * ripple_rate * 0.5) * wave_amplitude * 0.5
            );

            // 3. 使用扰动后的 UV 采样 BUFF 纹理
            vec4 buff_tex_color = texture(buff_texture, distorted_buff_uv);

            // 4. 计算基础噪声
            float base_noise = texture(buff_noise, base_buff_uv * 0.5 + vec2(TIME * 0.05)).r;

            // 5. --- 新增: 柔和的动态泛光效果 ---
            // 计算到中心的距离
            vec2 center_uv = final_uv - vec2(0.5, 0.5);
            float dist_to_center = length(center_uv);

            // 创建柔和的径向渐变基础
            float radial_gradient = smoothstep(0.8, 0.0, dist_to_center);

            // 添加轻微的脉动效果
            float pulse = 0.6 + 0.15 * sin(TIME * 1.8);
            radial_gradient *= pulse;

            // 添加流动的噪声扰动以增加动感
            float flow_noise1 = texture(buff_noise, vec2(
                base_buff_uv.x * 0.7 + TIME * 0.1,
                base_buff_uv.y * 0.7 - TIME * 0.08
            )).r;

            float flow_noise2 = texture(buff_noise, vec2(
                base_buff_uv.x * 1.3 - TIME * 0.12,
                base_buff_uv.y * 1.3 + TIME * 0.09
            )).r;

            // 混合多层噪声以创建更有机的动态效果
            float combined_noise = mix(flow_noise1, flow_noise2, 0.5);
            combined_noise = mix(combined_noise, base_noise, 0.3);

            // 将噪声应用到径向渐变，创建不规则但柔和的光晕
            float glow_mask = radial_gradient * (0.7 + 0.5 * combined_noise);

            // 在边缘添加额外的波动效果
            float edge_pulse = sin(dist_to_center * 8.0 - TIME * 2.0) * 0.5 + 0.5;
            float edge_effect = smoothstep(0.6, 0.4, dist_to_center) * smoothstep(0.2, 0.4, dist_to_center);
            glow_mask += edge_effect * edge_pulse * 0.15;

            // 创建最终的泛光颜色
            vec3 glow_color = buff_color.rgb * (1.0 + 0.3 * combined_noise);

            // 计算最终alpha和颜色混合
            float buff_alpha = buff_tex_color.a * buff_color.a;
            // 确保泛光效果在半透明区域也可见
            buff_alpha = max(buff_alpha, glow_mask * 0.7);

            // 混合到最终颜色，使用glow_mask控制强度
            vec3 buff_rgb = mix(
                buff_tex_color.rgb * buff_color.rgb,
                glow_color,
                glow_mask
            );

            // 6. 混合到最终颜色
            final_color.rgb = mix(final_color.rgb, buff_rgb, buff_alpha);
        }
        // --- BUFF Overlay End ---

        // 禁止效果 (应用在所有其他效果之后，最后一个应用)
        if (enable_forbidden_effect) {
            // 计算禁止图标的中心缩放UV
            vec2 center = vec2(0.5, 0.5);
            vec2 forbidden_uv = (final_uv - center) / forbidden_scale + center;

            // 如果UV在有效范围内，则采样禁止纹理
            if (forbidden_uv.x >= 0.0 && forbidden_uv.x <= 1.0 &&
                forbidden_uv.y >= 0.0 && forbidden_uv.y <= 1.0) {

                // 采样禁止纹理
                vec4 forbidden_color = texture(forbidden_texture, forbidden_uv);

                // 脉动效果
                float pulse = mix(
                    forbidden_pulse_min,
                    forbidden_pulse_max,
                    (sin(TIME * forbidden_pulse_speed) + 1.0) * 0.5
                );

                // 应用脉动到不透明度
                float dynamic_opacity = forbidden_opacity * pulse;

                // 混合禁止图标和卡牌
                if (forbidden_color.a > 0.0) {
                    final_color.rgb = mix(
                        final_color.rgb,
                        forbidden_color.rgb,
                        forbidden_color.a * dynamic_opacity
                    );
                }
            }
        }
    }

    COLOR = final_color;
}
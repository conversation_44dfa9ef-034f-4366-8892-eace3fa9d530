shader_type canvas_item;

// 浮动效果参数
uniform float amplitude : hint_range(0.0, 10.0) = 3.0;  // 浮动幅度
uniform float frequency : hint_range(0.1, 5.0) = 1.0;    // 浮动频率
uniform float time_offset = 0.0;                        // 时间偏移，用于错开多个卡牌的浮动

void vertex() {
    // 应用垂直浮动效果
    float time = TIME * frequency + time_offset;
    float offset = sin(time) * amplitude;

    // 只在Y轴上应用浮动
    VERTEX.y += offset;

    // 添加轻微的旋转效果
    float rotation = sin(time) * 0.01; // 非常轻微的旋转
    vec2 center = vec2(0.5, 0.5);
    vec2 centered_vertex = VERTEX - center;
    float s = sin(rotation);
    float c = cos(rotation);
    VERTEX.x = centered_vertex.x * c - centered_vertex.y * s + center.x;
    VERTEX.y = centered_vertex.x * s + centered_vertex.y * c + center.y;
}

void fragment() {
    // 获取原始颜色
    vec4 original_color = texture(TEXTURE, UV);

    // 计算UV坐标的边缘模糊
    float edge = smoothstep(0.0, 0.01, UV.x) * smoothstep(0.0, 0.01, UV.y) *
                 smoothstep(0.0, 0.01, 1.0 - UV.x) * smoothstep(0.0, 0.01, 1.0 - UV.y);

    // 应用边缘模糊到颜色
    COLOR = original_color * edge;
}
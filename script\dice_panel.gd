extends Panel

# 骰子面板 - 处理骰子界面的逻辑

# 信号
signal dice_confirmed(limit_score)
signal dice_total_value_changed(total_value)  # 新增信号用于传递骰子总值

# 节点引用
@onready var dice_icons_container = $TopInfo/DiceIconsContainer
@onready var limit_score_label = $TopInfo/ScoreContainer/LimitScoreLabel
@onready var fixed_limit_label = $TopInfo/FixedLimitLabel
@onready var dice_sum_label = $TopInfo/DiceContainer/DiceSumLabel
@onready var score_container = $TopInfo/ScoreContainer
@onready var score_icon = $TopInfo/ScoreContainer/ScoreIcon
@onready var dice_container = $CenterContainer/DiceContainer
@onready var dice1 = $CenterContainer/DiceContainer/Dice1
@onready var dice2 = $CenterContainer/DiceContainer/Dice2
@onready var dice3 = $CenterContainer/DiceContainer/Dice3
@onready var dice4 = $CenterContainer/DiceContainer/Dice4
@onready var dice5 = $CenterContainer/DiceContainer/Dice5
@onready var dice6 = $CenterContainer/DiceContainer/Dice6
@onready var dice7 = $CenterContainer/DiceContainer/Dice7
@onready var dice8 = $CenterContainer/DiceContainer/Dice8
@onready var dice9 = $CenterContainer/DiceContainer/Dice9
@onready var dice10 = $CenterContainer/DiceContainer/Dice10
@onready var roll_button = $BottomButtons/RollButton
@onready var confirm_button = $BottomButtons/ConfirmButton

# 动画变量
var panel_original_position = Vector2.ZERO
var panel_hidden_position = Vector2.ZERO
var is_animating = false

# 骰子图标跳动动画变量
var icon_bounce_timer = 0.0
var icon_bounce_interval = 0.3  # 每0.5秒跳动一次
var icon_bounce_duration = 0.3  # 跳动动画持续0.5秒
var icon_round_pause = 2.0  # 每轮结束后暂停2秒
var is_icon_bouncing = false
var current_bounce_index = -1  # 当前跳动的图标索引
var next_bounce_index = 0      # 下一个要跳动的图标索引
var is_round_pausing = false   # 是否正在阶间暂停

# 骰子纹理
const DICE_TEXTURE = preload("res://assert/dict/UpscaledDice_Pink.png")
const DICE_FRAMES_PER_ROW = 6
const DICE_FRAME_SIZE = Vector2(112, 112)  # 更新为实际的帧大小

# 骰子图标纹理
const DICE_ICON = preload("res://assert/dict/dice.png")
const DICE_ICON_SIZE = Vector2(24, 24)
const DICE_ICON_SPACING = 5

# 骰子图标数组
var dice_icons = []

# 骰子动画时间
const ANIMATION_TIME = 1.0

# 变量
var is_rolling = false
var dice_values = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
var animation_timer = 0.0
var animation_speed = 0.02  # 帧动画更快
var animation_frames = 0
var current_animation_frame = 0  # 当前动画帧索引

# 骰子停止状态跟踪
var dice_stopped = [false, false, false, false, false, false, false, false, false, false]
var current_stop_index = 0
var stop_delay = 0.2  # 从0.2改为0.1，使骰子停止间隔更短

# 积分动画变量
var current_displayed_score = 0  # 当前显示的分数
var target_score = 0             # 目标分数
var score_animation_speed = 30   # 每秒变化的分数
var is_score_animating = false   # 是否正在执行分数动画
var pending_score_deductions = [] # 等待扣减的分数列表

# 存储骰子光芒效果的面板信息
var glow_panels = []
var glow_original_colors = []
var glow_pulse_timers = []
var glow_pulse_directions = []
var is_glow_effect_active = false

# 骰子动画变量
var dice_original_positions = {}  # 存储骰子的原始位置
var dice_animation_progress = {}  # 存储每个骰子的动画进度
var JUMP_HEIGHT = 50  # 跳跃高度（像素）
var ANIMATION_DURATION = 0.3  
var dice_node_indices = {}  # 存储骰子节点和其索引的映射

# 骰子总和动画变量
var current_displayed_sum = 0  # 当前显示的总和
var target_sum = 0            # 目标总和
var sum_animation_speed = 50   # 每秒变化的数值
var is_sum_animating = false   # 是否正在执行总和动画

# 骰子阴影变量
var dice_shadows = {}
var shadow_offset = Vector2(0, 5)  # 修改阴影偏移，只在垂直方向有偏移

# 骰子布局变量
var base_dice_spacing = 15  # 基础骰子间距
var max_dice_per_row = 5  # 每行最多显示的骰子数
var min_scale = 0.6  # 最小缩放比例
var max_scale = 1.0  # 最大缩放比例
var horizontal_margin = 40  # 水平边距
var vertical_margin = 30   # 垂直边距
var base_scale = Vector2(1.0, 1.0)  # 基础缩放比例

# 脉冲动画变量
var pulse_tween = null

# 按钮纹理
const ROLL_BUTTON_TEXTURE = preload("res://assert/dice_panel/roll_button.png")
const CONFIRM_BUTTON_TEXTURE = preload("res://assert/dice_panel/confirm_button.png")

# 初始化
func _ready():
    # 连接按钮信号
    roll_button.pressed.connect(_on_roll_button_pressed)
    confirm_button.pressed.connect(_on_confirm_button_pressed)
    
    # 初始化骰子图标
    _init_dice_icons()
    
    # 连接GameManager的信号
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        game_manager.dice_count_changed.connect(_on_dice_count_changed)
        game_manager.dice_rolls_changed.connect(_on_dice_rolls_changed)
    
    # 初始不可见
    visible = false
    
    # 初始化积分标签
    limit_score_label.scale = Vector2(1.0, 1.0)
    limit_score_label.pivot_offset = limit_score_label.size / 2
    
    # 初始化骰子模糊材质
    _init_blur_materials()
    
    # 初始隐藏面板
    visible = false
    modulate.a = 0
    
    # 设置面板圆角
    custom_minimum_size = Vector2(500, 300)
    size_flags_horizontal = Control.SIZE_EXPAND_FILL
    size_flags_vertical = Control.SIZE_EXPAND_FILL
    
    # 初始隐藏所有骰子
    dice1.visible = false
    dice2.visible = false
    dice3.visible = false
    dice4.visible = false
    dice5.visible = false
    dice6.visible = false
    dice7.visible = false
    dice8.visible = false
    dice9.visible = false
    dice10.visible = false
    
    # 确保dice_values数组初始化为正确大小
    dice_values = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
    
    # 设置面板位置变量
    panel_original_position = Vector2(0, 0) # 将在show_dice_panel中更新
    panel_hidden_position = Vector2(0, -300) # 将在show_dice_panel中更新
    
    # 设置按钮为圆形并添加交互效果
    _setup_round_buttons()
    
    # 设置骰子交互
    _setup_dice_interaction()
    
    # 初始化所有骰子精灵
    _setup_dice_sprite(dice1)
    _setup_dice_sprite(dice2)
    _setup_dice_sprite(dice3)
    _setup_dice_sprite(dice4)
    _setup_dice_sprite(dice5)
    _setup_dice_sprite(dice6)
    _setup_dice_sprite(dice7)
    _setup_dice_sprite(dice8)
    _setup_dice_sprite(dice9)
    _setup_dice_sprite(dice10)
    
# 初始化骰子模糊材质
func _init_blur_materials():
    # 创建模糊效果材质
    for i in range(10):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            # 移除之前可能存在的材质
            dice_node.material = null

# 应用或移除骰子模糊效果
func _apply_blur_effect(dice_node: TextureRect, enable: bool):
    if not dice_node:
        return
        
    if enable:
        # 创建模糊效果材质
        var blur_material = ShaderMaterial.new()
        blur_material.shader = load("res://shader/blur_shader.gdshader")
        
        # 设置着色器参数
        blur_material.set_shader_parameter("blur_amount", 3.0)
        blur_material.set_shader_parameter("motion_speed", 70.0)
        
        # 应用材质
        dice_node.material = blur_material
    else:
        # 移除模糊效果
        dice_node.material = null

# 初始化骰子图标
func _init_dice_icons():
    # 清除现有图标
    for icon in dice_icons:
        if is_instance_valid(icon):
            icon.queue_free()
    dice_icons.clear()
    
    # 创建10个骰子图标
    for i in range(10):
        # 创建容器来包含阴影和图标
        var container = Control.new()
        container.custom_minimum_size = DICE_ICON_SIZE
        container.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
        container.size_flags_vertical = Control.SIZE_SHRINK_CENTER
        
        # 创建阴影
        var shadow = TextureRect.new()
        shadow.texture = DICE_ICON
        shadow.custom_minimum_size = DICE_ICON_SIZE
        shadow.expand_mode = 1  # 保持纵横比
        shadow.stretch_mode = 5  # 保持纵横比并居中
        shadow.modulate = Color(0, 0, 0, 0.3)  # 半透明黑色
        shadow.position = Vector2(6, 2)  # 阴影偏移
        container.add_child(shadow)
        
        # 创建主图标
        var icon = TextureRect.new()
        icon.texture = DICE_ICON
        icon.custom_minimum_size = DICE_ICON_SIZE
        icon.expand_mode = 1  # 保持纵横比
        icon.stretch_mode = 5  # 保持纵横比并居中
        
        # 设置正确的缩放和锚点
        icon.scale = Vector2(1.0, 1.0)
        icon.pivot_offset = DICE_ICON_SIZE / 2  # 设置缩放的中心点
        container.add_child(icon)
        
        # 设置容器的缩放中心点
        container.pivot_offset = DICE_ICON_SIZE / 2
        
        # 添加到容器
        dice_icons_container.add_child(container)
        dice_icons.append(container)  # 现在存储的是容器而不是单独的图标

# 开始骰子图标跳动动画
func _start_icon_bounce_animation():
    # 检查是否有可用的骰子图标
    var game_manager = get_node("/root/GameManager")
    if not game_manager or game_manager.dice_rolls_left <= 0:
        return
    
    # 标记正在进行跳动动画
    is_icon_bouncing = true
    
    # 获取可用的骰子图标索引
    var available_indices = []
    for i in range(min(dice_icons.size(), game_manager.dice_rolls_chance)):
        if i < game_manager.dice_rolls_left and dice_icons[i].visible:
            available_indices.append(i)
    
    # 如果没有可用的骰子图标，不进行动画
    if available_indices.size() == 0:
        is_icon_bouncing = false
        return
    
    # 判断是否是新一轮的开始
    var is_new_round = (next_bounce_index == 0)
    
    # 按顺序选择下一个要跳动的图标
    # 如果next_bounce_index超出可用范围，则说明一轮结束，需要暂停
    if next_bounce_index >= available_indices.size():
        # 一轮已结束，设置暂停状态
        is_icon_bouncing = false
        is_round_pausing = true
        icon_bounce_timer = 0.0  # 重置计时器开始暂停计时
        return
    
    # 设置当前要跳动的图标索引
    current_bounce_index = available_indices[next_bounce_index]
    
    # 更新下一个要跳动的图标索引
    next_bounce_index = next_bounce_index + 1
    
    # 获取要跳动的图标
    var icon = dice_icons[current_bounce_index]
    
    # 创建跳动动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 先放大
    tween.tween_property(icon, "scale", Vector2(1.3, 1.3), icon_bounce_duration * 0.3)
    # 然后恢复
    tween.tween_property(icon, "scale", Vector2(1.0, 1.0), icon_bounce_duration * 0.7)
    
    # 动画完成后重置状态
    tween.finished.connect(func():
        is_icon_bouncing = false
        current_bounce_index = -1
    )

# 更新骰子图标显示
func update_dice_icons(rolls_left: int, max_rolls: int):
    # 确保已初始化图标
    if dice_icons.size() == 0:
        _init_dice_icons()
    
    # 更新每个图标的状态
    for i in range(dice_icons.size()):
        if i < max_rolls:
            # 只显示当前最大可用数量的图标
            dice_icons[i].visible = true
            
            if i < rolls_left:
                # 点亮图标（可用骰子）
                dice_icons[i].modulate = Color(1, 1, 1, 1)
                # 确保图标有正确的比例
                dice_icons[i].scale = Vector2(1.0, 1.0)
            else:
                # 灰暗图标（已用骰子）
                dice_icons[i].modulate = Color(0.5, 0.5, 0.5, 0.5)
        else:
            # 隐藏超出最大数量的图标
            dice_icons[i].visible = false

# 显示骰子面板
func show_dice_panel():
    AudioManager.play_sfx("res://assert/audio/sfx/dice_panel_up.mp3")
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:     
        # 确保图标数组已初始化
        if dice_icons.size() == 0:
            _init_dice_icons()
            
        # 清除上一次骰子的边框效果
        _clear_all_dice_outlines()
            
        # 更新骰子图标 - 在新阶开始时，所有图标应该被重置为点亮状态
        # 注意：在准备阶段，dice_rolls_left应该等于dice_rolls_chance
        update_dice_icons(game_manager.dice_rolls_left, game_manager.dice_rolls_chance)
        
        # 重置骰子图标跳动动画计时器
        icon_bounce_timer = 0.0
        is_icon_bouncing = false
        is_round_pausing = false
        current_bounce_index = -1
        next_bounce_index = 0  # 重置为从第一个可用骰子开始跳动
        
        # 重置积分标签的显示状态
        limit_score_label.modulate = Color(1, 1, 1, 1)
        limit_score_label.scale = Vector2(1.0, 1.0)
        
        # 初始显示骰子总和为"0"
        dice_sum_label.text = "0"
        current_displayed_sum = 0
        target_sum = 0
        is_sum_animating = false
        
        # 更新按钮状态
        roll_button.disabled = game_manager.dice_rolls_chance <= 0
        _update_button_disabled_state(roll_button)
        
        # 初始禁用确认按钮，只有掷过骰子后才能点击
        confirm_button.disabled = true
        _update_button_disabled_state(confirm_button)
        
        # 初始化dice_values数组，确保与当前骰子数量匹配
        dice_values = []
        for i in range(game_manager.dice_count):
            dice_values.append(1)
        
        # 重置骰子停止状态
        dice_stopped = []
        for i in range(game_manager.dice_count):
            dice_stopped.append(false)
        current_stop_index = 0
        
        # 根据当前骰子数量更新骰子显示
        update_dice_count(game_manager.dice_count)
    
    # 显示面板并播放弹出动画
    visible = true
    
    # 获取视口大小和面板大小，计算中心位置
    var viewport_rect = get_viewport_rect().size
    var panel_size = size
    var center_position = Vector2(
        (viewport_rect.x - panel_size.x) / 2,
        (viewport_rect.y - panel_size.y) / 2
    )
    
    # 更新位置变量 - 确保面板总是显示在屏幕中央
    panel_original_position = center_position
    panel_hidden_position = Vector2(center_position.x, center_position.y - 300)
    
    # 设置初始位置在屏幕上方
    global_position = panel_hidden_position
    modulate.a = 0
    
    # 创建弹出动画
    is_animating = true
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK) # 使用BACK过渡类型实现Q弹效果
    
    # 从上方弹出
    tween.tween_property(self, "global_position", panel_original_position, 0.5)
    tween.parallel().tween_property(self, "modulate:a", 1.0, 0.3)
    
    await tween.finished
    is_animating = false

# 处理过程
func _process(delta):
    # 处理骰子总和动画
    if is_sum_animating:
        var sum_change = sum_animation_speed * delta
        
        if current_displayed_sum > target_sum:
            # 总和减少
            current_displayed_sum -= sum_change
            if current_displayed_sum <= target_sum:
                current_displayed_sum = target_sum
                is_sum_animating = false
        elif current_displayed_sum < target_sum:
            # 总和增加
            current_displayed_sum += sum_change
            if current_displayed_sum >= target_sum:
                current_displayed_sum = target_sum
                is_sum_animating = false
        
        # 更新显示
        dice_sum_label.text = str(int(current_displayed_sum))
        
        # 动画完成后，播放完成效果
        if not is_sum_animating and current_displayed_sum == target_sum:
            _play_sum_complete_effect()
    
    # 处理积分动画
    if is_score_animating:
        var score_change = score_animation_speed * delta
        
        if current_displayed_score > target_score:
            # 分数减少
            current_displayed_score -= score_change
            if current_displayed_score <= target_score:
                current_displayed_score = target_score
                is_score_animating = false
        elif current_displayed_score < target_score:
            # 分数增加
            current_displayed_score += score_change
            if current_displayed_score >= target_score:
                current_displayed_score = target_score
                is_score_animating = false
        
        # 更新显示
        limit_score_label.text = str(int(current_displayed_score))
        
        # 动画完成后，可能需要播放完成效果
        if not is_score_animating and current_displayed_score == target_score:
            _play_score_complete_effect()
    
    # 处理骰子图标跳动动画
    if visible and not is_rolling and dice_icons.size() > 0:
        # 只有面板可见且不在掷骰子动画中才处理跳动动画
        icon_bounce_timer += delta
        
        if is_round_pausing:
            # 正在轮次间暂停
            if icon_bounce_timer >= icon_round_pause:
                # 暂停结束，重置状态开始新一轮
                icon_bounce_timer = 0.0
                is_round_pausing = false
                next_bounce_index = 0  # 重置为从第一个可用骰子开始
        elif not is_icon_bouncing:
            # 没有正在进行的跳动动画且不在暂停中
            if icon_bounce_timer >= icon_bounce_interval:
                # 重置计时器
                icon_bounce_timer = 0.0
                # 开始跳动动画
                _start_icon_bounce_animation()
    
    if is_rolling:
        # 确保骰子在动画期间可见，但只显示当前骰子数量允许的骰子
        var game_manager = get_node("/root/GameManager")
        if game_manager:
            if not dice1.visible and game_manager.dice_count >= 1:
                dice1.visible = true
            if not dice2.visible and game_manager.dice_count >= 2:
                dice2.visible = true
            if not dice3.visible and game_manager.dice_count >= 3:
                dice3.visible = true
            if not dice4.visible and game_manager.dice_count >= 4:
                dice4.visible = true
            if not dice5.visible and game_manager.dice_count >= 5:
                dice5.visible = true
            if not dice6.visible and game_manager.dice_count >= 6:
                dice6.visible = true
            if not dice7.visible and game_manager.dice_count >= 7:
                dice7.visible = true
            if not dice8.visible and game_manager.dice_count >= 8:
                dice8.visible = true
            if not dice9.visible and game_manager.dice_count >= 9:
                dice9.visible = true
            if not dice10.visible and game_manager.dice_count >= 10:
                dice10.visible = true
        
        # 在动画期间显示限制积分和骰子总和为"--"
        if game_manager:
            # limit_score_label.text = "限制积分: --"
            dice_sum_label.text = "--"
        
        animation_timer += delta
        
        if animation_timer >= animation_speed:
            animation_timer = 0.0
            animation_frames += 1
            
            # 更新动画帧
            current_animation_frame = (current_animation_frame + 1) % DICE_FRAMES_PER_ROW
            
            # 更新每个骰子的帧动画
            if game_manager:
                if game_manager.dice_count >= 1:
                    _update_dice_animation(dice1, dice_stopped[0])
                if game_manager.dice_count >= 2:
                    _update_dice_animation(dice2, dice_stopped[1])
                if game_manager.dice_count >= 3:
                    _update_dice_animation(dice3, dice_stopped[2])
                if game_manager.dice_count >= 4:
                    _update_dice_animation(dice4, dice_stopped[3])
                if game_manager.dice_count >= 5:
                    _update_dice_animation(dice5, dice_stopped[4])
                if game_manager.dice_count >= 6:
                    _update_dice_animation(dice6, dice_stopped[5])
                if game_manager.dice_count >= 7:
                    _update_dice_animation(dice7, dice_stopped[6])
                if game_manager.dice_count >= 8:
                    _update_dice_animation(dice8, dice_stopped[7])
                if game_manager.dice_count >= 9:
                    _update_dice_animation(dice9, dice_stopped[8])
                if game_manager.dice_count >= 10:
                    _update_dice_animation(dice10, dice_stopped[9])
            
            # 从左至右逐一停止骰子
            if animation_frames >= 16 and current_stop_index < game_manager.dice_count:
                if animation_frames % int(stop_delay / animation_speed) == 0:
                    # 停止当前索引的骰子
                    dice_stopped[current_stop_index] = true
                    
                    # 创建粒子特效
                    _create_dice_stop_effect(current_stop_index)
                    
                    # 更新骰子显示
                    var dice_node = _get_dice_by_index(current_stop_index)
                    if dice_node and current_stop_index < dice_values.size():
                        # 设置最终的骰子值
                        _update_dice_animation(dice_node, true)
                        
                        # 移除模糊效果
                        _apply_blur_effect(dice_node, false)
                        
                        # 创建从骰子到积分的扣减动画
                        _create_score_deduction_effect(dice_node, dice_values[current_stop_index])
                    
                    # 移动到下一个骰子
                    current_stop_index += 1
            
            # 动画结束条件：所有骰子都已停止
            if current_stop_index >= game_manager.dice_count and animation_frames >= 10:
                is_rolling = false
                animation_frames = 0
                current_animation_frame = 0
                
                # 显示最终结果
                update_dice_display()
                
                # 动画结束后显示实际限制积分和骰子总和
                if game_manager:
                    # 计算并显示固定限制积分
                    var fixed_limit = game_manager.first_round_score
                    if game_manager.current_round > 1:
                        fixed_limit = game_manager.first_round_score + (game_manager.current_round - 1) * 5
                    fixed_limit_label.text = "固定积分: %d" % fixed_limit
                    
                    # 计算并显示骰子总和，确保计算所有骰子
                    var dice_sum = 0
                    for i in range(game_manager.dice_count):
                        if i < dice_values.size():
                            dice_sum += dice_values[i]
                    dice_sum_label.text = "%d" % dice_sum
                    
                    # 确保最终积分值正确
                    var final_score = fixed_limit - dice_sum
                    current_displayed_score = final_score
                    limit_score_label.text = str(final_score)
                    
                    # 更新游戏管理器的限制积分值
                    # game_manager.limit_score = final_score
                    
                    # 根据剩余掷骰子次数更新掷骰子按钮状态
                    roll_button.disabled = game_manager.dice_rolls_left <= 0
                    _update_button_disabled_state(roll_button)
                
                # 动画结束后启用确认按钮
                confirm_button.disabled = false
                _update_button_disabled_state(confirm_button)
                AudioManager.stop_sfx()
            

    # 处理光芒面板闪烁效果
    if is_glow_effect_active:
        _update_glow_panels(delta)

# 掷骰子按钮点击
func _on_roll_button_pressed():
    # 掷骰子音效
    AudioManager.play_dice_roll_sfx()
    
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager or game_manager.dice_rolls_left <= 0:
        return
    
    # 立即禁用掷骰子按钮，防止重复点击
    roll_button.disabled = true
    _update_button_disabled_state(roll_button)
    
    # 清除所有骰子的边框特效
    _clear_all_dice_outlines()
    
    # 为所有骰子应用模糊效果
    # for i in range(10):
    #     var dice_node = _get_dice_by_index(i)
    #     if dice_node and i < game_manager.dice_count:
    #         dice_node.visible = true
    #         _apply_blur_effect(dice_node, true)
    
    # 开始骰子动画
    _start_dice_animation()
    
    # 重置骰子停止状态
    for i in range(dice_stopped.size()):
        dice_stopped[i] = false
    current_stop_index = 0
    
    # 掷骰子
    dice_values = game_manager.roll_dice()
    
    # 更新UI - GameManager会通过信号更新骰子图标
    # limit_score_label.text = "限制积分: --"
    dice_sum_label.text = "--"
    
    # 在掷骰子动画过程中禁用确认按钮
    confirm_button.disabled = true
    _update_button_disabled_state(confirm_button)
    
    # 播放按钮按下动画
    _play_button_press_animation(roll_button)

# 清除所有骰子的边框特效
func _clear_all_dice_outlines():
    for i in range(10):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            var outline = dice_node.get_node_or_null("DiceOutline")
            if outline:
                # 创建淡出动画
                var fade_tween = create_tween()
                fade_tween.tween_property(outline, "modulate:a", 0.0, 0.2)
                
                # 使用匿名函数连接tween完成信号，避免等待
                fade_tween.finished.connect(func():
                    if outline and is_instance_valid(outline):
                        outline.queue_free()
                )

# 确认按钮点击
func _on_confirm_button_pressed():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 计算骰子总和
        var dice_sum = 0
        for value in dice_values:
            dice_sum += value
        
        # 发送骰子总值信号
        emit_signal("dice_total_value_changed", dice_sum)
        
        # 播放按钮按下动画
        _play_button_press_animation(confirm_button)
        
        # 播放收回动画
        _play_panel_hide_animation(dice_sum)

# 隐藏骰子面板
func hide_dice_panel():
    # 停止脉冲动画
    stop_pulse_animation()

    # 隐藏面板
    visible = false

    # 清除所有骰子边框
    for i in range(10):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            var outline = dice_node.get_node_or_null("DiceOutline")
            if outline:
                outline.queue_free()
    
    # 移除所有骰子的模糊效果
    # for i in range(10):
    #     var dice_node = _get_dice_by_index(i)
    #     if dice_node:
    #         _apply_blur_effect(dice_node, false)
    
    # 隐藏骰子
    dice1.visible = false
    dice2.visible = false
    dice3.visible = false
    dice4.visible = false
    dice5.visible = false
    dice6.visible = false
    dice7.visible = false
    dice8.visible = false
    dice9.visible = false
    dice10.visible = false

# 播放面板隐藏动画
func _play_panel_hide_animation(dice_sum):
    if is_animating:
        return
        
    is_animating = true
    
    # 创建收回动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK) # 使用BACK过渡类型实现Q弹效果
    
    # 向上收回
    tween.tween_property(self, "global_position", panel_hidden_position, 0.5)
    tween.parallel().tween_property(self, "modulate:a", 0.0, 0.3)
    
    await tween.finished
    
    # 隐藏骰子
    dice1.visible = false
    dice2.visible = false
    dice3.visible = false
    dice4.visible = false
    dice5.visible = false
    dice6.visible = false
    dice7.visible = false
    dice8.visible = false
    dice9.visible = false
    dice10.visible = false
    
    # 隐藏面板
    visible = false
    is_animating = false
    
    # 确认骰子结果，进入行动阶段
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        game_manager.confirm_dice_result(dice_sum)
    
    # 发出确认信号
    emit_signal("dice_confirmed", dice_sum)

# 获取骰子节点
func _get_dice_by_index(index):
    match index:
        0: return dice1
        1: return dice2
        2: return dice3
        3: return dice4
        4: return dice5
        5: return dice6
        6: return dice7
        7: return dice8
        8: return dice9
        9: return dice10
        _: return null

# 从骰子面板到积分栏的积分转移动画
func _create_firefly_effect(limit_score):
    # 获取游戏场景和积分标签
    var game_scene = get_parent()
    var limit_label = game_scene.get_node("TopBar/ActionPointsDisplay") if game_scene else null
    
    if not limit_label:
        print("警告: 未找到积分标签，无法创建转移效果")
        return
    
    # 创建一个显示积分的标签节点
    var score_label = Label.new()
    game_scene.add_child(score_label)
    
    # 设置标签属性
    score_label.text = str(limit_score)
    score_label.add_theme_font_size_override("font_size", 40)
    score_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))  # 金黄色
    score_label.add_theme_constant_override("outline_size", 2)
    score_label.add_theme_color_override("font_outline_color", Color(0.3, 0.2, 0, 1))
    
    # 设置标签初始位置（骰子面板中心）
    score_label.position = panel_original_position + Vector2(size.x / 2, size.y / 2)
    score_label.pivot_offset = Vector2(score_label.size.x / 2, score_label.size.y / 2)
    
    # 确保标签在最上层显示
    score_label.z_index = 1000
    
    # 创建简单的跟随粒子（可选）
    var particles = GPUParticles2D.new()
    score_label.add_child(particles)
    
    # 设置简单的粒子效果
    var particle_material = ParticleProcessMaterial.new()
    particle_material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_POINT
    particle_material.direction = Vector3(0, 0, 0)  # 无方向性，随标签移动
    particle_material.spread = 180.0
    particle_material.initial_velocity_min = 10.0
    particle_material.initial_velocity_max = 20.0
    particle_material.gravity = Vector3(0, 0, 0)
    particle_material.scale_min = 1.0
    particle_material.scale_max = 2.0
    particle_material.color = Color(1.0, 0.8, 0.2, 0.6)  # 金黄色，半透明
    
    # 设置粒子属性
    particles.process_material = particle_material
    particles.amount = 10  # 少量粒子，不分散注意力
    particles.lifetime = 0.5
    particles.explosiveness = 0.1
    particles.one_shot = false
    particles.emitting = true
    
    # 创建从骰子面板到积分标签的动画
    var tween = create_tween()
    tween.set_parallel(true)  # 允许同时执行多个动画
    
    # 主路径动画 - 使用弹性曲线让动画更有活力
    tween.tween_property(score_label, "global_position", limit_label.global_position, 0.3).set_ease(Tween.EASE_OUT)
    
    # 缩放动画 - 起始放大然后到目标时恢复正常
    tween.tween_property(score_label, "scale", Vector2(1.5, 1.5), 0.1)
    tween.chain().tween_property(score_label, "scale", Vector2(1.0, 1.0), 0.3).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
    tween.parallel().tween_property(score_label, "modulate:a", 0.0, 0.3)
    
    # 旋转轻微摆动动画
    # tween.tween_property(score_label, "rotation_degrees", 10, 0.2)
    # tween.chain().tween_property(score_label, "rotation_degrees", -10, 0.4)
    # tween.chain().tween_property(score_label, "rotation_degrees", 0, 0.2)
    
    # 等待动画完成
    await tween.finished
    
    # 到达目标后的效果
    # 1. 播放积分标签充能动画
    var animation_manager = get_node("/root/AnimationManager")
    if animation_manager:
        animation_manager.play_limit_label_animation(limit_label)
    else:
        # 简单的缩放动画作为备用
        var label_tween = create_tween()
        label_tween.tween_property(limit_label, "scale", Vector2(1.2, 1.2), 0.2)
        label_tween.tween_property(limit_label, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 2. 标签融入积分栏的效果
    var fade_tween = create_tween()
    fade_tween.tween_property(score_label, "scale", Vector2(0.7, 0.7), 0.2)
    fade_tween.parallel().tween_property(score_label, "modulate:a", 0.0, 0.2)
    
    # 等待淡出动画完成
    await fade_tween.finished
    
    # 清理标签节点
    score_label.queue_free()

# 更新骰子显示
func update_dice_display():
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
        
    # 更新每个骰子的显示
    for i in range(10):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            if i < game_manager.dice_count and i < dice_values.size() and dice_values[i] > 0:
                dice_node.visible = true
                _update_dice_animation(dice_node, true)  # 显示最终的骰子值
            else:
                dice_node.visible = false
    
    # 计算并显示骰子总和
    var dice_sum = 0
    for i in range(game_manager.dice_count):
        if i < dice_values.size():
            dice_sum += dice_values[i]
    
    # 设置目标总和并开始动画
    target_sum = dice_sum
    current_displayed_sum = 0  # 从0开始动画
    is_sum_animating = true

# 更新骰子数量
func update_dice_count(new_count: int):
    # 清除之前的映射
    dice_node_indices.clear()
    
    # 隐藏所有骰子和它们的阴影
    for i in range(10):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            dice_node.visible = false
            var shadow = dice_shadows.get(dice_node)
            if shadow:
                shadow.visible = false
    
    # 设置初始纹理
    var atlas = AtlasTexture.new()
    atlas.atlas = DICE_TEXTURE
    atlas.region = Rect2(
        4 * DICE_FRAME_SIZE.x,  # 第5列
        2 * DICE_FRAME_SIZE.y,  # 第3行
        DICE_FRAME_SIZE.x,
        DICE_FRAME_SIZE.y
    )
    
    # 根据数量显示骰子并设置初始纹理
    for i in range(new_count):
        var dice_node = _get_dice_by_index(i)
        if dice_node:
            dice_node.visible = true
            dice_node.texture = atlas
            # 建立骰子节点和索引的映射关系
            dice_node_indices[dice_node] = i
            
            # 显示对应的阴影
            var shadow = dice_shadows.get(dice_node)
            if shadow:
                shadow.visible = true
                shadow.texture = atlas
    
    # 确保dice_values数组与当前骰子数量匹配
    while dice_values.size() < new_count:
        dice_values.append(1)
    
    # 更新骰子显示
    if not is_rolling:
        # 只有在非掷骰状态才更新显示
        for i in range(10):
            var dice_node = _get_dice_by_index(i)
            if dice_node and dice_node.visible:
                dice_node.texture = atlas
                var shadow = dice_shadows.get(dice_node)
                if shadow:
                    shadow.texture = atlas
    
    # 更新骰子布局
    _update_dice_layout(new_count)

# 设置骰子精灵帧
func _setup_dice_sprite(dice_node: TextureRect):
    # 设置纹理
    dice_node.texture = DICE_TEXTURE
    dice_node.custom_minimum_size = DICE_FRAME_SIZE * 0.8  # 根据需要调整大小
    dice_node.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    dice_node.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    dice_node.pivot_offset = dice_node.custom_minimum_size / 2  # 设置缩放中心点
    
    # 创建阴影节点
    var shadow = TextureRect.new()
    shadow.name = "DiceShadow"
    shadow.texture = DICE_TEXTURE
    shadow.custom_minimum_size = dice_node.custom_minimum_size
    shadow.expand_mode = dice_node.expand_mode
    shadow.stretch_mode = dice_node.stretch_mode
    shadow.modulate = Color(0, 0, 0, 0.3)  # 半透明黑色
    shadow.show_behind_parent = true  # 确保阴影显示在骰子后面
    shadow.pivot_offset = shadow.custom_minimum_size / 2  # 设置缩放中心点
    
    # 将阴影添加为骰子的兄弟节点
    dice_node.get_parent().add_child(shadow)
    dice_node.get_parent().move_child(shadow, dice_node.get_index())  # 移动到骰子前面
    
    # 存储阴影引用
    dice_shadows[dice_node] = shadow
    
    # 初始化阴影位置
    shadow.position = dice_node.position + shadow_offset

# 设置按钮为圆形并添加交互效果
func _setup_round_buttons():
    # 设置掷骰子按钮
    roll_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
    roll_button.custom_minimum_size = Vector2(70, 70)
    roll_button.pivot_offset = roll_button.custom_minimum_size / 2
    roll_button.focus_mode = Control.FOCUS_NONE
    
    # 创建掷骰子按钮的阴影
    var roll_shadow = TextureRect.new()
    roll_shadow.texture = ROLL_BUTTON_TEXTURE
    roll_shadow.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    roll_shadow.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    roll_shadow.custom_minimum_size = Vector2(70, 70)
    roll_shadow.modulate = Color(0, 0, 0, 0.3)  # 半透明黑色
    roll_shadow.show_behind_parent = true  # 显示在按钮后面
    roll_shadow.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建掷骰子按钮的纹理矩形
    var roll_texture_rect = TextureRect.new()
    roll_texture_rect.texture = ROLL_BUTTON_TEXTURE
    roll_texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    roll_texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    roll_texture_rect.custom_minimum_size = Vector2(70, 70)
    roll_texture_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建禁用状态遮罩
    var roll_disabled_overlay = TextureRect.new()  # 改用TextureRect
    roll_disabled_overlay.texture = ROLL_BUTTON_TEXTURE  # 使用相同的纹理
    roll_disabled_overlay.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    roll_disabled_overlay.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    roll_disabled_overlay.custom_minimum_size = Vector2(70, 70)
    roll_disabled_overlay.modulate = Color(0.2, 0.2, 0.2, 0.3)  # 深灰色半透明
    roll_disabled_overlay.visible = false  # 初始不可见
    roll_disabled_overlay.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 清除原有子节点并添加新节点
    for child in roll_button.get_children():
        child.queue_free()
    roll_button.add_child(roll_shadow)
    roll_button.add_child(roll_texture_rect)
    roll_button.add_child(roll_disabled_overlay)
    
    # 设置阴影初始位置
    roll_shadow.position = Vector2(4, 4)
    
    # 创建一个空的样式盒，保持按钮的可点击区域但不显示背景
    var empty_style = StyleBoxEmpty.new()
    roll_button.add_theme_stylebox_override("normal", empty_style)
    roll_button.add_theme_stylebox_override("hover", empty_style)
    roll_button.add_theme_stylebox_override("pressed", empty_style)
    roll_button.add_theme_stylebox_override("disabled", empty_style)
    roll_button.add_theme_stylebox_override("focus", empty_style)
    
    # 设置确认按钮
    confirm_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
    confirm_button.custom_minimum_size = Vector2(70, 70)
    confirm_button.pivot_offset = confirm_button.custom_minimum_size / 2
    confirm_button.focus_mode = Control.FOCUS_NONE
    
    # 创建确认按钮的阴影
    var confirm_shadow = TextureRect.new()
    confirm_shadow.texture = CONFIRM_BUTTON_TEXTURE
    confirm_shadow.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    confirm_shadow.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    confirm_shadow.custom_minimum_size = Vector2(70, 70)
    confirm_shadow.modulate = Color(0, 0, 0, 0.3)
    confirm_shadow.show_behind_parent = true
    confirm_shadow.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建确认按钮的纹理矩形
    var confirm_texture_rect = TextureRect.new()
    confirm_texture_rect.texture = CONFIRM_BUTTON_TEXTURE
    confirm_texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    confirm_texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    confirm_texture_rect.custom_minimum_size = Vector2(70, 70)
    confirm_texture_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建禁用状态遮罩
    var confirm_disabled_overlay = TextureRect.new()  # 改用TextureRect
    confirm_disabled_overlay.texture = CONFIRM_BUTTON_TEXTURE  # 使用相同的纹理
    confirm_disabled_overlay.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    confirm_disabled_overlay.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    confirm_disabled_overlay.custom_minimum_size = Vector2(70, 70)
    confirm_disabled_overlay.modulate = Color(0.2, 0.2, 0.2, 0.3)
    confirm_disabled_overlay.visible = false
    confirm_disabled_overlay.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 清除原有子节点并添加新节点
    for child in confirm_button.get_children():
        child.queue_free()
    confirm_button.add_child(confirm_shadow)
    confirm_button.add_child(confirm_texture_rect)
    confirm_button.add_child(confirm_disabled_overlay)
    
    # 设置阴影初始位置
    confirm_shadow.position = Vector2(4, 4)
    
    # 创建一个空的样式盒
    confirm_button.add_theme_stylebox_override("normal", empty_style)
    confirm_button.add_theme_stylebox_override("hover", empty_style)
    confirm_button.add_theme_stylebox_override("pressed", empty_style)
    confirm_button.add_theme_stylebox_override("disabled", empty_style)
    confirm_button.add_theme_stylebox_override("focus", empty_style)
    
    # 添加鼠标进入/退出事件
    roll_button.mouse_entered.connect(_on_button_mouse_entered.bind(roll_button))
    roll_button.mouse_exited.connect(_on_button_mouse_exited.bind(roll_button))
    confirm_button.mouse_entered.connect(_on_button_mouse_entered.bind(confirm_button))
    confirm_button.mouse_exited.connect(_on_button_mouse_exited.bind(confirm_button))

# 按钮鼠标进入事件
func _on_button_mouse_entered(button: Button):
    if button.disabled:
        return
        
    
    # 创建更有弹性的动画
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.15, 1.15), 0.4)
    
    # 获取阴影节点
    var shadow = button.get_child(0)  # 阴影是第一个子节点
    if shadow:
        # 创建阴影动画
        var shadow_tween = create_tween()
        shadow_tween.set_ease(Tween.EASE_OUT)
        shadow_tween.set_trans(Tween.TRANS_ELASTIC)
        shadow_tween.tween_property(shadow, "position", Vector2(6, 6), 0.4)  # 阴影位置变大
        shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.2, 0.4)  # 阴影变淡

# 按钮鼠标退出事件
func _on_button_mouse_exited(button: Button):
    if button.disabled:
        return
        
    # 创建更有弹性的动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.4)
    
    # 获取阴影节点
    var shadow = button.get_child(0)  # 阴影是第一个子节点
    if shadow:
        # 创建阴影动画
        var shadow_tween = create_tween()
        shadow_tween.set_ease(Tween.EASE_OUT)
        shadow_tween.set_trans(Tween.TRANS_ELASTIC)
        shadow_tween.tween_property(shadow, "position", Vector2(4, 4), 0.4)  # 阴影恢复原位
        shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.3, 0.4)  # 阴影恢复原透明度

# 按钮按下动画
func _play_button_press_animation(button: Button):
    # 创建更有弹性的按下动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(0.85, 0.85), 0.15)
    tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.3)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 获取阴影节点
    var shadow = button.get_child(0)  # 阴影是第一个子节点
    if shadow:
        # 创建阴影动画
        var shadow_tween = create_tween()
        shadow_tween.set_ease(Tween.EASE_OUT)
        shadow_tween.set_trans(Tween.TRANS_ELASTIC)
        # 按下时阴影变小且加深
        shadow_tween.tween_property(shadow, "position", Vector2(2, 2), 0.15)
        shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.4, 0.15)
        # 反弹时阴影变大且变淡
        shadow_tween.tween_property(shadow, "position", Vector2(5, 5), 0.3)
        shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.2, 0.3)
        # 恢复正常
        shadow_tween.tween_property(shadow, "position", Vector2(4, 4), 0.2)
        shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.3, 0.2)

# 更新按钮禁用状态
func _update_button_disabled_state(button: Button):
    # 获取禁用状态遮罩（第三个子节点）
    var disabled_overlay = button.get_child(2)
    if disabled_overlay:
        disabled_overlay.visible = button.disabled
        
        if button.disabled:
            # 创建禁用效果动画
            var tween = create_tween()
            tween.set_ease(Tween.EASE_OUT)
            tween.set_trans(Tween.TRANS_CUBIC)
            tween.tween_property(button, "modulate", Color(0.7, 0.7, 0.7, 1.0), 0.2)
            
            # 阴影变淡
            var shadow = button.get_child(0)
            if shadow:
                var shadow_tween = create_tween()
                shadow_tween.tween_property(shadow, "modulate:a", 0.15, 0.2)
        else:
            # 创建启用效果动画
            var tween = create_tween()
            tween.set_ease(Tween.EASE_OUT)
            tween.set_trans(Tween.TRANS_CUBIC)
            tween.tween_property(button, "modulate", Color(1, 1, 1, 1), 0.2)
            
            # 阴影恢复
            var shadow = button.get_child(0)
            if shadow:
                var shadow_tween = create_tween()
                shadow_tween.tween_property(shadow, "modulate:a", 0.3, 0.2)

# 设置骰子交互
func _setup_dice_interaction():
    # 为每个骰子添加鼠标事件
    _setup_dice_mouse_events(dice1, 0)
    _setup_dice_mouse_events(dice2, 1)
    _setup_dice_mouse_events(dice3, 2)
    _setup_dice_mouse_events(dice4, 3)
    _setup_dice_mouse_events(dice5, 4)
    _setup_dice_mouse_events(dice6, 5)
    _setup_dice_mouse_events(dice7, 6)
    _setup_dice_mouse_events(dice8, 7)
    _setup_dice_mouse_events(dice9, 8)
    _setup_dice_mouse_events(dice10, 9)

# 设置骰子鼠标事件
func _setup_dice_mouse_events(dice_node: TextureRect, index: int):
    # 确保骰子节点可以接收鼠标事件
    dice_node.mouse_filter = Control.MOUSE_FILTER_STOP
    dice_node.pivot_offset = dice_node.size / 2
    
    # 添加鼠标进入事件
    dice_node.mouse_entered.connect(func():
        if not is_rolling and dice_node.visible:
            AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
            # 创建Q弹动画
            var tween = create_tween()
            tween.set_ease(Tween.EASE_OUT)
            tween.set_trans(Tween.TRANS_ELASTIC)
            # 使用基础缩放值计算目标缩放
            tween.tween_property(dice_node, "scale", base_scale * 1.2, 0.3)
            
            # 同步更新阴影
            var shadow = dice_shadows.get(dice_node)
            if shadow:
                var shadow_tween = create_tween()
                shadow_tween.set_ease(Tween.EASE_OUT)
                shadow_tween.set_trans(Tween.TRANS_ELASTIC)
                # 阴影跟随放大，但水平方向拉伸更多
                shadow_tween.tween_property(shadow, "scale", base_scale * Vector2(1.3, 1.1), 0.3)
                # 调整阴影位置
                shadow_tween.parallel().tween_property(shadow, "position", 
                    dice_node.position + shadow_offset * base_scale.y * 1.2, 0.3)
                # 降低阴影透明度
                shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.2, 0.3)
    )
    
    # 添加鼠标退出事件
    dice_node.mouse_exited.connect(func():
        if not is_rolling and dice_node.visible:
            # 创建Q弹动画
            var tween = create_tween()
            tween.set_ease(Tween.EASE_OUT)
            tween.set_trans(Tween.TRANS_ELASTIC)
            # 恢复到基础缩放值
            tween.tween_property(dice_node, "scale", base_scale, 0.3)
            
            # 同步更新阴影
            var shadow = dice_shadows.get(dice_node)
            if shadow:
                var shadow_tween = create_tween()
                shadow_tween.set_ease(Tween.EASE_OUT)
                shadow_tween.set_trans(Tween.TRANS_ELASTIC)
                # 阴影恢复原始大小
                shadow_tween.tween_property(shadow, "scale", base_scale, 0.3)
                # 调整阴影位置
                shadow_tween.parallel().tween_property(shadow, "position", 
                    dice_node.position + shadow_offset * base_scale.y, 0.3)
                # 恢复阴影透明度
                shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.3, 0.3)
    )
    
    # 添加鼠标点击事件
    dice_node.gui_input.connect(func(event):
        if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
            if not is_rolling and dice_node.visible:
                AudioManager.play_sfx("res://assert/audio/sfx/button_click001.mp3")
                # 创建Q弹动画
                var tween = create_tween()
                tween.set_ease(Tween.EASE_OUT)
                tween.set_trans(Tween.TRANS_ELASTIC)
                # 使用基础缩放值计算目标缩放
                tween.tween_property(dice_node, "scale", base_scale * 0.8, 0.1)
                tween.tween_property(dice_node, "scale", base_scale, 0.2)
                
                # 同步更新阴影
                var shadow = dice_shadows.get(dice_node)
                if shadow:
                    var shadow_tween = create_tween()
                    shadow_tween.set_ease(Tween.EASE_OUT)
                    shadow_tween.set_trans(Tween.TRANS_ELASTIC)
                    # 阴影跟随缩小，但水平方向变化更大
                    shadow_tween.tween_property(shadow, "scale", base_scale * Vector2(0.7, 0.9), 0.1)
                    shadow_tween.tween_property(shadow, "scale", base_scale, 0.2)
                    # 调整阴影位置
                    shadow_tween.parallel().tween_property(shadow, "position", 
                        dice_node.position + shadow_offset * base_scale.y * 0.8, 0.1)
                    shadow_tween.tween_property(shadow, "position", 
                        dice_node.position + shadow_offset * base_scale.y, 0.2)
                    # 增加阴影透明度
                    shadow_tween.parallel().tween_property(shadow, "modulate:a", 0.4, 0.1)
                    shadow_tween.tween_property(shadow, "modulate:a", 0.3, 0.2)
                
                # 随机选择第四行的一个图案
                var atlas = AtlasTexture.new()
                atlas.atlas = DICE_TEXTURE
                var random_column = randi() % DICE_FRAMES_PER_ROW  # 随机选择0-5之间的列
                atlas.region = Rect2(
                    random_column * DICE_FRAME_SIZE.x,  # 随机列
                    3 * DICE_FRAME_SIZE.y,  # 第四行（索引为3）
                    DICE_FRAME_SIZE.x,
                    DICE_FRAME_SIZE.y
                )
                
                # 更新骰子和阴影的纹理
                dice_node.texture = atlas
                if shadow:
                    shadow.texture = atlas
    )

# 创建骰子停止粒子特效
func _create_dice_stop_effect(index: int):
    var dice_node = _get_dice_by_index(index)
    if not dice_node:
        return
    
    # 检查是否已经有边框，如果有就移除
    var existing_outline = dice_node.get_node_or_null("DiceOutline")
    if existing_outline:
        existing_outline.queue_free()
    
    # 创建外发光容器节点
    var effect_container = Control.new()
    effect_container.name = "DiceOutline"
    dice_node.add_child(effect_container)
    effect_container.show_behind_parent = true
    effect_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 设置容器大小和位置
    var outline_padding = 0.1  # 基础描边宽度
    effect_container.size = dice_node.size + Vector2(outline_padding * 2, outline_padding * 2)
    effect_container.position = Vector2(-outline_padding, -outline_padding)
    
    # 设置描边颜色 - 使用与粒子相同的颜色逻辑
    var outline_color = Color(1.0, 0.8, 0.2, 0.8)  # 默认为金黄色
    
    # 根据骰子点数设置不同颜色
    if index < dice_values.size():
        var value = dice_values[index]
        if value >= 5:  # 高点数使用金色
            outline_color = Color(1.0, 0.8, 0.2, 0.8)
        elif value >= 3:  # 中点数使用蓝色
            outline_color = Color(0.2, 0.6, 1.0, 0.8)
        else:  # 低点数使用绿色
            outline_color = Color(0.2, 0.8, 0.4, 0.8)
    
    # 创建多层光芒效果
    var layers = 10
    var max_glow_size = 6.0  # 最大光芒大小
    var base_corner_radius = 10.0  # 基础圆角半径
    
    for i in range(layers):
        # 创建面板节点来显示圆角效果
        var panel = Panel.new()
        effect_container.add_child(panel)
        
        # 计算每层的尺寸（外层越大）
        var layer_padding = outline_padding + (max_glow_size * (i + 1) / layers)
        panel.size = dice_node.size + Vector2(layer_padding * 2, layer_padding * 2)
        panel.position = Vector2(-layer_padding, -layer_padding)
        
        # 计算每层的颜色（外层越透明）
        var alpha = 0.2 * (1.0 - float(i) / layers)
        
        # 创建带圆角的样式盒
        var style_box = StyleBoxFlat.new()
        style_box.bg_color = Color(outline_color.r, outline_color.g, outline_color.b, alpha)
        
        # 设置圆角 - 随着外层增加，圆角也相应增加
        var corner_radius = base_corner_radius + (i * 1.0)
        style_box.corner_radius_top_left = corner_radius
        style_box.corner_radius_top_right = corner_radius
        style_box.corner_radius_bottom_left = corner_radius
        style_box.corner_radius_bottom_right = corner_radius
        
        # 应用样式盒
        panel.add_theme_stylebox_override("panel", style_box)
        
        # 设置锚点用于缩放动画
        panel.pivot_offset = panel.size / 2
    
    # 创建内层边框（最清晰的边框）
    var inner_panel = Panel.new()
    effect_container.add_child(inner_panel)
    inner_panel.size = dice_node.size + Vector2(outline_padding * 2, outline_padding * 2)
    inner_panel.position = Vector2(-outline_padding, -outline_padding)
    
    # 创建内层边框样式
    var inner_style = StyleBoxFlat.new()
    inner_style.bg_color = outline_color
    inner_style.corner_radius_top_left = base_corner_radius
    inner_style.corner_radius_top_right = base_corner_radius
    inner_style.corner_radius_bottom_left = base_corner_radius
    inner_style.corner_radius_bottom_right = base_corner_radius
    
    # 应用内层样式
    inner_panel.add_theme_stylebox_override("panel", inner_style)
    inner_panel.pivot_offset = inner_panel.size / 2
    
    # 设置容器的缩放锚点
    effect_container.pivot_offset = effect_container.size / 2
    
    # 创建初始动画效果
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 从小到大的整体动画
    effect_container.scale = Vector2(0.5, 0.5)
    tween.tween_property(effect_container, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 创建持续的光芒呼吸效果
    _create_glow_pulse_animation(effect_container)

# 创建光芒呼吸动画
func _create_glow_pulse_animation(effect_container):
    # 只创建整体呼吸效果
    if pulse_tween:
        pulse_tween.kill()
    pulse_tween = create_tween()
    pulse_tween.set_loops() # 无限循环
    
    # 缓慢呼吸效果 - 轻微放大缩小
    pulse_tween.tween_property(effect_container, "scale", Vector2(1.05, 1.05), 0.8)
    pulse_tween.tween_property(effect_container, "scale", Vector2(0.95, 0.95), 0.8)
    
    # 收集所有面板信息并开始处理闪烁效果
    _setup_glow_panels(effect_container)
    is_glow_effect_active = true

# 设置光芒面板
func _setup_glow_panels(effect_container):
    # 清除之前的数据
    glow_panels.clear()
    glow_original_colors.clear()
    glow_pulse_timers.clear()
    glow_pulse_directions.clear()
    
    # 收集所有面板
    for i in range(effect_container.get_child_count()):
        var child = effect_container.get_child(i)
        if child is Panel:
            var style = child.get_theme_stylebox("panel", "Panel")
            if style is StyleBoxFlat:
                glow_panels.append(child)
                glow_original_colors.append(style.bg_color)
                glow_pulse_timers.append(randf() * 0.7) # 随机初始时间
                glow_pulse_directions.append(1) # 初始方向

# 更新光芒面板闪烁效果
func _update_glow_panels(delta):
    # 循环处理每个面板
    var i = 0
    while i < glow_panels.size():
        if not is_instance_valid(glow_panels[i]):
            # 移除无效的面板
            glow_panels.remove_at(i)
            glow_original_colors.remove_at(i)
            glow_pulse_timers.remove_at(i)
            glow_pulse_directions.remove_at(i)
            continue
            
        # 更新计时器
        glow_pulse_timers[i] += delta
        
        # 切换方向
        if glow_pulse_timers[i] >= 0.7:
            glow_pulse_timers[i] = 0.0
            glow_pulse_directions[i] *= -1
            
        # 更新面板样式
        _update_panel_style(i)
        
        i += 1
    
    # 如果没有面板了，停止效果
    if glow_panels.size() == 0:
        is_glow_effect_active = false

# 更新单个面板的样式
func _update_panel_style(index):
    var panel = glow_panels[index]
    var original_color = glow_original_colors[index]
    var style = panel.get_theme_stylebox("panel", "Panel")
    
    if style is StyleBoxFlat:
        var progress = glow_pulse_timers[index] / 0.7
        var alpha_factor = 1.0
        
        # 根据方向计算透明度
        if glow_pulse_directions[index] > 0:
            # 从亮到暗
            alpha_factor = 1.0 - (progress * 0.5)
        else:
            # 从暗到亮
            alpha_factor = 0.5 + (progress * 0.5)
            
        # 创建新样式
        var new_style = StyleBoxFlat.new()
        new_style.bg_color = Color(original_color.r, original_color.g, original_color.b, original_color.a * alpha_factor)
        
        # 复制圆角
        if style.corner_radius_top_left > 0:
            new_style.corner_radius_top_left = style.corner_radius_top_left
            new_style.corner_radius_top_right = style.corner_radius_top_right
            new_style.corner_radius_bottom_left = style.corner_radius_bottom_left
            new_style.corner_radius_bottom_right = style.corner_radius_bottom_right
        
        # 应用新样式
        panel.add_theme_stylebox_override("panel", new_style)

# 停止所有光芒效果
func _stop_glow_effects():
    is_glow_effect_active = false
    glow_panels.clear()
    glow_original_colors.clear()
    glow_pulse_timers.clear()
    glow_pulse_directions.clear()

# 播放积分完成效果
func _play_score_complete_effect():
    # 播放积分标签的完成动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 震动效果
    tween.tween_property(limit_score_label, "scale", Vector2(1.2, 1.2), 0.2)
    tween.tween_property(limit_score_label, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 颜色闪烁
    var original_color = limit_score_label.modulate
    
    # 根据积分是增加还是减少，变化颜色
    var flash_color = Color(1.0, 0.3, 0.3, 1.0)  # 红色（减少）
    if current_displayed_score > 0:  # 仍有积分
        flash_color = Color(1.0, 1.0, 0.3, 1.0)  # 黄色
    
    # 颜色闪烁动画
    tween.parallel().tween_property(limit_score_label, "modulate", flash_color, 0.2)
    tween.parallel().tween_property(limit_score_label, "modulate", original_color, 0.3)

# 创建从骰子到积分的扣减动画
func _create_score_deduction_effect(dice_node: TextureRect, dice_value: int):
    # 计算当前骰子的全局位置
    var dice_global_pos = dice_node.global_position + dice_node.size / 2
    var limit_score_label_pos = limit_score_label.global_position
    
    # 创建一个显示扣减的标签节点
    var score_label = Label.new()
    add_child(score_label)
    
    # 设置标签属性
    score_label.text = "+" + str(dice_value)
    score_label.add_theme_font_size_override("font_size", 30)
    score_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2, 1.0))  # 金色
    score_label.add_theme_constant_override("outline_size", 2)
    score_label.add_theme_color_override("font_outline_color", Color(0.3, 0.0, 0.0, 1.0))
    
    # 设置标签初始位置（骰子位置）
    score_label.global_position = Vector2(dice_global_pos.x -20, dice_global_pos.y)
    score_label.pivot_offset = Vector2(score_label.size.x / 2, score_label.size.y / 2)
    
    # 确保标签在最上层显示
    score_label.z_index = 1000
    
    # 即时扣减积分值
    current_displayed_score -= dice_value
    limit_score_label.text = str(int(current_displayed_score))
    
    # 创建标签动画
    var tween = create_tween()
    tween.set_parallel(true)  # 允许同时执行多个动画
    
    # 初始缩放
    score_label.scale = Vector2(0.5, 0.5)
    
    # 向上浮动动画
    tween.tween_property(score_label, "position:y", score_label.position.y - 100, 0.5).set_ease(Tween.EASE_OUT)
    
    # 缩放动画
    tween.tween_property(score_label, "scale", Vector2(1.2, 1.2), 0.2)
    tween.chain().tween_property(score_label, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 渐变消失
    tween.tween_property(score_label, "modulate:a", 0.0, 0.5).set_delay(0.3)
    
    # 播放LimitScoreLabel的震动动画
    var score_tween = create_tween()
    score_tween.set_ease(Tween.EASE_OUT)
    score_tween.set_trans(Tween.TRANS_ELASTIC)
    score_tween.tween_property(limit_score_label, "scale", Vector2(1.2, 1.2), 0.1)
    score_tween.tween_property(limit_score_label, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 颜色闪烁
    limit_score_label.modulate = Color(1.0, 0.3, 0.3, 1.0)  # 红色
    var color_tween = create_tween()
    color_tween.tween_property(limit_score_label, "modulate", Color(1, 1, 1, 1), 0.3)
    
    # 等待动画完成后删除标签
    await tween.finished
    score_label.queue_free()

# 播放骰子总和完成效果
func _play_sum_complete_effect():
    # 播放总和标签的完成动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 震动效果
    tween.tween_property(dice_sum_label, "scale", Vector2(1.2, 1.2), 0.2)
    tween.tween_property(dice_sum_label, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 颜色闪烁
    var original_color = dice_sum_label.modulate
    var flash_color = Color(1.0, 0.8, 0.2, 1.0)  # 金色
    
    # 颜色闪烁动画
    tween.parallel().tween_property(dice_sum_label, "modulate", flash_color, 0.2)
    tween.parallel().tween_property(dice_sum_label, "modulate", original_color, 0.3)

# 响应骰子数量变化
func _on_dice_count_changed(new_count: int):
    var game_manager = get_node("/root/GameManager")
    if game_manager and visible:
        # 更新骰子图标
        update_dice_icons(game_manager.dice_rolls_left, game_manager.dice_rolls_chance)
        # 更新骰子显示
        update_dice_count(new_count)

# 响应骰子次数变化
func _on_dice_rolls_changed(rolls_left: int, max_rolls: int):
    if visible:
        # 更新骰子图标
        update_dice_icons(rolls_left, max_rolls)
        
        # 重置跳动动画状态
        icon_bounce_timer = 0.0
        is_icon_bouncing = false
        is_round_pausing = false
        current_bounce_index = -1
        next_bounce_index = 0  # 重置为从第一个可用骰子开始跳动

# 更新骰子帧动画
func _update_dice_animation(dice_node: TextureRect, is_stopped: bool):
    if not dice_node:
        return
    
    # 获取阴影节点
    var shadow = dice_shadows.get(dice_node)
    
    # 存储原始位置（如果还没有存储）
    if not dice_original_positions.has(dice_node):
        dice_original_positions[dice_node] = dice_node.position
        dice_animation_progress[dice_node] = 0.0
    
    # 更新动画进度
    if not is_stopped:
        dice_animation_progress[dice_node] = min(dice_animation_progress[dice_node] + animation_speed, ANIMATION_DURATION)
        
        # 计算当前高度（使用抛物线运动）
        var progress = dice_animation_progress[dice_node] / ANIMATION_DURATION
        var height = JUMP_HEIGHT * 4 * progress * (progress - 1)  # 抛物线方程：-4h * t * (t-1)
        
        # 更新骰子位置
        dice_node.position.y = dice_original_positions[dice_node].y + height
        
        # 更新阴影位置和效果
        if shadow:
            # 阴影始终保持在原始高度，但水平位置跟随骰子
            shadow.position.x = dice_node.position.x
            shadow.position.y = dice_original_positions[dice_node].y + shadow_offset.y * dice_node.scale.y
            
            # 根据骰子高度调整阴影的透明度和大小
            var height_factor = abs(height) / JUMP_HEIGHT
            var shadow_scale = 1.0 - height_factor * 0.3  # 高度越高，阴影越小
            var shadow_stretch = 1.0 + height_factor * 0.5  # 高度越高，阴影越扁
            var shadow_alpha = 0.3 - height_factor * 0.2  # 高度越高，阴影越淡
            
            # 应用阴影变形效果
            shadow.scale = Vector2(shadow_scale * shadow_stretch, shadow_scale) * dice_node.scale
            shadow.modulate = Color(0, 0, 0, max(0, shadow_alpha))
    else:
        # 确保骰子回到原始位置
        dice_node.position = dice_original_positions[dice_node]
        
        # 重置阴影位置和属性
        if shadow:
            shadow.position.x = dice_node.position.x
            shadow.position.y = dice_node.position.y + shadow_offset.y * dice_node.scale.y
            shadow.scale = dice_node.scale
            shadow.modulate = Color(0, 0, 0, 0.3)
        
        # 清除动画数据
        dice_animation_progress.erase(dice_node)
        dice_original_positions.erase(dice_node)
    
    # 对于TextureRect，我们需要通过AtlasTexture来显示特定帧
    var atlas = AtlasTexture.new()
    atlas.atlas = DICE_TEXTURE
    
    if not is_stopped:
        # 在第一行的帧之间循环 (0,5) 到 (5,5)
        var frame_row = 5  # 第六行（索引为5）
        var frame_column = current_animation_frame % DICE_FRAMES_PER_ROW
        atlas.region = Rect2(
            frame_column * DICE_FRAME_SIZE.x,
            frame_row * DICE_FRAME_SIZE.y,
            DICE_FRAME_SIZE.x,
            DICE_FRAME_SIZE.y
        )
    else:
        # 使用映射获取正确的骰子索引
        var dice_index = dice_node_indices.get(dice_node, -1)
        var dice_value = 1  # 默认值
        
        # 检查索引是否在有效范围内
        if dice_index >= 0 and dice_index < dice_values.size():
            dice_value = dice_values[dice_index]
        
        # 停止时显示第一行对应点数的帧
        atlas.region = Rect2(
            (dice_value - 1) * DICE_FRAME_SIZE.x,
            0,  # 第一行
            DICE_FRAME_SIZE.x,
            DICE_FRAME_SIZE.y
        )
    
    dice_node.texture = atlas
    
    # 同步更新阴影的纹理
    if shadow:
        shadow.texture = atlas

# 开始骰子动画
func _start_dice_animation():
    # 重置所有骰子的动画进度
    dice_animation_progress.clear()
    dice_original_positions.clear()
    
    # 重置动画变量
    is_rolling = true
    animation_timer = 0.0
    animation_frames = 0
    current_animation_frame = 0
    
    # 重置骰子停止状态
    for i in range(dice_stopped.size()):
        dice_stopped[i] = false
    current_stop_index = 0

# 计算最佳骰子大小和间距
func _calculate_dice_metrics(dice_count: int, container_size: Vector2) -> Dictionary:
    var rows = 1
    var dice_in_last_row = dice_count
    
    # 确定是否需要两行
    if dice_count > max_dice_per_row:
        rows = 2
        dice_in_last_row = dice_count - max_dice_per_row
    
    # 计算第一行的骰子数
    var dice_in_first_row = dice_count if rows == 1 else max_dice_per_row
    
    # 计算基础大小
    var base_size = DICE_FRAME_SIZE * 0.8
    
    # 计算最大可用宽度和高度（考虑边距）
    var available_width = container_size.x - horizontal_margin * 2
    var available_height = container_size.y - vertical_margin * 2
    
    # 根据行数计算所需的垂直空间
    var required_vertical_space = rows * base_size.y + (rows - 1) * base_dice_spacing
    
    # 计算水平和垂直方向的缩放因子
    var horizontal_scale = 1.0
    var vertical_scale = 1.0
    
    # 计算水平缩放因子
    var first_row_total = dice_in_first_row * base_size.x + (dice_in_first_row - 1) * base_dice_spacing
    var last_row_total = dice_in_last_row * base_size.x + (dice_in_last_row - 1) * base_dice_spacing
    var max_row_width = max(first_row_total, last_row_total)
    
    if max_row_width > available_width:
        horizontal_scale = available_width / max_row_width
    
    # 计算垂直缩放因子
    if required_vertical_space > available_height:
        vertical_scale = available_height / required_vertical_space
    
    # 使用较小的缩放因子，确保两个方向都适配
    var scale = min(horizontal_scale, vertical_scale)
    
    # 根据骰子数量调整缩放范围，使用更温和的缩放
    var count_factor = 1.0 - (float(dice_count) / 10.0) * 0.25  # 从0.4改为0.25，减少缩放程度
    var target_scale = lerp(min_scale * 1.2, max_scale, count_factor)  # 提高最小缩放基准
    
    # 确保不超过目标缩放，但也不会过小
    scale = clamp(scale, min_scale * 1.2, target_scale)  # 使用clamp确保最小值
    
    # 计算最终的骰子大小和间距
    var final_size = base_size * scale
    var final_spacing = base_dice_spacing * scale
    
    return {
        "size": final_size,
        "spacing": final_spacing,
        "scale": scale
    }

# 更新骰子布局
func _update_dice_layout(dice_count: int):
    # 获取容器的大小
    var container_size = $CenterContainer/DiceContainer.size
    
    # 计算最佳骰子大小和间距
    var metrics = _calculate_dice_metrics(dice_count, container_size)
    var dice_size = metrics.size
    var dice_spacing = metrics.spacing
    
    # 计算布局
    var rows = 1
    var dice_in_last_row = dice_count
    
    # 如果骰子数量超过每行最大数量，需要分成两行
    if dice_count > max_dice_per_row:
        rows = 2
        dice_in_last_row = dice_count - max_dice_per_row
    
    # 计算第一行的骰子数
    var dice_in_first_row = dice_count if rows == 1 else max_dice_per_row
    
    # 计算每行的总宽度（包括间距）
    var first_row_width = dice_in_first_row * dice_size.x + (dice_in_first_row - 1) * dice_spacing
    var last_row_width = dice_in_last_row * dice_size.x + (dice_in_last_row - 1) * dice_spacing
    
    # 计算整体高度
    var total_height = rows * dice_size.y + (rows - 1) * dice_spacing
    
    # 计算起始位置，使骰子在容器中居中
    # 当有两行时，整体向上偏移
    var vertical_offset = 0.0
    if rows == 2:
        vertical_offset = dice_size.y * 0.3  # 向上偏移30%的骰子高度
    var start_y = (container_size.y - total_height) / 2 - vertical_offset
    
    # 存储基础缩放
    base_scale = Vector2(metrics.scale, metrics.scale)
    
    # 布局第一行骰子
    var current_dice = 0
    for i in range(dice_in_first_row):
        var dice_node = _get_dice_by_index(current_dice)
        if dice_node:
            # 计算每行的起始X位置，确保居中
            var start_x = (container_size.x - first_row_width) / 2
            var x = start_x + i * (dice_size.x + dice_spacing)
            
            # 更新骰子位置和大小
            dice_node.position = Vector2(x, start_y)
            dice_node.scale = base_scale
            
            # 更新阴影位置和大小
            var shadow = dice_shadows.get(dice_node)
            if shadow:
                shadow.position = Vector2(x, start_y + shadow_offset.y * base_scale.y)
                shadow.scale = base_scale
        current_dice += 1
    
    # 如果有第二行，布局第二行骰子
    if rows == 2:
        # 计算第二行的起始Y位置
        var second_row_y = start_y + dice_size.y + dice_spacing
        
        for i in range(dice_in_last_row):
            var dice_node = _get_dice_by_index(current_dice)
            if dice_node:
                # 计算第二行的起始X位置，确保居中
                var start_x = (container_size.x - last_row_width) / 2
                var x = start_x + i * (dice_size.x + dice_spacing)
                
                # 更新骰子位置和大小
                dice_node.position = Vector2(x, second_row_y)
                dice_node.scale = base_scale
                
                # 更新阴影位置和大小
                var shadow = dice_shadows.get(dice_node)
                if shadow:
                    shadow.position = Vector2(x, second_row_y + shadow_offset.y * base_scale.y)
                    shadow.scale = base_scale
            current_dice += 1

func _exit_tree():
    if pulse_tween:
        pulse_tween.kill()

# 停止脉冲动画
func stop_pulse_animation():
    if pulse_tween and is_instance_valid(pulse_tween):
        pulse_tween.kill()
        pulse_tween = null

    # 清理所有骰子的边框特效（包含脉冲动画）
    _clear_all_dice_outlines()

    # 重置光芒效果状态
    is_glow_effect_active = false

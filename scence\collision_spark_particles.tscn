[gd_scene load_steps=7 format=3 uid="uid://b2sj4qb5ot54p"]

[ext_resource type="Script" uid="uid://dnek8w2ppkoh2" path="res://script/collision_spark_particles.gd" id="1_collision"]

[sub_resource type="Gradient" id="Gradient_spark"]
offsets = PackedFloat32Array(0, 0.3, 1)
colors = PackedColorArray(1, 0.9, 0.2, 1, 1, 0.5, 0.1, 1, 0.8, 0.1, 0, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_spark"]
gradient = SubResource("Gradient_spark")

[sub_resource type="Curve" id="Curve_spark"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_spark"]
curve = SubResource("Curve_spark")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_spark"]
particle_flag_disable_z = true
angle_max = 360.0
direction = Vector3(0, 0, 0)
spread = 90.0
initial_velocity_min = 20.0
initial_velocity_max = 100.0
angular_velocity_min = -720.0
angular_velocity_max = 720.0
gravity = Vector3(0, 98, 0)
damping_min = 40.0
damping_max = 60.0
scale_min = 2.0
scale_max = 5.0
scale_curve = SubResource("CurveTexture_spark")
color_ramp = SubResource("GradientTexture1D_spark")

[node name="CollisionSparkParticles" type="GPUParticles2D"]
emitting = false
amount = 10
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_spark")
script = ExtResource("1_collision")

[node name="Timer" type="Timer" parent="."]
wait_time = 5.0
autostart = true

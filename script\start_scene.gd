extends Control

# 是否已经切换场景（防止重复切换）
var scene_switched = false

func _ready():
    print("开屏动画场景启动")
    # 加载并应用语言设置
    _load_and_apply_language_settings()
    # 加载并应用屏幕设置
    _load_and_apply_display_settings()

# 处理输入事件（允许跳过动画）
func _input(event):
    if event is InputEventKey and event.pressed:
        # 按任意键跳过动画
        if not scene_switched:
            print("用户跳过开屏动画")
            _switch_to_main_menu()
    elif event is InputEventMouseButton and event.pressed:
        # 点击鼠标跳过动画
        if not scene_switched:
            print("用户点击跳过开屏动画")
            _switch_to_main_menu()

# 动画结束时的回调函数
func _on_animation_player_animation_finished(anim_name: StringName) -> void:
    print("开屏动画 '", anim_name, "' 结束，切换到主菜单场景")
    _switch_to_main_menu()

# 切换到主菜单场景
func _switch_to_main_menu():
    if scene_switched:
        return  # 防止重复切换

    scene_switched = true
    print("正在切换到主菜单场景...")

    # 切换到主菜单场景
    get_tree().change_scene_to_file("res://scence/main_menu.tscn")

# 加载并应用显示设置
func _load_and_apply_display_settings():
    print("正在加载显示设置...")

    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")

    if err != OK:
        print("start_scene未找到设置文件，使用默认显示设置")

    # 应用全屏设置
    var is_fullscreen = config.get_value("display", "fullscreen", true)
    if is_fullscreen:
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
        print("应用全屏模式")
    else:
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
        print("应用窗口模式")


    if not is_fullscreen:
        var width = config.get_value("display", "width", 1280)
        var height = config.get_value("display", "height", 720)
        DisplayServer.window_set_size(Vector2i(width, height))
            
        # 居中窗口
        var screen_size = DisplayServer.screen_get_size()
        var window_size = DisplayServer.window_get_size()
        var centered_pos = (screen_size - window_size) / 2
        DisplayServer.window_set_position(centered_pos)
        print("窗口居中显示")

    print("显示设置加载完成")

# 加载并应用语言设置
func _load_and_apply_language_settings():
    print("正在加载语言设置...")

    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")

    if err == OK:
        var saved_locale = config.get_value("language", "locale", "en")
        TranslationServer.set_locale(saved_locale)
        print("应用语言设置: ", saved_locale)
    else:
        # 如果没有设置文件，使用默认语言
        TranslationServer.set_locale("en")
        print("使用默认语言: en")

    print("语言设置加载完成")

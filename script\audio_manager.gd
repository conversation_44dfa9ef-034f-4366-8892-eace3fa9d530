extends Node

# 音频管理器 - 负责管理游戏中的所有音频

# 音频播放器节点
var music_player: AudioStreamPlayer
var sfx_players: Array[AudioStreamPlayer] = []  # 音效播放器池
var ui_player: AudioStreamPlayer
const MAX_SFX_PLAYERS = 50  # 最大同时播放的音效数量

# 音频文件路径
const MUSIC_MAIN_MENU = "res://assert/audio/music/bgm010.mp3"
const MUSIC_GAMEPLAY = "res://assert/audio/music/bgm009.mp3"
const MUSIC_VICTORY = "res://assert/audio/music/victory.mp3"
const MUSIC_DEFEAT = "res://assert/audio/music/defeat.mp3"

const SFX_CARD_MOVE = "res://assert/audio/sfx/card_move.mp3"
const SFX_CARD_FLIP = "res://assert/audio/sfx/card_flip.mp3"
const SFX_DICE_ROLL = "res://assert/audio/sfx/dice_roll.mp3"
const SFX_BUFF_ACTIVATE = "res://assert/audio/sfx/buff_activate.mp3"
const SFX_ROUND_END = "res://assert/audio/sfx/round_end.mp3"
const SFX_BUTTON_CLICK = "res://assert/audio/sfx/pop-click.wav"

const SETTING_PATH = "user://settings.cfg"

# 音量设置
var music_volume: float = 0.8
var sfx_volume: float = 1.0
var master_volume: float = 1.0
var is_muted: bool = false

# 信号
signal volume_changed()

# 初始化
func _ready():
    # 创建音频播放器节点
    music_player = AudioStreamPlayer.new()
    ui_player = AudioStreamPlayer.new()
    
    # 初始化音效播放器池
    for i in range(MAX_SFX_PLAYERS):
        var sfx_player = AudioStreamPlayer.new()
        sfx_player.bus = "SFX"
        add_child(sfx_player)
        sfx_players.append(sfx_player)
    
    # 添加到场景树
    add_child(music_player)
    add_child(ui_player)
    
    # 明确设置播放器使用的总线
    music_player.bus = "Music"
    ui_player.bus = "SFX"
    
    # 设置初始音量
    update_volumes()
    
    # 连接信号
    music_player.finished.connect(_on_music_finished)

# 游戏退出时保存音频设置
func _exit_tree():
    # 创建配置文件实例
    var config = ConfigFile.new()
    
    # 尝试加载现有的配置文件
    var err = config.load(SETTING_PATH)
    
    # 如果没有配置文件，从头创建一个
    if err != OK:
        print("无法加载配置文件，创建新的配置文件")
    
    # 音乐和音效总线索引
    var music_bus_idx = AudioServer.get_bus_index("Music")
    var sfx_bus_idx = AudioServer.get_bus_index("SFX")
    
    # 保存当前的音频设置
    if music_bus_idx >= 0:
        var music_volume_db = AudioServer.get_bus_volume_db(music_bus_idx)
        config.set_value("audio", "music_volume", music_volume_db)
    
    if sfx_bus_idx >= 0:
        var sfx_volume_db = AudioServer.get_bus_volume_db(sfx_bus_idx)
        config.set_value("audio", "sfx_volume", sfx_volume_db)
    
    # 保存配置文件
    var save_err = config.save(SETTING_PATH)
    SteamInfo.fileUpload("Settings",config)

# 更新所有音量
func update_volumes():
    var actual_music_volume = 0.0 if is_muted else (music_volume * master_volume)
    var actual_sfx_volume = 0.0 if is_muted else (sfx_volume * master_volume)
    
    music_player.volume_db = linear_to_db(actual_music_volume)
    ui_player.volume_db = linear_to_db(actual_sfx_volume)
    
    # 更新所有音效播放器的音量
    for sfx_player in sfx_players:
        sfx_player.volume_db = linear_to_db(actual_sfx_volume)
    
    volume_changed.emit()

# 获取可用的音效播放器
func _get_available_sfx_player() -> AudioStreamPlayer:
    # 首先查找未在播放的播放器
    for player in sfx_players:
        if not player.playing:
            return player
    
    # 如果所有播放器都在使用中，返回播放时间最长的那个
    var oldest_player = sfx_players[0]
    var longest_time = 0.0
    
    for player in sfx_players:
        if player.get_playback_position() > longest_time:
            longest_time = player.get_playback_position()
            oldest_player = player
    
    return oldest_player

# 设置主音量
func set_master_volume(volume: float):
    master_volume = clamp(volume, 0.0, 1.0)
    update_volumes()

# 设置音乐音量
func set_music_volume(volume: float):
    music_volume = clamp(volume, 0.0, 1.0)
    update_volumes()

# 设置音效音量
func set_sfx_volume(volume: float):
    sfx_volume = clamp(volume, 0.0, 1.0)
    update_volumes()

# 静音/取消静音
func toggle_mute():
    is_muted = !is_muted
    update_volumes()
    return is_muted

# 播放背景音乐
func play_music(music_path: String):
    # 如果已经在播放相同的音乐，则不做任何操作
    if music_player.stream and music_player.playing and music_player.stream.resource_path == music_path:
        return
    
    # 停止当前音乐
    music_player.stop()
    
    # 加载并播放新音乐
    var music_stream = load(music_path)
    if music_stream:
        music_player.stream = music_stream
        music_player.play()

# 播放音效
func play_sfx(sfx_path: String):
    # 获取一个可用的音效播放器
    var player = _get_available_sfx_player()
    
    # 加载并播放音效
    var sfx_stream = load(sfx_path)
    if sfx_stream:
        player.stream = sfx_stream
        player.play()

# 停止音效并回到最初
func stop_sfx():
    for player in sfx_players:
        player.stop()

# 停止所有音频
func stop_all_audio():
    music_player.stop()
    stop_sfx()
    ui_player.stop()

# 播放主菜单音乐
func play_main_menu_music():
    play_music(MUSIC_MAIN_MENU)

# 播放游戏进行时音乐
func play_gameplay_music():
    play_music(MUSIC_GAMEPLAY)

# 播放胜利音乐
func play_victory_music():
    play_music(MUSIC_VICTORY)

# 播放失败音乐
func play_defeat_music():
    play_music(MUSIC_DEFEAT)

# 播放卡牌移动音效
func play_card_move_sfx():
    play_sfx(SFX_CARD_MOVE)

# 播放卡牌翻转音效
func play_card_flip_sfx():
    play_sfx(SFX_CARD_FLIP)

# 播放骰子投掷音效
func play_dice_roll_sfx():
    play_sfx(SFX_DICE_ROLL)

# 播放BUFF激活音效
func play_buff_activate_sfx():
    play_sfx(SFX_BUFF_ACTIVATE)

# 播放阶结束音效
func play_round_end_sfx():
    play_sfx(SFX_ROUND_END)

# 播放按钮点击音效
func play_button_click_sfx():
    play_sfx(SFX_BUTTON_CLICK)

# 音乐播放完成回调
func _on_music_finished():
    # 如果是背景音乐，则循环播放
    if music_player.stream:
        if music_player.stream.resource_path == MUSIC_MAIN_MENU or music_player.stream.resource_path == MUSIC_GAMEPLAY:
            music_player.play()

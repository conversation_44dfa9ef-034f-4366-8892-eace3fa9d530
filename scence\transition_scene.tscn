[gd_scene load_steps=10 format=3 uid="uid://dq5osum61avny"]

[ext_resource type="Script" uid="uid://bn5hpycapeenh" path="res://script/transition_scene.gd" id="1_r8tp1"]
[ext_resource type="Texture2D" uid="uid://e1rros1od7sd" path="res://assert/items/duck_dict.png" id="2_uwhj6"]
[ext_resource type="Texture2D" uid="uid://jygq5ehlrdgx" path="res://assert/back/coins_back.png" id="3_back"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xr6x3"]
bg_color = Color(0.717647, 0, 0.984314, 1)
skew = Vector2(0.2, 0)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10
shadow_size = 2
shadow_offset = Vector2(4, 4)

[sub_resource type="Animation" id="Animation_ulnky"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("%DuckSprite:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}

[sub_resource type="Animation" id="Animation_5fogi"]
resource_name = "duck_shake"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("%DuckSprite:rotation")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [-0.261799, 0.0, -0.261799]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_e1jwc"]
_data = {
&"RESET": SubResource("Animation_ulnky"),
&"duck_shake": SubResource("Animation_5fogi")
}

[sub_resource type="Shader" id="Shader_vignette"]
code = "shader_type canvas_item;

uniform float vignette_intensity : hint_range(0.0, 1.0) = 0.4;
uniform float vignette_opacity : hint_range(0.0, 1.0) = 0.5;
uniform vec4 vignette_rgb : source_color = vec4(0.0, 0.0, 0.0, 1.0);

float vignette(vec2 uv){
    uv *= 1.0 - uv.xy;
    float vignette = uv.x * uv.y * 15.0;
    return pow(vignette, vignette_intensity);
}

void fragment(){
    vec4 color = vignette_rgb;
    color.a = 1.0 - vignette(UV);
    color.a *= vignette_opacity;
    COLOR = color;
}"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_vignette"]
shader = SubResource("Shader_vignette")
shader_parameter/vignette_intensity = 0.4
shader_parameter/vignette_opacity = 0.5
shader_parameter/vignette_rgb = Color(0, 0, 0, 1)

[node name="TransitionScene" type="CanvasLayer"]
layer = 10
follow_viewport_scale = 0.001
script = ExtResource("1_r8tp1")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="SlideContainer" type="Control" parent="Control"]
unique_name_in_owner = true
clip_contents = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="SlidePanel" type="TextureRect" parent="Control/SlideContainer"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_back")
expand_mode = 1
stretch_mode = 4

[node name="ProgressContainer" type="MarginContainer" parent="Control"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 14
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_top = 50.0
offset_bottom = 110.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 100
theme_override_constants/margin_right = 100

[node name="VBoxContainer" type="VBoxContainer" parent="Control/ProgressContainer"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="LoadingLabel" type="Label" parent="Control/ProgressContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "LoadingLabel"
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="Control/ProgressContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_xr6x3")
max_value = 1.0
value = 0.5
show_percentage = false

[node name="AnimationPlayer" type="AnimationPlayer" parent="Control/ProgressContainer/VBoxContainer/ProgressBar"]
libraries = {
&"": SubResource("AnimationLibrary_e1jwc")
}
autoplay = "duck_shake"

[node name="DuckSprite" type="TextureRect" parent="Control/ProgressContainer/VBoxContainer/ProgressBar"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_top = -30.0
offset_right = 60.0
offset_bottom = 30.0
grow_vertical = 2
pivot_offset = Vector2(30, 30)
texture = ExtResource("2_uwhj6")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="VignetteMask" type="ColorRect" parent="Control"]
material = SubResource("ShaderMaterial_vignette")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Upcards"
config/version="1.0"
run/main_scene="uid://cimgv8aklwaks"
config/features=PackedStringArray("4.4", "Forward Plus")
boot_splash/show_image=false
boot_splash/image="uid://c8v0oifkf6wid"
config/icon="res://assert/icon/game_icon.png"

[autoload]

SteamInfo="*res://script/steam_info.gd"
LanguageManager="*res://script/language_manager.gd"
GameManager="*res://script/game_manager.gd"
CardSystem="*res://script/card_system.gd"
BuffManager="*res://script/buff_manager.gd"
AudioManager="*res://script/audio_manager.gd"
AnimationManager="*res://script/animation_manager.gd"
CursorManager="*res://script/cursor_manager.gd"
UIManager="*res://script/ui_manager.gd"
DeckDiscardAreaManager="*res://script/deck_discard_area_manager.gd"
SceneManager="*res://script/scene_manager.gd"
RewardWeightManager="*res://script/reward_weight_manager.gd"

[display]

window/stretch/mode="canvas_items"

[internationalization]

locale/translations=PackedStringArray("res://translations/main_menu.en.translation", "res://translations/main_menu.ja.translation", "res://translations/main_menu.ko.translation", "res://translations/main_menu.ru.translation", "res://translations/main_menu.zh_CN.translation")
locale/translations_pot_files=PackedStringArray("res://translations/main_menu.csv")
locale/locale_filter_mode=1
locale/language_filter=PackedStringArray("en", "zh_CN", "ja", "ko", "ru")
locale/locale_filter=["en", "zh_CN", "ja", "ko", "ru"]

[physics]

2d/subwindows/embed_subwindows=false

[rendering]

textures/canvas_textures/default_texture_repeat=1

[shader_globals]

mouse_screen_pos={
"type": "vec2",
"value": Vector2(0, 0)
}

shader_type canvas_item;

uniform vec2 shadow_offset = vec2(10.0, 10.0); 

uniform vec4 shadow_color : source_color = vec4(0.0, 0.0, 0.0, 0.3);

varying float sprite_rotation;

vec2 rotate_point(vec2 point, float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return vec2(
        point.x * c - point.y * s,
        point.x * s + point.y * c
    );
}

void vertex() {
    sprite_rotation = atan(MODEL_MATRIX[0][1], MODEL_MATRIX[0][0]);
    
    VERTEX.xy += rotate_point(shadow_offset, -sprite_rotation);
}

void fragment() {
    
    vec4 tex_color = texture(TEXTURE, UV);
   
    COLOR = tex_color * shadow_color;
}

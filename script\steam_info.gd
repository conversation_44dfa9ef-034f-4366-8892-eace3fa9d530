extends Node
    
const SETTING_PATH = "user://settings.cfg"

func _ready() -> void:
    initialize_steam()
    init_config()

    Steam.file_write_async_complete.connect(file_write_complete)
    

func init_config():
    var configString = fileDownload("Settings")
    var config = ConfigFile.new()

    # 将字符串转换为 ConfigFile
    if configString != null and configString != "":
        # 直接解析配置字符串
        var parse_result = config.parse(configString)
        if parse_result != OK:
            print("解析配置字符串失败，使用默认配置")
            config = null
    else:
        print("配置字符串为空，使用默认配置")
        config = null
    print("steam download:",config)
    if config == null:
         # 如果配置文件不存在，创建新的
        config = ConfigFile.new()
        # 保存窗口设置
        config.set_value("display", "fullscreen", true)
        config.set_value("display", "borderless", true)
        config.set_value("display", "width", DisplayServer.window_get_size().x)
        config.set_value("display", "height", DisplayServer.window_get_size().y)
        
        # 保存音量设置
        var music_bus_idx = AudioServer.get_bus_index("Music")
        var sfx_bus_idx = AudioServer.get_bus_index("SFX")
        
        if music_bus_idx >= 0:
            var music_volume_db = AudioServer.get_bus_volume_db(music_bus_idx)
            config.set_value("audio", "music_volume", music_volume_db)
        
        if sfx_bus_idx >= 0:
            var sfx_volume_db = AudioServer.get_bus_volume_db(sfx_bus_idx)
            config.set_value("audio", "sfx_volume", sfx_volume_db)
        
        # 保存语言设置
        config.set_value("language", "locale", "en")
        
        # 保存教程状态（如果存在）
        if config.has_section_key("Tutorial", "completed"):
            var tutorial_completed = config.get_value("Tutorial", "completed")
            config.set_value("Tutorial", "completed", tutorial_completed)
    # config.save(SETTING_PATH)

func initialize_steam() -> void:
    var initialize_response: Dictionary = Steam.steamInitEx()
    print("Did Steam initialize?: %s" % initialize_response)

    if initialize_response['status'] > Steam.STEAM_API_INIT_RESULT_OK:
        print("Failed to initialize Steam, shutting down: %s" % initialize_response)
        get_tree().quit()
    else:
        print("Steam initialized successfully")
        var userId = Steam.getSteamID()
        var name = Steam.getFriendPersonaName(userId)
        print("Steam user ID: %s, name: %s" % [userId, name])
        
func _init() -> void:
    # Set your game's Steam app ID here
    OS.set_environment("SteamAppId", str(3907110))
    OS.set_environment("SteamGameId", str(3907110))

# 成就解锁
func achieve(achieve_name):
    var achievement = Steam.getAchievement(achieve_name)
    if achievement.ret && !achievement.achieved:
        var achieve = Steam.setAchievement(achieve_name)
        var store = Steam.storeStats()
        print("achieve succeed.achieve name is %s ,achievement is %s ,store is %s" % [achieve_name, achievement, store])
    else:
        print("achieve failed.achievement is %s" % achievement)
        
# 文件上传
func fileUpload(fileName,file):
    if fileName and file:
        Steam.fileWriteAsync(fileName,file.encode_to_text().to_utf8_buffer())
        print("steam upload succeed")
    else:
        print("steam upload failed")

# 文件下载
func fileDownload(fileName):
    if Steam.fileExists(fileName):
        var data: Dictionary = Steam.fileRead(fileName, Steam.getFileSize(fileName))
        var pba: PackedByteArray = data.buf
        var cfgData = pba.get_string_from_utf8()
        return cfgData
    return null

func file_write_complete(result):
    print("file upload succeed!result is :"+result)

extends Panel

# 游戏结算面板 - 处理游戏结束时的结算界面

# 节点引用
@onready var result_label = $VBoxContainer/ResultLabel
@onready var score_label = $VBoxContainer/ScoreLabel
@onready var level_round_label = $VBoxContainer/HBoxContainer/LeveRoundlLabel
@onready var record_label = $VBoxContainer/HBoxContainer/RecordLabel
@onready var restart_button = $VBoxContainer/ButtonsContainer/RestartButton
@onready var main_menu_button = $VBoxContainer/ButtonsContainer/MainMenuButton
@onready var slot_items = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems
@onready var sprite1 = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems/Sprite1
@onready var sprite2 = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems/Sprite2
@onready var sprite3 = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems/Sprite3
@onready var sprite4 = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems/Sprite4
@onready var sprite5 = $VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems/Sprite5
@onready var mask = $VBoxContainer/Mask
@onready var restart_mask = $VBoxContainer/ButtonsContainer/RestartButton/Mask
@onready var main_menu_mask = $VBoxContainer/ButtonsContainer/MainMenuButton/Mask

# 音效路径
const SPINNING_SOUND = "res://assert/audio/sfx/spinning.mp3"
const GAME_OVER_SOUND = "res://assert/audio/sfx/game_over02.mp3"
const BUTTON_HOVER_SOUND = "res://assert/audio/sfx/button_pop02.mp3"

# 动画相关常量
const SHOW_ANIMATION_DURATION = 0.5
const HIDE_ANIMATION_DURATION = 0.3
const SHOW_SCALE_START = Vector2(0.5, 0.5)
const SHOW_SCALE_END = Vector2(1.0, 1.0)
const SHOW_MODULATE_START = Color(1, 1, 1, 0)
const SHOW_MODULATE_END = Color(1, 1, 1, 1)

# 老虎机相关变量
var available_dishes = []  # 将在_ready中从配置文件加载
var current_dish_index = 0
var is_spinning = false
var spin_speed = 0.0
var max_spin_speed = 1600.0  # 每秒移动的像素数
var spin_deceleration = 800.0  # 每秒减少的速度
var initial_y = 0.0  # 初始Y位置
var target_y = 0.0  # 目标Y位置
var final_dish_name = ""
const SPRITE_SPACING = 192  # 精灵之间的间距
var target_dish_index = 0  # 目标图片索引
var total_distance = 0.0  # 总移动距离
var spinning_sound_player: AudioStreamPlayer  # 用于循环播放滚动音效

# 动画相关变量
var bounce_tween: Tween
var heartbeat_tween: Tween
const BOUNCE_SCALE = 1.3  # 弹跳时的最大缩放
const HEARTBEAT_MIN_SCALE = 0.9  # 心跳最小缩放
const HEARTBEAT_MAX_SCALE = 1.3  # 心跳最大缩放
const BOUNCE_DURATION = 0.5  # 弹跳动画持续时间
const HEARTBEAT_DURATION = 0.8  # 心跳动画持续时间
const HEARTBEAT_PAUSE = 3.0  # 心跳间隔时间
const ROTATION_ANGLE = 0.1  # 旋转角度（弧度）

# 初始化
func _ready():
    # 设置初始状态
    modulate = SHOW_MODULATE_START
    scale = SHOW_SCALE_START
    visible = false
    
    # 设置面板样式
    _setup_title_style()
    _setup_button_style()
    
    # 从配置文件加载菜品数据
    _load_dishes_from_config()
    
    # 连接信号
    restart_button.pressed.connect(_on_restart_button_pressed)
    main_menu_button.pressed.connect(_on_main_menu_button_pressed)
    
    # 连接鼠标移入信号
    restart_button.mouse_entered.connect(_on_button_mouse_entered)
    main_menu_button.mouse_entered.connect(_on_button_mouse_entered)
    
    # 设置按钮初始缩放
    restart_button.scale = Vector2(1.0, 1.0)
    main_menu_button.scale = Vector2(1.0, 1.0)
    
    # 记录初始Y位置
    initial_y = 0  # 重置初始位置为0
    
    # 初始化音效播放器
    spinning_sound_player = AudioStreamPlayer.new()
    spinning_sound_player.bus = "SFX"
    add_child(spinning_sound_player)
    
    # 连接语言变化信号
    LanguageManager.language_changed.connect(_on_language_changed)

# 语言变化时的处理函数
func _on_language_changed() -> void:
    # 重新加载菜品数据
    _load_dishes_from_config()

# 从配置文件加载菜品数据
func _load_dishes_from_config():
    var config = ConfigFile.new()
    var translation_config = ConfigFile.new()
    
    # 加载菜品基础配置
    var err = config.load("res://config/dish_name.cfg")
    if err != OK:
        push_error("Failed to load dish configuration!")
        return
    
    # 加载菜品翻译配置
    var current_locale = TranslationServer.get_locale()
    var trans_err = translation_config.load("res://config/dish_translations.cfg")
    if trans_err != OK:
        push_error("Failed to load dish translations!")
        return
    
    # 清空现有菜品列表（以防重新加载）
    available_dishes.clear()
    
    # 加载配置数据
    for section in config.get_sections():
        var dish_type = config.get_value(section, "type")
        var translation_key = dish_type + "." + current_locale
        
        # 获取当前语言的翻译，如果没有则回退到英文
        var dish_name = translation_config.get_value(translation_key, "name", 
            translation_config.get_value(dish_type + ".en", "name", config.get_value(section, "name")))
        var dish_desc = translation_config.get_value(translation_key, "description",
            translation_config.get_value(dish_type + ".en", "description", config.get_value(section, "description")))
        
        var dish = {
            "name": dish_name,
            "description": dish_desc,
            "texture": load(config.get_value(section, "icon")),
            "type": dish_type
        }
        available_dishes.append(dish)

# 开始老虎机效果
func start_slot_machine():
    if available_dishes.size() == 0:
        push_error("No dish data available!")
        return
    
    # 设置mask的z_index为0，确保在滚动时遮挡菜品
    mask.z_index = 0
    
    # 随机选择最终的菜品
    target_dish_index = randi() % available_dishes.size()
    current_dish_index = 0  # 重置索引
    total_distance = 0.0  # 重置总距离
    
    # 设置初始图片
    slot_items.position.y = initial_y  # 重置位置
    _update_slot_sprites()
    
    # 开始旋转
    is_spinning = true
    spin_speed = max_spin_speed
    
    # 开始循环播放旋转音效
    var spinning_stream = load(SPINNING_SOUND)
    if spinning_stream:
        spinning_sound_player.stream = spinning_stream
        spinning_sound_player.stream.loop = true  # 设置循环播放
        spinning_sound_player.play()
    
    # 计算目标位置（确保最终停在中间的sprite3位置）
    var total_dishes = available_dishes.size()
    var spins = 2  # 完整旋转的圈数
    target_y = initial_y - (total_dishes * SPRITE_SPACING * spins + target_dish_index * SPRITE_SPACING)
    
    # 确保按钮在动画完成前不可点击
    restart_button.disabled = true
    main_menu_button.disabled = true

# 更新老虎机显示的图片
func _update_slot_sprites():
    var total = available_dishes.size()
    
    # 如果已经停止旋转，只显示最终选中的图片
    if not is_spinning:
        sprite1.visible = false
        sprite2.visible = false
        sprite4.visible = false
        sprite5.visible = false
        sprite3.visible = true
        # 确保显示正确的图片
        sprite3.texture = available_dishes[target_dish_index].texture
        return
    
    # 旋转过程中显示所有图片
    sprite1.visible = true
    sprite2.visible = true
    sprite3.visible = true
    sprite4.visible = true
    sprite5.visible = true
    
    var idx1 = current_dish_index % total
    var idx2 = (current_dish_index + 1) % total
    var idx3 = (current_dish_index + 2) % total
    var idx4 = (current_dish_index + 3) % total
    var idx5 = (current_dish_index + 4) % total
    
    sprite1.texture = available_dishes[idx1].texture
    sprite2.texture = available_dishes[idx2].texture
    sprite3.texture = available_dishes[idx3].texture
    sprite4.texture = available_dishes[idx4].texture
    sprite5.texture = available_dishes[idx5].texture

# 处理老虎机动画
func _process(delta):
    if not is_spinning:
        return
    
    # 移动老虎机
    var movement = spin_speed * delta
    slot_items.position.y -= movement
    total_distance += movement  # 累计总移动距离
    
    # 如果超过一个图片的高度，更新图片
    if slot_items.position.y <= initial_y - SPRITE_SPACING:
        slot_items.position.y += SPRITE_SPACING
        current_dish_index = (current_dish_index + 1) % available_dishes.size()
        _update_slot_sprites()
    
    # 减速
    var remaining_distance = abs(target_y + total_distance)  # 计算到目标的剩余距离
    var speed_factor = min(1.0, remaining_distance / 1000.0)  # 调整减速距离
    spin_speed = max(300.0, spin_speed - spin_deceleration * delta * speed_factor)
    
    # 检查是否应该停止
    if spin_speed <= 300.0:
        # 检查是否到达目标图片
        var normalized_position = fmod(total_distance, SPRITE_SPACING)  # 获取在当前周期内的相对位置
        
        # 计算当前中间位置显示的图片索引
        var middle_index = (current_dish_index + 2) % available_dishes.size()
        
        # 如果接近目标位置且中间图片是目标图片，则停止
        if abs(normalized_position) < 10.0 and middle_index == target_dish_index:
            is_spinning = false
            slot_items.position.y = initial_y - normalized_position  # 精确定位
            
            # 设置mask的z_index为-1，让菜品显示在前面
            mask.z_index = -1
            
            # 停止旋转音效
            spinning_sound_player.stop()
            
            # 更新结果文本
            final_dish_name = available_dishes[target_dish_index].name
            result_label.text = "[center]" + tr("YouCookTo") + "[color=red]\n%s[/color][/center]" % final_dish_name
            
            # 开始弹跳动画
            start_bounce_animation()
            
            # 重新启用按钮并隐藏遮罩
            restart_button.disabled = false
            main_menu_button.disabled = false
            restart_mask.visible = false
            main_menu_mask.visible = false

# 开始弹跳动画
func start_bounce_animation():
    # 取消之前的动画（如果有）
    if bounce_tween:
        bounce_tween.kill()
    if heartbeat_tween:
        heartbeat_tween.kill()
    
    # 重置缩放
    sprite3.scale = Vector2(0.15, 0.15)
    
    # 创建新的弹跳动画
    bounce_tween = create_tween()
    bounce_tween.tween_property(sprite3, "scale", Vector2(0.15 * BOUNCE_SCALE, 0.15 * BOUNCE_SCALE), BOUNCE_DURATION * 0.4).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
    bounce_tween.tween_property(sprite3, "scale", Vector2(0.15, 0.15), BOUNCE_DURATION * 0.6).set_trans(Tween.TRANS_ELASTIC).set_ease(Tween.EASE_OUT)
    
    # 连接弹跳动画完成信号
    bounce_tween.finished.connect(start_heartbeat_animation)

# 开始心跳动画
func start_heartbeat_animation():
    # 创建新的心跳动画
    heartbeat_tween = create_tween()
    heartbeat_tween.set_loops()  # 设置循环
    
    # 重置初始状态
    sprite3.scale = Vector2(0.15, 0.15)
    sprite3.rotation = 0.0
    
    # 1. 第一阶段：放大并轻微旋转
    var parallel1 = heartbeat_tween.parallel()
    parallel1.tween_property(sprite3, "scale", 
        Vector2(0.15 * HEARTBEAT_MAX_SCALE, 0.15 * HEARTBEAT_MAX_SCALE), 
        HEARTBEAT_DURATION * 0.15
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
    
    parallel1.tween_property(sprite3, "rotation", 
        ROTATION_ANGLE, 
        HEARTBEAT_DURATION * 0.15
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
    
    # 2. 第二阶段：缩小并旋转回原位
    var parallel2 = heartbeat_tween.parallel()
    parallel2.tween_property(sprite3, "scale", 
        Vector2(0.15 * HEARTBEAT_MIN_SCALE, 0.15 * HEARTBEAT_MIN_SCALE), 
        HEARTBEAT_DURATION * 0.2
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
    
    parallel2.tween_property(sprite3, "rotation", 
        0.0, 
        HEARTBEAT_DURATION * 0.2
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
    
    # 3. 第三阶段：恢复正常大小
    heartbeat_tween.tween_property(sprite3, "scale", 
        Vector2(0.15, 0.15), 
        HEARTBEAT_DURATION * 0.15
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
    
    # 4. 暂停等待下一次心跳
    heartbeat_tween.tween_interval(HEARTBEAT_PAUSE)

# 设置标题样式
func _setup_title_style():
    if result_label:
        result_label.add_theme_font_size_override("font_size", 15)
    if score_label:
        score_label.add_theme_font_size_override("font_size", 24)
        score_label.add_theme_color_override("font_color", Color(0.9, 0.9, 0.9))
    if level_round_label:
        level_round_label.add_theme_font_size_override("font_size", 17)
        level_round_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.0))
    if record_label:
        record_label.add_theme_font_size_override("font_size", 17)
        record_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.0))

# 设置按钮样式
func _setup_button_style():
    var button_style_normal = StyleBoxFlat.new()
    button_style_normal.bg_color = Color(0.2, 0.4, 0.8, 0.8)
    button_style_normal.corner_radius_top_left = 8
    button_style_normal.corner_radius_top_right = 8
    button_style_normal.corner_radius_bottom_left = 8
    button_style_normal.corner_radius_bottom_right = 8
    button_style_normal.border_width_left = 2
    button_style_normal.border_width_top = 2
    button_style_normal.border_width_right = 2
    button_style_normal.border_width_bottom = 2
    button_style_normal.border_color = Color(0.3, 0.5, 0.9, 0.8)
    
    var button_style_hover = StyleBoxFlat.new()
    button_style_hover.bg_color = Color(0.3, 0.5, 0.9, 0.8)
    button_style_hover.corner_radius_top_left = 8
    button_style_hover.corner_radius_top_right = 8
    button_style_hover.corner_radius_bottom_left = 8
    button_style_hover.corner_radius_bottom_right = 8
    button_style_hover.border_width_left = 2
    button_style_hover.border_width_top = 2
    button_style_hover.border_width_right = 2
    button_style_hover.border_width_bottom = 2
    button_style_hover.border_color = Color(0.4, 0.6, 1.0, 0.8)
    
    var button_style_pressed = StyleBoxFlat.new()
    button_style_pressed.bg_color = Color(0.15, 0.35, 0.75, 0.8)
    button_style_pressed.corner_radius_top_left = 8
    button_style_pressed.corner_radius_top_right = 8
    button_style_pressed.corner_radius_bottom_left = 8
    button_style_pressed.corner_radius_bottom_right = 8
    button_style_pressed.border_width_left = 2
    button_style_pressed.border_width_top = 2
    button_style_pressed.border_width_right = 2
    button_style_pressed.border_width_bottom = 2
    button_style_pressed.border_color = Color(0.2, 0.4, 0.8, 0.8)
    
    # 应用样式到按钮
    for button in [restart_button, main_menu_button]:
        button.add_theme_stylebox_override("normal", button_style_normal)
        button.add_theme_stylebox_override("hover", button_style_hover)
        button.add_theme_stylebox_override("pressed", button_style_pressed)
        button.add_theme_font_size_override("font_size", 18)
        button.add_theme_color_override("font_color", Color(1, 1, 1))
        button.add_theme_color_override("font_hover_color", Color(1, 1, 1))
        button.add_theme_color_override("font_pressed_color", Color(0.9, 0.9, 0.9))

# 鼠标移入按钮时播放音效
func _on_button_mouse_entered():
    # 获取触发鼠标移入事件的按钮
    var button = restart_button if restart_button.is_hovered() else main_menu_button
    
    # 只有当按钮未被禁用时才播放音效和显示动画效果
    if not button.disabled:
        # 播放音效
        var audio_manager = get_node("/root/AudioManager")
        if audio_manager:
            audio_manager.play_sfx(BUTTON_HOVER_SOUND)
        
        # 创建Q弹放大效果
        var hover_tween = create_tween()
        hover_tween.set_parallel(true)
        hover_tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.1).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
        
        # 处理鼠标移出事件
        button.mouse_exited.connect(
            func():
                var exit_tween = create_tween()
                exit_tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.1).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT),
            CONNECT_ONE_SHOT)

# 显示面板动画
func show_panel():
    visible = true
    # 显示遮罩
    restart_mask.visible = true
    main_menu_mask.visible = true
    var show_tween = create_tween()
    show_tween.set_parallel(true)
    show_tween.tween_property(self, "scale", SHOW_SCALE_END, SHOW_ANIMATION_DURATION).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
    show_tween.tween_property(self, "modulate", SHOW_MODULATE_END, SHOW_ANIMATION_DURATION).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_IN)

# 隐藏面板动画
func hide_panel():
    # 停止心跳动画
    stop_heartbeat_animation()

    var hide_tween = create_tween()
    hide_tween.set_parallel(true)
    hide_tween.tween_property(self, "scale", SHOW_SCALE_START, HIDE_ANIMATION_DURATION).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_IN)
    hide_tween.tween_property(self, "modulate", SHOW_MODULATE_START, HIDE_ANIMATION_DURATION).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
    hide_tween.finished.connect(_on_hide_animation_finished)

# 隐藏动画完成回调
func _on_hide_animation_finished():
    if is_instance_valid(self):
        visible = false

# 更新结算信息
func update_settlement_info():
    # 获取并禁用结束阶按钮
    #var end_round_button = get_node("/root/GameScene/%TopBar/BottomRight/NextButton/EndRoundButton")
    #if end_round_button:
        #end_round_button.disabled = true
    
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
    
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 播放游戏结束音效
    var audio_manager = get_node("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx(GAME_OVER_SOUND)
    
    # 更新关卡阶标签
    #level_round_label.text = "关卡： %d  /  阶: %d" % [game_manager.current_level, game_manager.current_round]
    level_round_label.text = tr("LevelTitle") + ":" + str(game_manager.current_level) + "/" + tr("RoundTitle") + ":" + str(game_manager.current_round)
    
    result_label.text = "[center]"+ tr("YouCookTo")+ "[/center]\n"
    
    # 显示面板并开始老虎机动画
    show_panel()
    start_slot_machine()

# 重新开始按钮点击处理
func _on_restart_button_pressed():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 清除BUFF显示区域
        var buff_display = get_parent().get_node("BuffDisplay")
        if buff_display:
            buff_display.clear_all_buffs()
        
        # 清除BUFF管理器中的激活BUFF
        var buff_manager = get_node("/root/BuffManager")
        if buff_manager:
            buff_manager.clear_active_buffs()

        # 清除永久BUFF展示区
        var forever_buff_area = get_parent().get_node_or_null("ForeverBuffArea")
        if forever_buff_area and forever_buff_area.has_method("clear_buffs"):
            forever_buff_area.clear_buffs()

        # 开始新游戏
        game_manager.start_new_game()
        
        # 隐藏结算面板
        hide_panel()

# 主菜单按钮点击处理
func _on_main_menu_button_pressed():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 隐藏结算面板
        hide_panel()
        # 返回主菜单
        game_manager.return_to_main_menu()

func _exit_tree():
    if heartbeat_tween:
        heartbeat_tween.kill()

# 停止心跳动画
func stop_heartbeat_animation():
    if heartbeat_tween and is_instance_valid(heartbeat_tween):
        heartbeat_tween.kill()
        heartbeat_tween = null
    if bounce_tween and is_instance_valid(bounce_tween):
        bounce_tween.kill()
        bounce_tween = null

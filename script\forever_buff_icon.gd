extends Control

signal mouse_entered_buff(buff_data: Dictionary)
signal mouse_exited_buff

var buff_data: Dictionary = {}
@onready var buff_icon: TextureRect = %BuffIcon
@onready var ball: Control = $Ball
@onready var background: TextureRect = $Ball/Background
var float_tween: Tween
var hover_tween: Tween

const HOVER_SCALE = 1.2  # 悬停时的放大比例
const HOVER_DURATION = 0.3  # 动画持续时间

func _ready() -> void:
    ball.mouse_entered.connect(_on_mouse_entered)
    ball.mouse_exited.connect(_on_mouse_exited)
    # 使用动画管理器启动浮动动画
    # float_tween = AnimationManager.play_buff_icon_float_animation(ball)
    
    # 应用边缘模糊shader
    var shader = load("res://shader/blur_edge.gdshader")
    background.material = ShaderMaterial.new()
    background.material.shader = shader
    background.material.set_shader_parameter("blur_amount", 1.0)
    background.material.set_shader_parameter("edge_threshold", 0.2)

func setup(data: Dictionary) -> void:
    buff_data = data
    if data.has("icon") and data.icon != null:
        buff_icon.texture = load(data.icon)
    # Set background texture from card_pic
    if data.has("card_pic") and data.card_pic != null:
        background.texture = load(data.card_pic)

func _on_mouse_entered() -> void:
    mouse_entered_buff.emit(buff_data)
    _play_hover_animation(true)

func _on_mouse_exited() -> void:
    mouse_exited_buff.emit()
    _play_hover_animation(false)

func _play_hover_animation(is_hovering: bool) -> void:
    # 如果存在之前的hover动画，先停止它
    if hover_tween and hover_tween.is_valid():
        hover_tween.kill()
    
    hover_tween = create_tween()
    hover_tween.set_trans(Tween.TRANS_ELASTIC)  # 使用弹性过渡效果
    hover_tween.set_ease(Tween.EASE_OUT)
    
    if is_hovering:
        hover_tween.tween_property(ball, "scale", Vector2.ONE * HOVER_SCALE, HOVER_DURATION)
    else:
        hover_tween.tween_property(ball, "scale", Vector2.ONE, HOVER_DURATION)

func _exit_tree() -> void:
    # 清理tween
    if float_tween and float_tween.is_valid():
        float_tween.kill()
    if hover_tween and hover_tween.is_valid():
        hover_tween.kill() 

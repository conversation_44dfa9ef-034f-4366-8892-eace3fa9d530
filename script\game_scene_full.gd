extends Control

# 游戏场景 - 处理主要游戏界面的逻辑

# 信号定义
signal card_drag_started(card_node)
signal card_drag_ended(card_node)
signal dice_confirmed(dice_result)
signal scene_exiting

# 节点引用
@onready var score_label = %TopBar/ScoreInfo/ScoreLabel
@onready var limit_label = %TopBar/LimitInfo/LimitLabel
@onready var action_points_display = %TopBar/ActionPointsDisplay
@onready var hand_container = %PlayerArea/HandContainer
@onready var pool_container = %PoolArea/PoolContainer
@onready var deck_discard_area = %RightSide/DeckDiscardArea # 抽牌区
@onready var deck_discard_image = %RightSide/DeckDiscardArea/DeckDiscardImage
@onready var discard_area = %RightSide/DiscardArea # 弃牌区
@onready var deck_count_label = %RightSide/DeckCountLabel
@onready var end_round_button = %EndRoundButton
@onready var gear_button = %TopBar/LeftButtons/GearButton
@onready var settings_menu_panel = $SettingsMenuPanel
@onready var dice_panel = $DicePanel
@onready var settings_panel = $SettingsPanel
@onready var game_over_panel = $GameOverPanel
@onready var rule_panel = $RulePanel
@onready var score_display = %PlayerArea/ScoreDisplay
@onready var tooltip_bubble = $TooltipBubble
@onready var buff_display = %BuffDisplay
@onready var card_detail_panel = null
@onready var cursor_manager = CursorManager
@onready var game_overlay = $DicePanel/GameOverlay
@onready var firework_particles = %FireworkParticles
@onready var change_chip = %RightSide/ChangeChip
@onready var change_chip_shadow = %RightSide/ChangeChip/Shadow
@onready var change_chip_texture = %RightSide/ChangeChip/TextureRect
@onready var change_chip_shader = %RightSide/ChangeChip/Shader
@onready var change_chip_shader2 = %RightSide/ChangeChip/Shader2
@onready var change_draw_card_shader = %RightSide/DeckDiscardArea/ShaderCard
@onready var add_pic = %Add
@onready var change_pic = %Change
@onready var magic_particles = %RightSide/DeckDiscardArea/MagicParticles
@onready var reduction_hint = %ReductionHint
@onready var is_magic = false  # 是否为魔法状态，替换所有公共池牌
@onready var dice_num_label = %DiceNumLable # 新增骰子总数标签引用
# 类型提示
@onready var UIManager := get_node_or_null("/root/UIManager") as Node
# 在变量声明部分添加TopBarManager的引用
@onready var top_bar_manager = %TopBar
@onready var score_value_label = null  # 将在_ready()中初始化
@onready var score_coin = null  # 将在_ready()中初始化
@onready var is_score_animating = false  # 积分动画状态标志
var is_end_round_processing = false  # 阶结束处理状态
var animation_manager = null  # AnimationManager引用

@onready var scroll = $BottomRight/Scroll
@onready var scroll_button = %ScrollButton
@onready var card_rules_panel = $CardRulesPanel
@onready var forever_buff_area = $ForeverBuffArea
@onready var tutorial_overlay = $tutorialOverlay
@onready var buff_manager = get_node("/root/BuffManager")

# 卡牌节点场景
const CARD_NODE_SCENE = preload("res://scence/card_node.tscn")
const CARD_LANDING_PARTICLES = preload("res://scence/card_landing_particles.tscn")

# 变量
var selected_hand_card = null
var selected_pool_card = null
var hand_card_nodes = []
var pool_card_nodes = []
var is_dragging = false
var hover_card = null  # 当前悬停的卡牌
var dragging_card_original_index = -1  # 拖动中的卡牌原始索引
var drop_indicator = null  # 卡牌放置位置指示器
var target_card_index = -1  # 拖动目标位置索引

# 积分限制标签动画变量
var limit_label_animation_timer = 0.0
var limit_label_animation_interval = 3.0  # 每3秒动画一次
var is_limit_label_animating = false  # 是否正在播放动画
var is_other_animation_playing = false  # 是否有其他动画正在播放

# 节点引用部分添加新的管理器引用
@onready var deck_discard_manager = null # 抽牌弃牌区管理器
@onready var discard_area_manager = null # 弃牌区管理器

# 添加变量来跟踪鼠标是否悬停在按钮上
var is_button_hovered = false

# 节点引用
@onready var hover_tooltip = preload("res://scence/hover_tooltip.tscn")
#@onready var buff_tooltip_area = $Background/Draw/Block3/ColorRect
#@onready var common_tooltip_label = $Background/Draw/Block4/ColorRect
#@onready var hand_tooltip_label = $Background/Draw/Block5/ColorRect
#@onready var push_tooltip_label = $Background/Draw/Block1/ColorRect
#@onready var pull_tooltip_label = $Background/Draw/Block2/ColorRect
@onready var hover_card_indicator = null # 悬停时的卡牌指示器
#@onready var beer_area = $Background/LeftBG/Concat
@onready var current_tooltip_instance = null # 当前活动的tooltip实例

# 在节点引用部分添加
@onready var debug_panel = $DebugPanel

var scroll_float_tween: Tween
var scroll_hover_tween: Tween

# 卡牌布局参数
var base_card_width = 90  # 基础卡牌宽度
var base_card_heigth = 135 # 基础卡牌高度
var spacing = 10  # 卡牌间距

var card_add_queue = []
var is_processing_card_queue = false

func _ready():
    # 初始化背景音乐播放器
    _setup_background_music()
    
    # 获取全局管理器引用
    animation_manager = get_node("/root/AnimationManager")
    
    # 连接信号
    end_round_button.pressed.connect(_on_end_round_button_pressed)
    
    # 连接骰子面板信号
    dice_panel.dice_total_value_changed.connect(_on_dice_total_value_changed)
    
    # 设置齿轮按钮
    if gear_button:
        # 设置齿轮按钮的缩放中心点
        gear_button.pivot_offset = gear_button.size / 2
        # 连接按钮信号
        gear_button.pressed.connect(_on_gear_button_pressed)
        # 连接鼠标进入/离开事件
        gear_button.mouse_entered.connect(_on_gear_button_mouse_entered)
        gear_button.mouse_exited.connect(_on_gear_button_mouse_exited)
        gear_button.button_down.connect(_on_gear_button_down)
        gear_button.button_up.connect(_on_gear_button_up)
    else:
        push_error("Gear button not found! Check if the node path is correct.")
    
    # 连接设置菜单面板的信号
    if settings_menu_panel:
        settings_menu_panel.rule_button_pressed.connect(_on_rule_button_pressed)
        settings_menu_panel.settings_button_pressed.connect(_on_settings_button_pressed)
        settings_menu_panel.exit_button_pressed.connect(_on_exit_button_pressed)
    else:
        push_error("Settings menu panel not found! Check if the scene is properly instantiated.")
    
    # 设置EndRoundButton的鼠标信号连接
    end_round_button.mouse_entered.connect(_on_end_round_button_mouse_entered)
    end_round_button.mouse_exited.connect(_on_end_round_button_mouse_exited)
    # end_round_button.button_down.connect(_on_end_round_button_down)
    # end_round_button.button_up.connect(_on_end_round_button_up)
    
    # 启动按钮心跳动画
    _start_button_heartbeat_animation()
    
    # 连接设置面板的显示改变信号
    if settings_panel:
        settings_panel.display_changed.connect(_on_display_changed)
    
    # 使用抽牌弃牌区管理器
    _setup_deck_discard_manager()
    
    # 设置弃牌区管理器
    _setup_discard_area_manager()
    
    # 初始隐藏面板
    dice_panel.visible = false
    settings_panel.visible = false
    game_over_panel.modulate = Color(1, 1, 1, 0)  # 设置初始透明度
    game_over_panel.scale = Vector2(0.5, 0.5)  # 设置初始缩放
    game_over_panel.visible = false
    rule_panel.visible = false
    
    # 初始化卡牌详情面板
    if not card_detail_panel:
        var detail_panel_scene = load("res://scence/card_detail_panel.tscn").instantiate()
        detail_panel_scene.name = "CardDetailPanel"
        add_child(detail_panel_scene)
        card_detail_panel = detail_panel_scene
    
    # 确保卡牌详情面板初始隐藏
    card_detail_panel.visible = false
    
    # 应用UI管理器的统一主题和样式
    if UIManager:
        UIManager.init_scene_ui(self)
    
    # 获取游戏管理器和卡牌系统
    var game_manager = get_node("/root/GameManager")
    var card_system = get_node("/root/CardSystem")
    
    if game_manager and card_system:
        # 连接游戏管理器信号
        game_manager.game_state_changed.connect(_on_game_state_changed)
        game_manager.round_changed.connect(_on_round_changed)
        # game_manager.score_changed.connect(_on_score_changed)
        game_manager.action_points_changed.connect(_on_action_points_changed)
        game_manager.dice_rolled.connect(_on_dice_rolled)
        game_manager.dice_count_changed.connect(_on_dice_count_changed)
        game_manager.hand_limit_changed.connect(_on_hand_limit_changed)
        
        # 连接卡牌系统信号
        card_system.card_added_to_hand.connect(_on_card_added_to_hand)
        card_system.card_added_to_pool.connect(_on_card_added_to_pool)
        card_system.cards_swapped.connect(_on_cards_swapped)
        card_system.hand_evaluated.connect(_on_hand_evaluated)
        card_system.buff_card_created.connect(_on_buff_card_created)
        card_system.buff_activated.connect(_on_buff_activated)
    
    # 开始新游戏
    game_manager.start_new_game()
    # 更新界面
    update_ui()
            
    # 创建BUFF显示区域
    if not buff_display:
        var buff_display_scene = Control.new()
        buff_display_scene.name = "BuffDisplay"
        buff_display_scene.set_script(load("res://script/buff_display.gd"))
        add_child(buff_display_scene)
        buff_display = buff_display_scene
        
    # 连接 BUFF 显示区域的右键点击信号
    if buff_display and buff_display.has_signal("buff_card_right_clicked"):
        buff_display.buff_card_right_clicked.connect(_on_buff_display_card_right_clicked_handler)
        
    # 初始化积分限制标签的原始状态
    limit_label.pivot_offset = limit_label.size / 2  # 设置缩放中心点
    
    # --- 修改积分显示部分 ---
    # 保存原始积分标签引用
    score_label = score_label
    score_value_label = score_label
    
    # 重新设置积分标签为只显示数字
    if score_label:
        score_label.text = "0"
        # score_label.add_theme_font_size_override("font_size", 28)  # 增大字号
        score_label.add_theme_color_override("font_color", Color(1.0, 0.84, 0.0, 1.0))  # 金色文字
        score_label.add_theme_constant_override("outline_size", 1)
        score_label.add_theme_color_override("font_outline_color", Color(0.5, 0.3, 0.0, 1.0))  # 描边
    
    # 找到金币节点
    score_coin = %TopBar/ScoreInfo/coins
    if score_coin:
        # 添加闪光效果的Shader
        var shine_material = ShaderMaterial.new()
        shine_material.shader = load("res://shader/coin_shine.gdshader")
        shine_material.set_shader_parameter("shine_color", Color(1.0, 1.0, 0.8, 0.8))
        shine_material.set_shader_parameter("shine_speed", 0.5)
        shine_material.set_shader_parameter("shine_width", 0.2)
        score_coin.material = shine_material
        
        # 设置旋转中心
        score_coin.pivot_offset = score_coin.size / 2
    
    # 牌堆数不可见
    # deck_count_label.visible = true
    
    # 确保TopBar有TopBarManager脚本
    if top_bar_manager and not is_instance_valid(top_bar_manager.get_script()):
        top_bar_manager.set_script(load("res://script/top_bar_manager.gd"))
    
    # 确保TopBar有TopBarManager脚本
    if top_bar_manager and is_instance_valid(top_bar_manager.get_script()):
        # 手动初始化top_bar_manager的节点引用
        top_bar_manager.init_references(
            %TopBar/RoundInfo/Round/RoundLabel,
            limit_label,
            score_label,
            %TopBar/LimitInfo/cash,
            %TopBar/RoundInfo/Round/round,
            score_coin,
            %TopBar/RoundInfo/Level/LevelLabel,
            %TopBar/RoundInfo/Level/level,
            %ProgressBar,
            %TopBar/RoundInfo/Round/RoundTitle,
            %TopBar/RoundInfo/Level/LevelTitle,
            reduction_hint,
        )
        # 确保处理功能启用
        top_bar_manager.set_process(true)
        
        # 延迟检查
        get_tree().create_timer(2.0).timeout.connect(func(): 
            top_bar_manager.force_enable_processing()  # 再次尝试启用
        )
    else:
        push_error("无法加载TopBarManager脚本！")
    
    # 清理可能存在的旧指示器
    if animation_manager:
        animation_manager.clean_drop_indicator()
    
    # 初始化行动点显示
    # if action_points_display:
    #     action_points_display.set_max_points(GameManager.max_action_points)  # 设置初始最大行动点数

    # 初始化区域提示
    #_setup_area_tooltips()

    # 设置羽毛节点的初始状态和信号连接
    if change_chip:
        change_chip.pivot_offset = change_chip.size / 2  # 设置旋转和缩放的中心点
        change_chip.mouse_entered.connect(_on_change_chip_mouse_entered)
        change_chip.mouse_exited.connect(_on_change_chip_mouse_exited)
        change_chip.gui_input.connect(_on_change_chip_gui_input)
        _start_feather_floating_animation()

    # 注册啤酒区域
    #if cursor_manager and beer_area:
        #cursor_manager.register_area(beer_area, cursor_manager.CursorArea.BEER_AREA)

    # 确保调试面板初始隐藏
    if debug_panel:
        debug_panel.hide()

    # 连接卷轴按钮信号
    scroll_button.mouse_entered.connect(_on_scroll_mouse_entered)
    scroll_button.mouse_exited.connect(_on_scroll_mouse_exited)
    scroll_button.pressed.connect(_on_scroll_pressed)
    
    # 开始浮动动画
    _start_scroll_float_animation()

    # 连接永久BUFF区域的信号
    forever_buff_area.buff_tooltip_requested.connect(_on_buff_tooltip_requested)
    forever_buff_area.buff_tooltip_hide_requested.connect(_on_buff_tooltip_hide_requested)
    
    # 连接buff_manager的永久BUFF信号
    buff_manager.permanent_buff_added.connect(_on_permanent_buff_added)

    # 检查是否需要显示教程
    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")
    var show_tutorial = true
    
    if err == OK:
        if config.has_section_key("Tutorial", "completed"):
            show_tutorial = not config.get_value("Tutorial", "completed")
    
    if show_tutorial:
        tutorial_overlay.visible = true

# 播放积分动画 - 统一所有积分更新的动画效果
func _play_score_animation(old_score, new_score, tween):
    # 使用AnimationManager中的方法
    if animation_manager:
        return animation_manager.play_score_change_animation(self.score_value_label, self.score_coin, old_score, new_score)
    
    return tween

func _play_limit_animation(old_score, new_score, tween):
    # 使用AnimationManager中的方法
    if animation_manager:
        return animation_manager.play_label_change_animation(self.limit_label, self.score_coin, old_score, new_score)
    
    return tween

# 通用的金币Q弹膨胀效果方法
func play_coin_elastic_effect(coin_node,scale_tween):
    # 使用AnimationManager中的方法
    if animation_manager:
        return animation_manager.play_coin_elastic_effect(coin_node, scale_tween)
    
    return scale_tween

# 更新界面
func update_ui():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    var card_system = get_node("/root/CardSystem")
    if game_manager:
        # 使用TopBarManager更新UI
        if top_bar_manager and top_bar_manager.has_method("update_ui"):
            top_bar_manager.update_ui(
                game_manager.current_round, 
                game_manager.current_score if game_manager.current_score > 0 else null, 
                game_manager.limit_score if game_manager.limit_score > 0 else null
            )
        
        # 更新行动点显示
        if action_points_display:
            action_points_display.update_points(game_manager.action_points)
        
        # 更新牌堆牌数显示
        if card_system:
            _update_deck_count()
        
        # 更新结束阶按钮状态
        _update_end_turn_button()

# 游戏状态变化处理
func _on_game_state_changed(new_state):
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
    
    match new_state:
        GameManager.GameState.PREPARATION:
            # 禁用结束阶按钮
            end_round_button.disabled = true
            
            # 只在关卡开始时显示骰子界面
            if game_manager.current_round == 1 or not game_manager.has_rolled_for_level:
                # 显示骰子界面并添加灰色蒙版
                action_points_display.update_points(0)
                dice_panel.show_dice_panel()
            else:
                # 如果不是关卡开始，直接进入行动阶段
                game_manager.change_game_state(GameManager.GameState.ACTION)
            
            # 标记其他动画正在播放
            is_other_animation_playing = true
            
        GameManager.GameState.ACTION:
            # 确保骰子面板被隐藏
            dice_panel.visible = false
            
            # 发牌
            _deal_initial_cards()
            
            # 动画结束后允许积分限制标签动画
            await get_tree().create_timer(1.0).timeout
            is_other_animation_playing = false
            
            # 启用结束阶按钮（如果手牌数量足够）
            _update_end_turn_button()
            
        GameManager.GameState.SETTLEMENT:
            # 结算阶段的处理...
            pass
            
        GameManager.GameState.GAME_OVER:
            # 禁用结束阶按钮
            end_round_button.disabled = true
            
            # 显示结算界面
            game_over_panel.visible = true
            game_over_panel.update_settlement_info()
            BuffManager.active_buffs.clear()
            BuffManager.buff_area.clear()
            
            # 停止积分限制标签动画
            is_other_animation_playing = true
            
        GameManager.GameState.MAIN_MENU:
            # 返回主菜单，使用过渡动画
            var scene_manager = get_node_or_null("/root/SceneManager")
            if scene_manager:
                scene_manager.change_scene_with_transition("res://scence/main_menu.tscn")

# 阶变化处理
func _on_round_changed(new_round):
    # 将阶变化事件委托给TopBarManager
    if top_bar_manager and is_instance_valid(top_bar_manager.get_script()):
        top_bar_manager._on_round_changed(new_round)

# 分数变化处理
# func _on_score_changed(new_score):
#     # 将分数变化事件委托给TopBarManager
#     if top_bar_manager and top_bar_manager.has_method("_on_score_changed"):
#         top_bar_manager._on_score_changed(new_score)

# 行动点变化处理
func _on_action_points_changed(new_points):
    # 更新行动点显示
    if action_points_display:
        action_points_display.update_points(new_points)

# 卡牌添加到手牌处理
func _on_card_added_to_hand(card_data):
    # 创建卡牌节点
    var card_node = CARD_NODE_SCENE.instantiate()
    hand_container.add_child(card_node)
    
    # 设置卡牌数据
    card_node.set_card_data(card_data, true, hand_card_nodes.size())

    if hand_card_nodes.size() >= 3:
        # 设置初始位置为抽牌/弃牌区域位置
        var start_position = deck_discard_image.global_position 
        
        # 计算手牌区的目标位置
        var target_position = AnimationManager.drop_indicator.global_position
        
        # 使用动画管理器播放抽牌动画
        if animation_manager:
            await animation_manager.play_draw_card_animation(card_node, start_position, target_position)
        
    # 连接信号
    card_node.card_drag_started.connect(_on_hand_card_drag_started)
    card_node.card_drag_ended.connect(_on_hand_card_drag_ended)
    card_node.card_clicked.connect(_on_hand_card_clicked)
    card_node.card_right_clicked.connect(_on_card_right_clicked)
    
    # 添加到手牌节点列表
    hand_card_nodes.append(card_node)
    
    # 更新手牌显示
    _update_hand_display()
    if hand_card_nodes.size() > 3:
        _play_card_landing_effect(card_node)
    
    # 检查这张新卡是否应该被禁用（青蛙牌效果）
    _check_card_forbidden(card_data, card_node)
    
    # 更新结束阶按钮状态
    _update_end_turn_button()
    # 注册手牌区域
    cursor_manager.register_area(card_node, cursor_manager.CursorArea.HAND_AREA)

    # 如果鼠标仍在抽牌区域内且不是魔法状态，更新指示器位置
    if deck_discard_manager and deck_discard_manager.mouse_is_in_discard_area and not is_magic:
        deck_discard_manager.show_next_card_indicator()
    
# 卡牌添加到公共牌池处理
func _on_card_added_to_pool(card_data):
    # 创建卡牌节点
    var card_node = CARD_NODE_SCENE.instantiate()
    pool_container.add_child(card_node)
    
    # 设置卡牌数据
    card_node.set_card_data(card_data, false, pool_card_nodes.size())
    
    # 连接信号 - 只连接点击和右键点击，不连接拖拽相关信号
    card_node.card_clicked.connect(_on_pool_card_clicked)
    card_node.card_right_clicked.connect(_on_card_right_clicked)
    # 禁用拖拽相关信号连接
    card_node.can_drag = false # 直接设置属性 
    card_node.set_draggable(false) # 调用方法确保一致性
    
    # 添加到公共牌池节点列表
    pool_card_nodes.append(card_node)
    
    # 更新公共牌池显示
    _update_pool_display()

    # 检查这张新卡是否应该被禁用（青蛙牌效果）
    _check_card_forbidden(card_data, card_node)
    
# 卡牌交换处理
func _on_cards_swapped(hand_index, pool_index):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    var buff_manager = get_node("/root/BuffManager")
    if not card_system or not buff_manager:
        return
    
    # 更新手牌节点
    if hand_index < hand_card_nodes.size():
        hand_card_nodes[hand_index].set_card_data(card_system.player_hand[hand_index], true, hand_index)
        # 检查交换后的手牌是否有BUFF，如果有，确保BUFF效果正确显示
        if card_system.player_hand[hand_index] and "buff" in card_system.player_hand[hand_index]:
            # 先应用BUFF效果
            hand_card_nodes[hand_index].set_buff_display(card_system.player_hand[hand_index].buff)
            # 然后应用浮动效果，确保两种效果都能正确显示
            hand_card_nodes[hand_index].apply_float_effect()
        # 检查该手牌是否应该被禁用（青蛙牌效果）
        _check_card_forbidden(card_system.player_hand[hand_index], hand_card_nodes[hand_index])
    
    # 更新公共牌池节点
    if pool_index < pool_card_nodes.size():
        pool_card_nodes[pool_index].set_card_data(card_system.common_pool[pool_index], false, pool_index)
        # 确保公共牌池卡牌仍然不可拖拽
        pool_card_nodes[pool_index].can_drag = false
        pool_card_nodes[pool_index].set_draggable(false)
        # 检查该公共池牌是否应该被禁用（青蛙牌效果）
        _check_card_forbidden(card_system.common_pool[pool_index], pool_card_nodes[pool_index])
    
    # 播放卡牌交换音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/small-scratch.wav")
    
    # 更新手牌显示
    _update_hand_display()
    
    # 更新公共牌池显示
    _update_pool_display()
    
    # 评估手牌
    card_system.evaluate_hand()

# 手牌评估处理
func _on_hand_evaluated(score, hand_type):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 更新分数显示
    # score_display.text = "积分: %d (%s)" % [score, card_system.get_hand_type_name(hand_type)]

# 当BUFF卡牌被创建时
func _on_buff_card_created(card_data):
    # 可以在这里添加特效或提示
    pass

# 当BUFF被激活时
func _on_buff_activated(buff_data):
    # 获取BuffManager
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        return
    
    # 如果是青蛙牌BUFF，标记禁用的卡牌
    if buff_data.type == buff_manager.BuffType.FROG and "additional_data" in buff_data:
        _mark_forbidden_cards()

# 标记禁用花色的卡牌
func _mark_forbidden_cards():
    var forbidden_suits = _get_active_frog_forbidden_suit()
    if forbidden_suits.is_empty():
        return
        
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 标记手牌中的禁用卡牌
    for i in range(hand_card_nodes.size()):
        if i < card_system.player_hand.size():
            var card_data = card_system.player_hand[i]
            var card_node = hand_card_nodes[i]
            
            # 如果卡牌花色与禁用花色相同，标记为禁用
            if forbidden_suits.has(card_data.suit):
                card_node.set_disabled(true)
                # 同时更新card_data中的标记
                card_system.player_hand[i]["is_disabled"] = true
            else:
                card_node.set_disabled(false)
                # 更新card_data中的标记
                if "is_disabled" in card_system.player_hand[i]:
                    card_system.player_hand[i].erase("is_disabled")
    
    # 标记公共牌池中的禁用卡牌
    for i in range(pool_card_nodes.size()):
        if i < card_system.common_pool.size():
            var card_data = card_system.common_pool[i]
            var card_node = pool_card_nodes[i]
            
            # 如果卡牌花色与禁用花色相同，标记为禁用
            if forbidden_suits.has(card_data.suit):
                card_node.set_disabled(true)
                # 同时更新card_data中的标记
                card_system.common_pool[i]["is_disabled"] = true
            else:
                card_node.set_disabled(false)
                # 更新card_data中的标记
                if "is_disabled" in card_system.common_pool[i]:
                    card_system.common_pool[i].erase("is_disabled")

# 当骰子数量改变时
func _on_dice_count_changed(new_count):
    # 更新骰子面板
    if dice_panel:
        dice_panel.update_dice_count(new_count)

# 当手牌上限改变时
func _on_hand_limit_changed(new_limit):
    # 更新手牌区域
    # 显示手牌上限变化的提示
    var message = "手牌上限变为: %d" % new_limit
    # show_message(message)
    
    # 如果当前手牌数量超过新上限，可能需要弃牌
    #if CardSystem.player_hand.size() > new_limit:
        ## 提示弃牌
        #_trigger_discard_excess_cards(new_limit)

# 当骰子被掷出时
func _on_dice_rolled(dice_results):
    # 计算总点数作为行动点数
    var total_points = 0
    for value in dice_results:
        total_points += value
    GameManager.action_points = total_points
    
    # # 设置行动点数
    # action_points_display.set_max_points(total_points)
    pass

# 隐藏骰子面板
func _hide_dice_panel():
    # 隐藏骰子面板
    dice_panel.visible = false
    
    # 恢复BUFF显示区域的可见性
    if buff_display:
        buff_display.visible = true

# 手牌卡牌拖动开始
func _on_hand_card_drag_started(card_node):
    discard_area.get_node_or_null("DiscardImage").texture = load("res://assert/right/open.png")
    selected_hand_card = card_node
    is_dragging = true
    
    # 保存原始位置，用于限制卡牌放置
    card_node.original_position = card_node.position
    
    # 记录拖动卡牌的原始索引
    dragging_card_original_index = card_node.card_index
    target_card_index = dragging_card_original_index
    
    # 保存手牌在全局坐标系中的位置，用于后续交换动画
    card_node.set_meta("hand_global_position", card_node.global_position)
    
    # 发送拖拽开始信号给光标管理器
    emit_signal("card_drag_started", card_node)
    
    # 获取自适应布局参数
    var layout = _calculate_adaptive_layout()
    
    # 创建放置位置指示器
    if animation_manager:
        drop_indicator = animation_manager.create_drop_indicator(hand_container, layout)

# 卡牌拖放结束处理
func _on_card_drag_ended(card_node, drop_position):
    # 初始化标记，用于判断卡牌是否拖放到了公共牌池
    var dropped_on_pool_card = false
    
    # 获取拖动卡牌的全局矩形
    var dragged_card_rect = card_node.get_global_rect()
    
    # 检查是否拖到了公共牌池卡牌上
    for pool_card in pool_card_nodes:
        if pool_card.get_global_rect().intersects(dragged_card_rect):
            # 如果拖放到了公共牌池卡牌上
            # 检查是否有足够的行动点
            # if GameManager.action_points <= 0:
            #     tooltip_bubble.show_tooltip("行动点不足")
            #     break
                
            selected_pool_card = pool_card
            selected_hand_card = card_node
            
            # 执行交换逻辑 - 立即交换数据，不等待动画
            _try_swap_cards()
            
            # 如果交换失败，直接返回
            if selected_hand_card or selected_pool_card:
                card_node.position = card_node.original_position
                break
            
            # 获取动画需要的位置信息
            var hand_card_original_position = card_node.get_meta("hand_global_position")
            var pool_card_position = pool_card.global_position
            
            # 播放手牌A的落牌特效
            _play_card_landing_effect(pool_card)
            
            # 播放交换动画 - 仅作视觉效果，实际交换已完成
            if animation_manager:
                animation_manager.play_card_swap_animation(
                    card_node, 
                    pool_card, 
                    hand_card_original_position,  # 手牌原始位置
                    pool_card_position,  # 公共池牌位置
                    0.5,  # 默认持续时间
                    card_node.global_position  # 传递卡牌当前位置而不是鼠标位置
                )
            
            dropped_on_pool_card = true
            break
    
    # 检查是否拖到了弃牌区
    if not dropped_on_pool_card and discard_area.get_global_rect().intersects(dragged_card_rect):
        # 如果拖放到了弃牌区，在当前位置弃掉这张牌
        _discard_card(card_node.card_index, true)
        return
    
    # 检查是否拖到了其他手牌上
    var dropped_on_hand_card = false
    
    if not dropped_on_pool_card:
        # 如果目标索引与原始索引不同，说明卡牌已经被拖到了新位置
        if target_card_index != dragging_card_original_index:
            # 移动卡牌到目标位置
            _move_hand_card_to_position(card_node.card_index, target_card_index)
            dropped_on_hand_card = true
    
    # 如果没有拖放到有效位置，则重置卡牌位置
    if not dropped_on_pool_card and not dropped_on_hand_card:
        card_node.position = card_node.original_position
    
    # 更新手牌位置
    _update_hand_display()
    
    # 如果卡牌回到手牌区，在卡牌的最终位置触发粒子效果
    if not dropped_on_pool_card:
        # 延迟一帧调用，确保卡牌已经回到最终位置
        await get_tree().process_frame
        # 获取卡牌在手牌区的最终位置
        var target_index = min(target_card_index, hand_card_nodes.size() - 1)
        if target_index >= 0 and target_index < hand_card_nodes.size():
            _play_card_landing_effect(hand_card_nodes[target_index])

    # 重置拖动相关变量
    dragging_card_original_index = -1
    target_card_index = -1

# 手牌卡牌点击处理
func _on_hand_card_clicked(card_node):
    # 检查是否处于弃牌区的待删除状态
    if discard_area_manager and discard_area_manager.is_in_deletion_mode():
        _discard_card(card_node.card_index,true)
        return
    
    # 选中手牌卡牌
    selected_hand_card = card_node
    
    # 如果已经选中了公共牌池卡牌，尝试交换
    if selected_pool_card:
        var temp_pool_card = selected_pool_card  # 临时保存引用
        var temp_hand_card = selected_hand_card  # 临时保存引用
        
        # 立即执行交换逻辑
        _try_swap_cards()
        
        # 如果交换失败，直接返回
        if selected_hand_card or selected_pool_card:
            return
            
        # 获取动画需要的位置信息
        var hand_position = temp_hand_card.global_position
        var pool_position = temp_pool_card.global_position
        
        # 播放交换动画 - 仅作视觉效果，实际交换已完成
        if animation_manager:
            animation_manager.play_card_swap_animation(
                temp_hand_card, 
                temp_pool_card, 
                hand_position,  # 手牌位置
                pool_position,  # 公共池牌位置
                0.5  # 默认持续时间
            )

# 公共牌池卡牌点击处理
func _on_pool_card_clicked(card_node):
    # 选中公共牌池卡牌
    selected_pool_card = card_node
    
    # 如果已经选中了手牌卡牌，尝试交换
    if selected_hand_card:
        var temp_pool_card = selected_pool_card  # 临时保存引用
        var temp_hand_card = selected_hand_card  # 临时保存引用
        
        # 立即执行交换逻辑
        _try_swap_cards()
        
        # 如果交换失败，直接返回
        if selected_hand_card or selected_pool_card:
            return
            
        # 获取动画需要的位置信息
        var hand_position = temp_hand_card.global_position
        var pool_position = temp_pool_card.global_position
        
        # 播放交换动画 - 仅作视觉效果，实际交换已完成
        if animation_manager:
            animation_manager.play_card_swap_animation(
                temp_hand_card, 
                temp_pool_card, 
                hand_position,  # 手牌位置
                pool_position,  # 公共池牌位置
                0.5  # 默认持续时间
            )

# 结束阶按钮点击处理
func _on_end_round_button_pressed():
    # 获取游戏管理器和卡牌系统
    end_round_button.disabled = true
    gear_button.disabled = true
    # 设置阶结算处理标志
    is_end_round_processing = true
    
    # 播放按钮点击动画
    _play_button_click_animation()
    
    # 通知抽牌弃牌区管理器
    if deck_discard_manager:
        deck_discard_manager.set_end_round_processing(true)
    
    var game_manager = get_node("/root/GameManager")
    var card_system = get_node("/root/CardSystem")
    var buff_manager = get_node("/root/BuffManager")
    if not game_manager or not card_system or not buff_manager:
        return
    
    # 检查手牌是否超过hand_limit张
    if card_system.player_hand.size() > game_manager.hand_limit:
        # 显示提示：需要弃牌
        tooltip_bubble.show_tooltip("手牌不能超过 %s 张，请弃牌" % [game_manager.hand_limit])
        is_end_round_processing = false
        if deck_discard_manager:
            deck_discard_manager.set_end_round_processing(false)
        end_round_button.disabled = false
        gear_button.disabled = false
        _on_end_round_button_mouse_exited()
        return
    
    # 清除之前激活的所有BUFF，确保只有当前手牌前三张中的BUFF生效
    card_system.active_buff_cards.clear()
    buff_manager.active_buffs.clear()

    # 在这里应用BUFF效果到游戏机制，确保只应用一次
    buff_manager.apply_buffs_to_game_mechanics()
    
    # 重新评估手牌以获取最新积分
    var result = card_system.evaluate_hand(true) # 做了计算预处理，但只用了牌型结构的结果
    
    # 播放积分结算动画
    await AnimationManager.play_score_settlement_animation(hand_card_nodes,pool_card_nodes,score_value_label,score_coin,hand_container,buff_display,limit_label,score_display,action_points_display.action_points_label,scroll,result)
    # 最新总积分
    var total_current_score = game_manager.total_score
    
    # 判断是否达到积分限制
    if total_current_score >= game_manager.limit_score:
        # 保存第四张及之后的手牌节点
        var remaining_cards = []
        for i in range(3, hand_card_nodes.size()):
            remaining_cards.append(hand_card_nodes[i])
            
            # 检查前三张手牌中的BUFF卡牌，并播放飞向buff_display的动画
        for i in range(min(3, card_system.player_hand.size())):
            var card_data = card_system.player_hand[i]
            if card_data and card_data.has("buff") and card_data["buff"] and card_data["buff"]["rounds_left"] > 0:
                # 检查是否是持续性buff
                var buff_type = card_data["buff"]["type"]
                var persistent_buff_types = [buff_manager.BuffType.SAME_RANK_ADD, buff_manager.BuffType.SAME_TYPE_ADD, 
                                buff_manager.BuffType.SAME_RANK_MULTIPLY, buff_manager.BuffType.SAME_TYPE_MULTIPLY,
                                buff_manager.BuffType.ROUND_SCORE_ADD,
                                buff_manager.BuffType.TIGER, buff_manager.BuffType.RABBIT, buff_manager.BuffType.FROG,
                                buff_manager.BuffType.TURTLE, buff_manager.BuffType.GORILLA,
                                buff_manager.BuffType.DRAW_COST_REDUCE]
                if buff_type in persistent_buff_types:
                    # 获取手牌节点的当前位置作为起始位置
                    var start_position = hand_card_nodes[i].global_position
                    # 获取buff_display中最后一张卡牌的位置作为目标位置，如果没有卡牌则使用默认位置
                    var target_position
                    if buff_display.active_buff_nodes.size() > 0:
                        var last_card = buff_display.active_buff_nodes[buff_display.active_buff_nodes.size() - 1]
                        target_position = last_card.global_position
                    else:
                        # 如果没有卡牌，使用buff_display的默认位置
                        target_position = buff_display.global_position + Vector2(0, buff_display.custom_minimum_size.y / 2)
                    # 隐藏原卡牌
                    hand_card_nodes[i].visible = false
                    # 播放BUFF卡牌飞行动画
                    animation_manager.play_buff_card_fly_animation(card_data, start_position, target_position)
                
        # 1. 弃牌前三张手牌 
        card_system.discard_hand_front_cards()

        # 2. 弃牌公共牌区卡牌
        card_system.discard_common_pool_cards()
        
        # 创建一个临时数组存储所待弃牌节点
        var temp_pool_cards = pool_card_nodes.duplicate()
        var hand_front_cards = hand_card_nodes.slice(0,3)
        var temp_hand_front = hand_front_cards.duplicate()
        temp_pool_cards.append_array(temp_hand_front)
        
        # 获取抽牌区位置
        var deck_position = $RightSide/DeckDiscardArea.global_position + Vector2($RightSide/DeckDiscardArea.size.x/2, $RightSide/DeckDiscardArea.size.y/2)

        # 为每张卡牌创建飞向抽牌区的动画
        var fly_tween = create_tween()
        for card_node in temp_pool_cards:
            animation_manager._fly_to_deck_discard_area(fly_tween,card_node,deck_position)
        
        # 等待所有卡牌飞向牌堆的动画完成
        await fly_tween.finished

        # 弃牌区卡牌飞向抽牌区
        var discard_cards = CardSystem.discard_pile.duplicate()
        var discard_position = discard_area.global_position + Vector2(discard_area.size.x/4,15)
        
        # 为每张弃牌创建一个临时节点用于动画
        var temp_discard_nodes = []
        for card_data in discard_cards:
            var temp_card = CARD_NODE_SCENE.instantiate()
            discard_area.add_child(temp_card)
            temp_card.set_card_data(card_data, false, -1)
            temp_card.global_position = discard_position
            temp_card.show_behind_parent = true
            temp_card.scale = Vector2(0.6,0.6)
            temp_card.visible = false
            temp_discard_nodes.append(temp_card)
        
        # 创建所有卡牌的动画，每张卡片错开一小段时间开始
        var last_tween = null
        for i in range(temp_discard_nodes.size()):
            await get_tree().create_timer(0.1).timeout  # 减少间隔时间到0.05秒
            last_tween = animation_manager.discard_fly_to_deck_area(temp_discard_nodes[i], deck_position)
        
        # 等待最后一个动画完成
        if last_tween:
            await last_tween.finished
            await get_tree().create_timer(0.1).timeout
        
        # 清空弃牌堆
        CardSystem.discard_pile.clear()
            
        # 移除所有公共牌池节点和前三张手牌节点
        for card_node in temp_pool_cards:
            card_node.queue_free()
        pool_card_nodes.clear()
        
        # 3. 保留手牌区非计算积分的卡牌（第四张及以后）
        hand_card_nodes = remaining_cards
        
        # 确保所有保留的卡牌都可以拖拽
        for card_node in hand_card_nodes:
            card_node.can_drag = true
            card_node.set_draggable(true)
            card_node.mouse_filter = Control.MOUSE_FILTER_STOP
        
        # 计算手牌布局参数
        var start_x = base_card_width / 2
        
        # 为保留的卡牌创建移动动画
        for i in range(hand_card_nodes.size()):
            var card_node = hand_card_nodes[i]
            var target_position = hand_container.global_position + Vector2(
                start_x + i * (base_card_width + spacing),
                0
            )
            # 播放保留卡牌移动动画
            if animation_manager:
                await animation_manager.play_retained_card_move_animation(card_node, target_position)
        
        # 等待一小段时间，让保留卡牌的动画完全结束
        await get_tree().create_timer(0.3).timeout
        
        # 更新手牌显示
        _update_hand_display()
    
    # 结束当前阶
    game_manager.end_round(total_current_score)
    
    # 在函数结尾恢复抽牌功能和标志位
    # 由于阶结算可能有多个异步操作，使用延时器确保所有处理完成后才恢复状态
    get_tree().create_timer(1.0).timeout.connect(func():
        is_end_round_processing = false
        if deck_discard_manager:
            deck_discard_manager.set_end_round_processing(false)
        end_round_button.disabled = false
        gear_button.disabled = false
        _on_end_round_button_mouse_exited()
    )
    
    # 更新牌堆牌数显示
    _update_deck_count()
                
# 设置按钮点击处理
func _on_settings_button_pressed():
    # 显示设置面板
    settings_panel.show_panel()

# 退出按钮点击处理
func _on_exit_button_pressed():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        # 返回主菜单
        game_manager.return_to_main_menu()

# 交换手牌位置（保留此函数以兼容其他可能的调用）
func _swap_hand_cards(index1, index2):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 检查索引是否有效
    if index1 < 0 or index1 >= card_system.player_hand.size() or index2 < 0 or index2 >= card_system.player_hand.size():
        # 索引无效，不执行交换
        return
    
    # 交换卡牌数据
    var temp = card_system.player_hand[index1]
    card_system.player_hand[index1] = card_system.player_hand[index2]
    card_system.player_hand[index2] = temp
    
    # 检查手牌节点索引是否有效
    if index1 >= hand_card_nodes.size() or index2 >= hand_card_nodes.size():
        # 节点索引无效，不更新节点
        return
    
    # 更新卡牌节点数据
    hand_card_nodes[index1].set_card_data(card_system.player_hand[index1], true, index1)
    hand_card_nodes[index2].set_card_data(card_system.player_hand[index2], true, index2)
    
    # 评估手牌
    card_system.evaluate_hand()

# 移动手牌到指定位置
func _move_hand_card_to_position(from_index, to_index):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 检查索引是否有效
    if from_index < 0 or from_index >= card_system.player_hand.size() or to_index < 0 or to_index >= card_system.player_hand.size():
        # 索引无效，不执行移动
        return
    
    # 保存要移动的卡牌数据和节点
    var moving_card_data = card_system.player_hand[from_index]
    var moving_card_node = hand_card_nodes[from_index]
    
    # 创建临时数组来存储更新后的卡牌数据和节点
    var new_hand_data = []
    var new_hand_nodes = []
    
    # 填充新数组，同时调整卡牌顺序
    if from_index < to_index:
        for i in range(card_system.player_hand.size()):
            if i != from_index:
                # 添加其他卡牌（跳过原始位置）
                new_hand_data.append(card_system.player_hand[i])
                new_hand_nodes.append(hand_card_nodes[i])
            if i == to_index:
                # 在目标位置插入移动的卡牌
                new_hand_data.append(moving_card_data)
                new_hand_nodes.append(moving_card_node)
    else:
        for i in range(card_system.player_hand.size()):
            if i == to_index:
                # 在目标位置插入移动的卡牌
                new_hand_data.append(moving_card_data)
                new_hand_nodes.append(moving_card_node)
            if i != from_index:
                # 添加其他卡牌（跳过原始位置）
                new_hand_data.append(card_system.player_hand[i])
                new_hand_nodes.append(hand_card_nodes[i])     
    
    # 更新卡牌系统中的手牌数据
    card_system.player_hand = new_hand_data
    # 更新手牌节点数组
    hand_card_nodes = new_hand_nodes
    
    # 更新所有卡牌节点的索引和数据
    for i in range(hand_card_nodes.size()):
        hand_card_nodes[i].set_card_data(card_system.player_hand[i], true, i)
        # 检查卡牌是否应该被禁用（青蛙牌效果）
        _check_card_forbidden(card_system.player_hand[i], hand_card_nodes[i])
    
    # 播放卡牌移动音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/small-scratch.wav")
    
    # 评估手牌
    card_system.evaluate_hand()
    
    # 更新手牌显示
    # _update_hand_display()

func _update_hand_container_order():
    # 只获取有card_index属性的子节点
    var sortable_children = hand_container.get_children().filter(func(child): 
        return "card_index" in child
    )
    # 对这些节点进行排序
    sortable_children.sort_custom(func(a, b):
        return a.card_index < b.card_index
    )
    # 重新排列这些节点
    for child in sortable_children:
        hand_container.move_child(child, sortable_children.find(child))

# 处理每一帧的更新
func _process(delta):
    # 如果正在拖动卡牌，检查是否悬停在其他卡牌上并更新空位动画
    if is_dragging and selected_hand_card:
        _check_drag_hover()
        
        # 检查是否拖动到弃牌区
        if discard_area_manager:
            if discard_area_manager.get_global_rect().has_point(get_global_mouse_position()):
                if not discard_area_manager.mouse_is_in_area:
                    discard_area_manager.on_card_drag_over()
            elif discard_area_manager.mouse_is_in_area:
                discard_area_manager.on_card_drag_exit()
        
        # 持续更新卡牌位置，实现平滑的空位移动动画
        # 计算布局参数
        var container_width = hand_container.size.x
        var base_card_width = base_card_width
        var min_spacing = 10.0
        var max_spacing = 30.0
        var hover_scale = 1.15
        
        # 计算实际间距
        var actual_spacing = max_spacing
        var total_cards = hand_card_nodes.size() - 1
        var dragged_card_width = base_card_width * hover_scale
        var ideal_total_width = total_cards * base_card_width + dragged_card_width + total_cards * max_spacing
        
        if ideal_total_width > container_width:
            actual_spacing = min_spacing
            var min_spacing_total_width = total_cards * base_card_width + dragged_card_width + total_cards * min_spacing
            
            if min_spacing_total_width > container_width:
                var available_width = container_width - dragged_card_width
                var remaining_cards = total_cards
                var required_width_per_card = available_width / remaining_cards if remaining_cards > 0 else base_card_width
                required_width_per_card = max(required_width_per_card, 40.0)
                actual_spacing = -(base_card_width - required_width_per_card)
        
        # 计算起始位置
        var total_width = total_cards * base_card_width + dragged_card_width + total_cards * actual_spacing
        var start_x = (container_width - total_width) / 2
        
        # 更新卡牌位置
        _update_cards_positions_during_drag(actual_spacing, start_x, base_card_width, dragged_card_width)
    
    # 处理积分限制标签的动画
    _process_limit_label_animation(delta)

# 处理积分限制标签的动画
func _process_limit_label_animation(delta):
    # 如果有其他动画正在播放或者标签本身正在动画中，则不执行
    if is_other_animation_playing or is_limit_label_animating:
        return
    
    # 更新计时器
    limit_label_animation_timer += delta
    
    # 当计时器达到间隔时间，执行动画
    if limit_label_animation_timer >= limit_label_animation_interval:
        limit_label_animation_timer = 0.0  # 重置计时器
        _play_limit_label_animation()  # 播放动画

# 播放积分限制标签的Q弹跳动动画
func _play_limit_label_animation():
    # 如果已经在动画中或有其他动画正在播放，则不执行
    if is_limit_label_animating or is_other_animation_playing:
        return
    
    # 标记动画开始
    is_limit_label_animating = true
    
    # 使用动画管理器播放限制标签动画
    if animation_manager:
        # 连接动画完成信号
        animation_manager.animation_finished.connect(func(animation_type):
            if animation_type == animation_manager.AnimationType.LIMIT_PULSE:
                is_limit_label_animating = false
        , CONNECT_ONE_SHOT)
        
        # 播放动画 - 使用完整路径
        animation_manager.play_limit_label_animation(limit_label)

# 检查拖动悬停
func _check_drag_hover():
     # 清除之前的高亮状态
    if hover_card and hover_card != selected_hand_card:
        hover_card.highlight(false)
        hover_card = null
        # 隐藏行动点扣减提示
        action_points_display.hide_reduction_hint()
    # 保存旧的目标索引，用于检测变化
    var old_target_index = target_card_index
    
    # 默认目标索引为原始索引
    target_card_index = dragging_card_original_index
    
    # 获取拖动中的卡牌的全局矩形
    var dragged_card_rect = selected_hand_card.get_global_rect() if selected_hand_card else Rect2()
    
    # 检查是否悬停在在弃牌区
    if discard_area.get_global_rect().intersects(dragged_card_rect):
        # 更新放置位置指示器到原始位置，并启用警告效果
        if drop_indicator:
            # 启用警告效果
            if drop_indicator.material:
                drop_indicator.material.set_shader_parameter("is_discard_warning", true)
        return
    else:
        # 禁用警告效果
        if drop_indicator and drop_indicator.material:
            drop_indicator.material.set_shader_parameter("is_discard_warning", false)
    
    # 获取手牌布局参数
    var layout = _calculate_hand_layout_params()
    
    # 检查是否悬停在公共牌池卡牌上
    for pool_card in pool_card_nodes:
        if pool_card.get_global_rect().intersects(dragged_card_rect):
            # 使用金色闪动效果高亮显示目标卡牌
            pool_card.highlight(true, true)  # 第二个参数true表示使用金色效果
            hover_card = pool_card
            # 位置指示器还原到原始位置
            if drop_indicator:
                # 将指示器移动到原始位置
                drop_indicator.position = Vector2(layout.start_x + dragging_card_original_index * (layout.base_card_width + layout.actual_spacing), 0)
                drop_indicator.size.x = layout.base_card_width
                drop_indicator.visible = true
            return
    
    # 获取鼠标在手牌容器中的相对位置
    var mouse_pos = get_global_mouse_position() - hand_container.global_position
    
    # 检查拖动卡牌是否与手牌容器区域相交
    var is_card_in_hand_container = hand_container.get_global_rect().intersects(dragged_card_rect)
    
    if is_card_in_hand_container:
        # 根据鼠标位置计算目标索引，考虑拖拽卡牌的缩放
        for i in range(layout.total_cards + 2):  # +2 是为了处理最右侧位置和额外的插入位置
            var card_left = layout.start_x + i * (layout.base_card_width + layout.actual_spacing) - layout.actual_spacing / 2
            var card_width = layout.dragged_card_width if i == target_card_index else layout.base_card_width
            var card_right = card_left + card_width + layout.actual_spacing
            
            if mouse_pos.x >= card_left and mouse_pos.x < card_right:
                target_card_index = i
                if i > layout.total_cards:  # 如果是最后一个位置
                    target_card_index = layout.total_cards
                break
    else:
        # 如果鼠标不在手牌容器内，将目标索引设置为原始索引
        target_card_index = dragging_card_original_index
    
    # 更新放置位置指示器
    if animation_manager and drop_indicator:
        # 计算指示器位置，考虑拖拽卡牌的缩放
        var indicator_x = layout.start_x + target_card_index * (layout.base_card_width + layout.actual_spacing)
        var indicator_pos = Vector2(indicator_x, 0)
        
        # 更新指示器位置和大小，使用拖拽卡牌的实际宽度
        drop_indicator.position = indicator_pos
        drop_indicator.size.x = layout.dragged_card_width
        drop_indicator.visible = true
        
    # 更新其他卡牌的位置
    _update_cards_positions_during_drag(layout.actual_spacing, layout.start_x, layout.base_card_width, layout.dragged_card_width)

# 在拖动过程中更新卡牌位置
func _update_cards_positions_during_drag(actual_spacing: float, start_x: float, base_card_width: float, dragged_card_width: float):
    var current_index = 0
    
    for i in range(hand_card_nodes.size()):
        var card_node = hand_card_nodes[i]
        
        # 跳过正在拖动的卡牌
        if card_node == selected_hand_card:
            continue
            
        # 计算卡牌的新位置索引
        var new_pos_idx = current_index
        
        # 如果当前索引大于等于目标索引，需要为拖动的卡牌留出空间
        if current_index >= target_card_index:
            new_pos_idx += 1
            
        # 设置卡牌的目标位置，考虑拖拽卡牌的宽度
        var target_pos_x = start_x
        
        # 修正位置计算逻辑，确保右侧卡牌不会左移
        if new_pos_idx < target_card_index:
            # 目标位置前的卡牌，直接使用索引乘以固定间距
            target_pos_x += new_pos_idx * (base_card_width + actual_spacing)
        else:
            # 目标位置后的卡牌，考虑拖拽卡牌的宽度和所有先前卡牌的宽度
            # 首先计算目标位置前所有卡牌占用的空间
            var before_target_width = target_card_index * (base_card_width + actual_spacing)
            # 然后加上拖拽卡牌的宽度
            var after_dragged_pos = before_target_width + dragged_card_width + actual_spacing
            # 最后加上当前卡牌到目标位置之后的所有卡牌的宽度
            target_pos_x += after_dragged_pos + (new_pos_idx - target_card_index - 1) * (base_card_width + actual_spacing)
        
        # 使用 tween 平滑移动卡牌
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        tween.tween_property(card_node, "position:x", target_pos_x, 0.1)
        
        current_index += 1

# 更新公共牌池显示
func _update_pool_display():
    # 获取自适应布局参数
    var layout = _calculate_adaptive_layout()
    var card_width = layout.card_width
    var adaptive_spacing = layout.adaptive_spacing
    var min_spacing = layout.min_spacing
    
    # 使用自适应间距计算总宽度
    var total_width = pool_card_nodes.size() * card_width + (pool_card_nodes.size() - 1) * adaptive_spacing
    
    # 如果总宽度超过容器宽度，则调整间距以适应
    if total_width > pool_container.size.x and pool_card_nodes.size() > 1:
        adaptive_spacing = (pool_container.size.x - pool_card_nodes.size() * card_width) / (pool_card_nodes.size() - 1)
        adaptive_spacing = max(adaptive_spacing, min_spacing * 0.7)  # 确保间距不会太小
        total_width = pool_card_nodes.size() * card_width + (pool_card_nodes.size() - 1) * adaptive_spacing
    
    # 计算起始位置，使卡牌居中显示
    var start_x = (pool_container.size.x - total_width) / 2
    
    # 更新每张卡牌的位置和缩放
    for i in range(pool_card_nodes.size()):
        var card_node = pool_card_nodes[i]
        # 更新卡牌索引
        card_node.card_index = i
        # 设置卡牌位置
        card_node.position.x = start_x + i * (card_width + adaptive_spacing)
        card_node.position.y = 0
        # 设置卡牌缩放
        # card_node.scale = Vector2(0.9, 0.9)
        # 保存原始位置
        card_node.original_position = card_node.position
        # 确保公共牌池卡牌不可拖拽
        card_node.can_drag = false
        card_node.set_draggable(false)

# 弃牌处理
func _discard_card(card_index, at_current_position = false):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system or card_index < 0 or card_index >= card_system.player_hand.size():
        return
    
    # 如果手牌数量小于等于3，不允许弃牌
    if card_system.player_hand.size() <= 3:
        tooltip_bubble.show_tooltip("手牌不能少于3张")
        # 重置卡牌位置
        if card_index < hand_card_nodes.size():
            hand_card_nodes[card_index].position = hand_card_nodes[card_index].original_position
        return
    
    # 保存要弃掉的卡牌数据
    var discarded_card = card_system.player_hand[card_index]
    
    # 创建一个临时卡牌节点用于执行动画
    var temp_card_node = CARD_NODE_SCENE.instantiate()
    add_child(temp_card_node)
    
    # 设置临时卡牌的数据和位置
    temp_card_node.set_card_data(discarded_card, true, -1)
    
    # 如果是在当前位置溶解，使用当前拖动位置
    if at_current_position && card_index < hand_card_nodes.size():
        temp_card_node.global_position = hand_card_nodes[card_index].global_position
    else:
        # 否则使用原始位置
        if card_index < hand_card_nodes.size():
            temp_card_node.position = hand_card_nodes[card_index].original_position
    
    # 从手牌数据中移除卡牌
    card_system.player_hand.remove_at(card_index)
    
    # 将卡牌添加到弃牌堆
    card_system.discard_pile.append(discarded_card)
    
    # 从手牌节点列表中移除并释放节点
    if card_index < hand_card_nodes.size():
        var card_node = hand_card_nodes[card_index]
        hand_card_nodes.remove_at(card_index)
        card_node.queue_free()
    
    # 连接临时卡牌的溶解完成信号
    temp_card_node.dissolve_completed.connect(_on_temp_card_dissolve_completed.bind(temp_card_node))
    
    # 获取弃牌区中心位置
    var discard_position = discard_area_manager.get_center_position()
    
    # 创建旋涡动画
    var vortex_tween = create_tween()
    vortex_tween.set_parallel(true)  # 允许多个属性同时动画
    
    # 设置起始位置和旋转
    var start_pos = temp_card_node.global_position
    var start_scale = temp_card_node.scale
    
    # 计算到目标点的距离
    var distance = start_pos.distance_to(discard_position)
    var duration =  0.6 
    
    # 创建旋涡效果动画
    # 1. 位置动画 - 使用二次贝塞尔曲线创造弧线路径
    var control_point = start_pos.lerp(discard_position, 0.5) + Vector2(0, -50)  # 控制点稍微向上偏移
    
    var pos_curve = Curve2D.new()
    pos_curve.add_point(start_pos)
    pos_curve.add_point(discard_position, Vector2.ZERO, Vector2.ZERO, 0)  # 添加终点
    
    # 使用Tween的自定义插值来创建路径动画
    vortex_tween.tween_method(
        func(progress: float):
            var t = 1.0 - progress  # 反转进度以创造加速效果
            var pos = start_pos.lerp(discard_position, 1.0 - t * t)  # 使用二次方程实现加速
            temp_card_node.global_position = pos,
        0.0,
        1.0,
        duration
    ).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
    
    # 2. 旋转动画 - 随着接近中心点旋转速度加快
    vortex_tween.tween_property(temp_card_node, "rotation", PI * 4, duration).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
    
    # 3. 缩放动画 - 接近中心点时逐渐缩小
    vortex_tween.tween_property(temp_card_node, "scale", Vector2(0.1, 0.1), duration).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
    
    # 4. 透明度动画 - 最后阶段逐渐消失
    vortex_tween.tween_property(temp_card_node, "modulate:a", 0.0, duration * 0.5).set_delay(duration * 0.5)
    
    await vortex_tween.finished
    
    # 播放弃牌区的动画效果
    discard_area_manager.play_discard_complete_animation()
    
    AudioManager.play_sfx("res://assert/audio/sfx/whip.wav")
    
    # 禁用拖动
    temp_card_node.set_draggable(false)
    temp_card_node.queue_free()
    
    # 更新手牌显示
    _update_hand_display()
    
    # 评估手牌
    card_system.evaluate_hand()
    
    # 更新结束阶按钮状态
    _update_end_turn_button()

# 临时卡牌溶解完成后的回调函数
func _on_temp_card_dissolve_completed(temp_card_node):
    # 断开信号连接
    if temp_card_node.dissolve_completed.is_connected(_on_temp_card_dissolve_completed):
        temp_card_node.dissolve_completed.disconnect(_on_temp_card_dissolve_completed)
    
    # 释放临时卡牌节点
    temp_card_node.queue_free()
    
    # 更新结束阶按钮状态
    _update_end_turn_button()

# 更新牌堆牌数显示 - 使用管理器
func _update_deck_count():
    if deck_discard_manager:
        deck_discard_manager.update_deck_count()

# 处理超出手牌上限的情况 - 使用管理器
func _trigger_discard_excess_cards(new_limit):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    
    # 计算需要弃掉的卡牌数量
    var excess_cards = card_system.player_hand.size() - new_limit
    if excess_cards <= 0:
        return
    
    # 显示提示信息
    var message = "手牌超出上限，请弃掉 %d 张卡牌" % excess_cards
    show_message(message)
    
    # 高亮弃牌区
    if discard_area_manager:
        discard_area_manager.set_highlight(true)
        
        # 创建一个计时器，在一段时间后恢复弃牌区颜色
        var timer = Timer.new()
        timer.wait_time = 2.0  # 2秒后恢复
        timer.one_shot = true
        add_child(timer)
        timer.timeout.connect(func(): 
            discard_area_manager.set_highlight(false)
            timer.queue_free()
        )
        timer.start()

# 规则按钮点击处理
func _on_rule_button_pressed():
    # 显示规则面板
    rule_panel.show_panel()
    
# 显示消息提示
func show_message(message: String):
    # 使用提示气泡显示消息
    if tooltip_bubble:
        tooltip_bubble.show_tooltip(message)
    else:
        # 如果没有提示气泡，则使用打印输出
        print(message)

# 播放卡牌落地粒子特效
func _play_card_landing_effect(card_node):
    # 使用动画管理器播放卡牌落地效果
    if animation_manager:
        animation_manager.play_card_landing_effect(card_node)

# 尝试交换卡牌
func _try_swap_cards():
    # 获取游戏管理器和卡牌系统
    var game_manager = get_node("/root/GameManager")
    var card_system = get_node("/root/CardSystem")
    
    if not game_manager or not card_system:
        return
    
    # 检查是否有足够的行动点
    # if game_manager.action_points <= 0:
    #     tooltip_bubble.show_tooltip("行动点不足")
    #     return
    
    # 检查选中的卡牌是否有效
    if not selected_hand_card or not selected_pool_card:
        return
    
    # 获取卡牌索引
    var hand_index = selected_hand_card.card_index
    var pool_index = selected_pool_card.card_index
    
    # 尝试交换卡牌
    if card_system.swap_cards(hand_index, pool_index):
        # 交换成功，扣除行动点
        # game_manager.consume_action_points(1)
        
        # 播放卡牌交换音效
        # var audio_manager = get_node_or_null("/root/AudioManager")
        # if audio_manager:
        #     audio_manager.play_sfx("res://assert/audio/sfx/small-scratch.wav")
        
        # 清除选中状态
        selected_hand_card = null
        selected_pool_card = null

# 清空卡牌
func _clear_cards():
    # 清空手牌
    for card_node in hand_card_nodes:
        card_node.queue_free()
    hand_card_nodes.clear()
    
    # 清空公共牌池
    for card_node in pool_card_nodes:
        card_node.queue_free()
    pool_card_nodes.clear()
    
    # 重置分数显示
    # score_display.text = "积分: 0"

# 发放初始卡牌
func _deal_initial_cards():
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
    deck_discard_manager.is_draw_card_processing = true
    # 重置卡牌
    card_system.reset_cards()
    
    # 获取抽牌区位置作为起始点
    var start_position = deck_discard_image.global_position
    
    # 定义共用的布局参数
    var base_card_width = base_card_width
    var spacing = 30.0
    
    # 清空现有的卡牌节点
    for card in hand_card_nodes:
        card.queue_free()
    hand_card_nodes.clear()
    
    for card in pool_card_nodes:
        card.queue_free()
    pool_card_nodes.clear()
    
    # 暂时断开信号连接，防止自动创建卡牌
    if card_system.card_added_to_hand.is_connected(_on_card_added_to_hand):
        card_system.card_added_to_hand.disconnect(_on_card_added_to_hand)
    if card_system.card_added_to_pool.is_connected(_on_card_added_to_pool):
        card_system.card_added_to_pool.disconnect(_on_card_added_to_pool)
    
    # 如果是第一阶，创建初始手牌
    if card_system.player_hand.size() == 0:
        # 计算手牌布局参数
        var hand_container_width = hand_container.size.x
        var total_width = 3 * base_card_width + 2 * spacing
        var start_x = (hand_container_width - total_width) / 2
        
        # 发放初始手牌（3张）
        card_system.deal_to_player(3)
        
        # 为每张手牌创建动画
        for i in range(3):
            # 等待一小段时间，创造顺序感
            await get_tree().create_timer(0.1).timeout  # 从0.1减少到0.05秒
            
            # 创建卡牌节点
            var card_node = CARD_NODE_SCENE.instantiate()
            hand_container.add_child(card_node)
            
            # 设置卡牌数据
            card_node.set_card_data(card_system.player_hand[i], true, i)
            
            # 连接信号
            card_node.card_drag_started.connect(_on_hand_card_drag_started)
            card_node.card_drag_ended.connect(_on_hand_card_drag_ended)
            card_node.card_clicked.connect(_on_hand_card_clicked)
            card_node.card_right_clicked.connect(_on_card_right_clicked)
            
            # 设置初始位置为抽牌区
            card_node.global_position = start_position
            
            # 计算最终位置（在手牌区）
            var target_position = hand_container.global_position + Vector2(
                start_x + i * (base_card_width + spacing),
                0
            )
            
            # 播放抽牌动画
            if animation_manager:
                animation_manager.play_draw_card_animation(card_node, start_position, target_position)
                # 不等待动画完成，直接开始下一张卡牌的动画
                get_tree().create_timer(0.15).timeout.connect(  # 在动画进行到一定程度后播放落地效果
                    func(): _play_card_landing_effect(card_node)
                )
            
            # 添加到手牌节点列表
            hand_card_nodes.append(card_node)
            
            # 注册手牌区域
            cursor_manager.register_area(card_node, cursor_manager.CursorArea.HAND_AREA)
    else:
        # 如果不是第一阶，确保现有手牌都有对应的UI节点
        for i in range(card_system.player_hand.size()):
            if i >= hand_card_nodes.size():
                var card_node = CARD_NODE_SCENE.instantiate()
                hand_container.add_child(card_node)
                card_node.set_card_data(card_system.player_hand[i], true, i)
                card_node.card_drag_started.connect(_on_hand_card_drag_started)
                card_node.card_drag_ended.connect(_on_hand_card_drag_ended)
                card_node.card_clicked.connect(_on_hand_card_clicked)
                card_node.card_right_clicked.connect(_on_card_right_clicked)
                card_node.global_position = hand_container.global_position + Vector2(
                    base_card_width / 2 + i * (base_card_width + spacing),
                    0
                )
                hand_card_nodes.append(card_node)
                cursor_manager.register_area(card_node, cursor_manager.CursorArea.HAND_AREA)
            else:
                # 确保现有手牌节点可以拖拽
                hand_card_nodes[i].can_drag = true
                hand_card_nodes[i].set_draggable(true)
        
        # 如果手牌少于3张，补充到3张
        var cards_needed = 3 - card_system.player_hand.size()
        if cards_needed > 0:
            # 暂时断开信号连接，防止自动创建卡牌
            if card_system.card_added_to_hand.is_connected(_on_card_added_to_hand):
                card_system.card_added_to_hand.disconnect(_on_card_added_to_hand)
            
            # 发放补充手牌
            card_system.deal_to_player(cards_needed)
            
            # 为每张补充的手牌创建动画
            for i in range(card_system.player_hand.size() - cards_needed, card_system.player_hand.size()):
                # 等待一小段时间，创造顺序感
                await get_tree().create_timer(0.1).timeout
                
                # 创建卡牌节点
                var card_node = CARD_NODE_SCENE.instantiate()
                hand_container.add_child(card_node)
                
                # 设置卡牌数据
                card_node.set_card_data(card_system.player_hand[i], true, i)
                
                # 连接信号
                card_node.card_drag_started.connect(_on_hand_card_drag_started)
                card_node.card_drag_ended.connect(_on_hand_card_drag_ended)
                card_node.card_clicked.connect(_on_hand_card_clicked)
                card_node.card_right_clicked.connect(_on_card_right_clicked)
                
                # 设置初始位置为抽牌区
                card_node.global_position = deck_discard_image.global_position
                
                # 计算最终位置（在手牌区）
                var target_position = hand_container.global_position + Vector2(
                    base_card_width / 2 + i * (base_card_width + spacing),
                    0
                )
                
                # 播放抽牌动画
                if animation_manager:
                    await animation_manager.play_draw_card_animation(card_node, deck_discard_image.global_position, target_position)
                    # 在动画进行到一定程度后播放落地效果
                    _play_card_landing_effect(card_node)
                
                # 添加到手牌节点列表
                hand_card_nodes.append(card_node)
                
                # 注册手牌区域
                cursor_manager.register_area(card_node, cursor_manager.CursorArea.HAND_AREA)
            
            # 重新连接信号
            card_system.card_added_to_hand.connect(_on_card_added_to_hand)
    
    # 计算公共牌池布局参数
    var pool_container_width = pool_container.size.x
    var total_pool_width = 3 * base_card_width + 2 * spacing
    var pool_start_x = (pool_container_width - total_pool_width) / 2
    
    # 发放公共牌池卡牌（3张）
    card_system.deal_to_common_pool(3)
    
    # 为每张公共牌池卡牌创建动画
    for i in range(3):
        # 等待一小段时间，创造顺序感
        await get_tree().create_timer(0.1).timeout  # 从0.1减少到0.05秒
        
        # 创建卡牌节点
        var card_node = CARD_NODE_SCENE.instantiate()
        pool_container.add_child(card_node)
        
        # 设置卡牌数据
        card_node.set_card_data(card_system.common_pool[i], false, i)
        
        # 连接信号
        card_node.card_clicked.connect(_on_pool_card_clicked)
        card_node.card_right_clicked.connect(_on_card_right_clicked)
        card_node.can_drag = false
        card_node.set_draggable(false)
        
        # 设置初始位置为抽牌区
        card_node.global_position = start_position
        
        # 计算最终位置（在公共牌池区）
        var target_position = pool_container.global_position + Vector2(
            pool_start_x + i * (base_card_width + spacing), 
            0
        )
        
        # 播放抽牌动画
        if animation_manager:
            animation_manager.play_draw_card_animation(card_node, start_position, target_position)
            # 不等待动画完成，直接开始下一张卡牌的动画
            get_tree().create_timer(0.15).timeout.connect(  # 在动画进行到一定程度后播放落地效果
                func(): _play_card_landing_effect(card_node)
            )
        
        # 添加到公共牌池节点列表
        pool_card_nodes.append(card_node)
    
    # 等待所有动画完成
    await get_tree().create_timer(0.4).timeout  # 给最后一张卡牌足够的时间完成动画
    
    # 重新连接信号
    card_system.card_added_to_hand.connect(_on_card_added_to_hand)
    card_system.card_added_to_pool.connect(_on_card_added_to_pool)
    
    # 更新手牌和公共牌池显示
    _update_hand_display()
    _update_pool_display()

    deck_discard_manager.is_draw_card_processing = false
    
    # 评估手牌
    card_system.evaluate_hand()
    
    # 更新牌堆牌数显示
    _update_deck_count()

    # 标记禁用的卡牌
    _mark_forbidden_cards()

# 更新手牌显示
func _update_hand_display():
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
        
    # 更新手牌容器顺序
    _update_hand_container_order()
    # 获取容器的可用宽度
    var container_width = hand_container.size.x
    
    # 基础卡牌尺寸和间距
    var base_card_width = base_card_width  # 卡牌基础宽度
    var min_spacing = 10.0       # 最小间距
    var max_spacing = 30.0       # 最大间距
    var min_visible_width = 40.0 # 最小可见宽度（当卡牌重叠时）
    
    # 计算理想情况下需要的总宽度（最大间距）
    var ideal_total_width = card_system.player_hand.size() * base_card_width + (card_system.player_hand.size() - 1) * max_spacing
    
    # 计算实际使用的间距
    var actual_spacing = max_spacing
    var overlap = 0.0
    
    # 如果理想宽度超过容器宽度，需要调整布局
    if ideal_total_width > container_width:
        # 首先尝试减小间距到最小值
        actual_spacing = min_spacing
        var min_spacing_total_width = card_system.player_hand.size() * base_card_width + (card_system.player_hand.size() - 1) * min_spacing
        
        # 如果减小间距后仍然超出容器宽度，则需要重叠显示
        if min_spacing_total_width > container_width:
            # 计算需要的重叠量
            var available_width = container_width - base_card_width  # 保证最后一张卡完全显示
            var remaining_cards = card_system.player_hand.size() - 1  # 除最后一张外的卡牌数
            var required_width_per_card = available_width / remaining_cards if remaining_cards > 0 else base_card_width
            
            # 确保每张卡至少显示最小可见宽度
            required_width_per_card = max(required_width_per_card, min_visible_width)
            overlap = base_card_width - required_width_per_card
            actual_spacing = -overlap  # 间距变为负值，表示重叠
    
    # 计算起始位置，使卡牌居中显示
    var total_width = card_system.player_hand.size() * base_card_width + (card_system.player_hand.size() - 1) * actual_spacing
    var start_x = (container_width - total_width) / 2
    
    # 更新每张卡牌的位置
    for i in range(hand_card_nodes.size()):
        var card_node = hand_card_nodes[i]
        
        # 更新卡牌索引
        card_node.card_index = i
        
        # 设置卡牌位置
        card_node.position.x = start_x + i * (base_card_width + actual_spacing)
        card_node.position.y = 0
        
        # 设置卡牌的Z索引，使后面的卡牌显示在前面的上层
        card_node.z_index = i
        
        # 保存原始位置
        card_node.original_position = card_node.position
        
        # 确保所有手牌可以拖拽
        card_node.can_drag = true
        card_node.set_draggable(true)
        
        # 设置前三张卡牌的特殊效果（轻微放大）
        if i < 3:
            card_node.set_as_front_card(true)
            card_node.material.set_shader_parameter("enable_shine_effect", true)
        else:
            card_node.set_as_front_card(false)
            card_node.material.set_shader_parameter("enable_shine_effect", false)
        
        # 当卡牌重叠时，添加鼠标悬停效果
        if overlap > 0:
            _setup_card_hover_effect(card_node)

# 设置卡牌悬停效果
func _setup_card_hover_effect(card_node):
    # 连接鼠标进入/离开信号
    if not card_node.mouse_entered.is_connected(_on_card_mouse_entered):
        card_node.mouse_entered.connect(_on_card_mouse_entered.bind(card_node))
    if not card_node.mouse_exited.is_connected(_on_card_mouse_exited):
        card_node.mouse_exited.connect(_on_card_mouse_exited.bind(card_node))

# 鼠标进入卡牌
func _on_card_mouse_entered(card_node):
    if not is_dragging:  # 只在非拖动状态下显示悬停效果
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        # 将卡牌稍微抬起并放大
        tween.tween_property(card_node, "position:y", -20.0, 0.2)
        tween.parallel().tween_property(card_node, "scale", Vector2(1.1, 1.1), 0.2)
        # 确保悬停的卡牌显示在最上层
        card_node.z_index = 1000

# 鼠标离开卡牌
func _on_card_mouse_exited(card_node):
    if not is_dragging:  # 只在非拖动状态下恢复原状
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        # 恢复卡牌原始位置和大小
        tween.tween_property(card_node, "position:y", card_node.original_position.y, 0.2)
        tween.parallel().tween_property(card_node, "scale", Vector2(1.0, 1.0), 0.2)
        # 恢复原始Z索引
        card_node.z_index = card_node.card_index

# 手牌卡牌拖动结束
func _on_hand_card_drag_ended(card_node, drop_position):
    if not discard_area_manager.is_pending_deletion:
        discard_area.get_node_or_null("DiscardImage").texture = load("res://assert/right/close.png")
    action_points_display.hide_reduction_hint()
    if not is_dragging:
        return
    
    is_dragging = false
    
    # 发送拖拽结束信号给光标管理器
    emit_signal("card_drag_ended", card_node)
    
    # 清除所有卡牌的高亮状态
    for card in hand_card_nodes:
        card.highlight(false)
    
    # 清除所有公共牌池卡牌的高亮状态（包括金色闪动效果）
    for pool_card in pool_card_nodes:
        pool_card.highlight(false)
    
    # 隐藏放置位置指示器
    if animation_manager and drop_indicator:
        animation_manager.hide_drop_indicator(drop_indicator)
    
    # 检查是否拖到了弃牌区
    if discard_area.get_global_rect().has_point(drop_position):
        # 如果拖放到了弃牌区，在当前位置弃掉这张牌
        _discard_card(card_node.card_index, true)
        return
    
    _on_card_drag_ended(card_node, drop_position)

# 处理卡牌右键点击
func _on_card_right_clicked(card_node):
    # 如果处于弃牌区的待删除状态，取消该状态
    if discard_area_manager and discard_area_manager.is_in_deletion_mode():
        discard_area_manager.process_card_deletion(-1)  # 传入-1表示不执行具体卡牌删除
        return
        
    # 获取卡牌数据
    var card_data = card_node.card_data
    if card_data:
        # 显示卡牌详情面板
        card_detail_panel.show_card_detail(card_data)

# 新增处理 BUFF 显示区域卡牌右键点击的函数
func _on_buff_display_card_right_clicked_handler(card_data):
    if card_data:
        # 显示卡牌详情面板
        card_detail_panel.show_card_detail(card_data)

# 退出时清理资源
func _exit_tree():
    # 发送场景退出信号
    scene_exiting.emit()

# 设置背景音乐
func _setup_background_music():
    AudioManager.play_gameplay_music()

# 添加显示改变的处理函数
func _on_display_changed():
    # 更新手牌和公共牌池的显示
    _update_hand_display()
    _update_pool_display()

# 获取当前活跃的青蛙牌禁用花色集合
func _get_active_frog_forbidden_suit():
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        return []
    
    var forbidden_suits = []
    
    # BUFF区域的BUFF
    for buff in buff_manager.buff_area:
        if buff.type == buff_manager.BuffType.FROG and "additional_data" in buff and "forbidden_suit" in buff.additional_data:
            var suit = buff.additional_data.forbidden_suit
            if not forbidden_suits.has(suit):
                forbidden_suits.append(suit)
    
    return forbidden_suits

# 检查这张新卡是否应该被禁用（青蛙牌效果）
func _check_card_forbidden(card_data, card_node):
    var buff_manager = get_node("/root/BuffManager")
    var card_system = get_node("/root/CardSystem")
    if not buff_manager or not card_system:
        return
    
    # 获取当前活跃的青蛙牌禁用花色集合
    var forbidden_suits = _get_active_frog_forbidden_suit()
    
    # 如果有禁用花色，检查卡牌是否匹配任一禁用花色
    if forbidden_suits.has(card_data.suit):
        card_node.set_disabled(true)
        # 同时更新card_data中的标记
        card_data["is_disabled"] = true
    else:
        card_node.set_disabled(false)
        # 更新card_data中的标记
        if "is_disabled" in card_data:
            card_data.erase("is_disabled")

# 计算自适应分辨率参数
func _calculate_adaptive_layout():
    # 获取当前视口大小
    var viewport_size = get_viewport_rect().size
    
    # 计算基于视口大小的缩放因子
    var base_viewport_width = 1152.0  # 基准视口宽度
    var base_viewport_height = 648.0  # 基准视口高度
    
    var scale_factor_width = viewport_size.x / base_viewport_width
    var scale_factor_height = viewport_size.y / base_viewport_height
    
    # 使用较小的缩放因子，确保卡牌在任何方向都不会超出屏幕
    var scale_factor = min(scale_factor_width, scale_factor_height)
    
    # 卡牌基础尺寸和间距
    var base_card_w = base_card_width  # 卡牌基础宽度
    var base_card_h = base_card_heigth # 卡牌基础高度
    var base_spc = 30.0      # 卡牌基础间距
    
    # 应用缩放因子到卡牌尺寸和间距
    var current_card_width = base_card_w * scale_factor
    var current_card_height = base_card_h * scale_factor # Added card_height
    var current_min_spacing = base_spc * scale_factor  
    var current_adaptive_spacing = base_spc * scale_factor
    
    # 返回计算结果
    return {
        "scale_factor": scale_factor,
        "card_width": current_card_width,
        "card_height": current_card_height, # Added
        "min_spacing": current_min_spacing,
        "adaptive_spacing": current_adaptive_spacing,
        "base_card_width": base_card_w, # Added
        "base_card_height": base_card_h, # Added
        "base_spacing": base_spc
    }


# 更新结束阶按钮状态
func _update_end_turn_button():
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if card_system:
        # 只有当手牌大于等于3张时，结束阶按钮才可用
        end_round_button.disabled = card_system.player_hand.size() < 3
        
        # 如果按钮被禁用，则降低其透明度
        if end_round_button.disabled:
            end_round_button.modulate = Color(1.0, 1.0, 1.0, 0.5)
        else:
            end_round_button.modulate = Color(1.0, 1.0, 1.0, 1.0)

# 添加设置抽牌弃牌区管理器的方法
func _setup_deck_discard_manager():
    # 创建管理器实例
    deck_discard_manager = load("res://script/deck_discard_area_manager.gd").new()
    add_child(deck_discard_manager)
    
    # 初始化管理器
    deck_discard_manager.initialize(
        deck_discard_area,
        null, # 这里传None，因为没有Label了
        deck_count_label,
        tooltip_bubble,
        self,
        action_points_display  # 添加行动点显示的引用
    )
    
    # 连接抽牌信号
    deck_discard_manager.card_drawn.connect(_on_card_drawn)

# 添加抽牌信号处理函数
func _on_card_drawn():
    # 更新牌堆显示
    _update_deck_count()
    
    # 更新结束阶按钮状态
    _update_end_turn_button()

# 开始按钮心跳动画
func _start_button_heartbeat_animation():
    # 创建心跳动画定时器
    var timer = Timer.new()
    timer.name = "HeartbeatTimer"
    add_child(timer)
    timer.wait_time = 5.0  # 每5秒跳动一次
    timer.timeout.connect(_play_button_heartbeat)
    timer.autostart = true
    timer.start()
    
    # 初始随机时间偏移，使得shader动画不同步
    end_round_button.material.set_shader_parameter("time_offset", randf() * 10.0)

# 播放按钮心跳动画
func _play_button_heartbeat():
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    # 使用动画管理器播放心跳动画
    animation_manager.play_button_heartbeat(end_round_button, button_shadow, end_round_button.disabled, is_end_round_processing, is_button_hovered)

# 按钮鼠标进入处理
func _on_end_round_button_mouse_entered():
    if end_round_button.disabled:
        return
        
    # 设置悬停状态标记
    is_button_hovered = true
    
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    
    # 鼠标悬停效果 - 轻微放大和加速闪烁
    if not is_end_round_processing:
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        tween.tween_property(end_round_button, "scale", Vector2(1.1, 1.1), 0.2)
        if button_shadow:
            tween.parallel().tween_property(button_shadow, "scale", Vector2(1.1, 1.1), 0.2)
        
        # 增加闪烁速度和强度，使闪烁更明显
        end_round_button.material.set_shader_parameter("pulse_speed", 4.0)
        end_round_button.material.set_shader_parameter("pulse_intensity", 0.8)
        
        # 改变鼠标指针
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)
        
        # 播放鼠标悬停音效
        if AudioManager:
            AudioManager.play_sfx("res://assert/audio/sfx/coins.wav")

# 按钮鼠标离开处理
func _on_end_round_button_mouse_exited():
    if end_round_button.disabled:
        return
        
    # 重置悬停状态标记
    is_button_hovered = false
    
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    
    # 恢复正常大小和闪烁速度
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_property(end_round_button, "scale", Vector2(1.0, 1.0), 0.2)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 恢复正常闪烁
    end_round_button.material.set_shader_parameter("pulse_speed", 1.5)
    end_round_button.material.set_shader_parameter("pulse_intensity", 0.3)
    
    # 恢复正常鼠标指针
    cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

# 按钮按下处理
func _on_end_round_button_down():
    if end_round_button.disabled:
        return
        
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    
    # 按钮按下效果 - 压缩效果
    if not is_end_round_processing:
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        tween.tween_property(end_round_button, "scale", Vector2(0.9, 0.9), 0.1)
        if button_shadow:
            tween.parallel().tween_property(button_shadow, "scale", Vector2(0.9, 0.9), 0.1)

        AudioManager.play_sfx("res://assert/audio/sfx/saber.wav")

# 按钮释放处理
func _on_end_round_button_up():
    if end_round_button.disabled:
        return
        
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    
    # 按钮释放效果 - 恢复大小
    if not is_end_round_processing:
        var tween = create_tween()
        tween.set_ease(Tween.EASE_OUT)
        tween.set_trans(Tween.TRANS_CUBIC)
        
        if end_round_button.is_hovered():
            # 如果鼠标仍然悬停在按钮上，回到悬停大小
            tween.tween_property(end_round_button, "scale", Vector2(1.1, 1.1), 0.2)
            if button_shadow:
                tween.parallel().tween_property(button_shadow, "scale", Vector2(1.1, 1.1), 0.2)
        else:
            # 否则回到正常大小
            tween.tween_property(end_round_button, "scale", Vector2(1.0, 1.0), 0.2)
            if button_shadow:
                tween.parallel().tween_property(button_shadow, "scale", Vector2(1.0, 1.0), 0.2)

# 播放按钮点击动画
func _play_button_click_animation():
    # 获取阴影节点
    var button_shadow = end_round_button.get_parent().get_node_or_null("Buttonshadow")
    
    # 创建Q弹果冻效果
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 先压缩
    tween.tween_property(end_round_button, "scale", Vector2(0.8, 0.8), 0.1)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "scale", Vector2(0.8, 0.8), 0.1)
    
    # 再弹出
    tween.tween_property(end_round_button, "scale", Vector2(1.2, 1.2), 0.3)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "scale", Vector2(1.2, 1.2), 0.3)
    
    # 最后恢复
    tween.tween_property(end_round_button, "scale", Vector2(1.0, 1.0), 0.2)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 添加发光效果
    var glow_tween = create_tween()
    glow_tween.set_ease(Tween.EASE_OUT)
    glow_tween.set_trans(Tween.TRANS_CUBIC)
    glow_tween.tween_property(end_round_button.material, "shader_parameter/glow_intensity", 1.0, 0.2)
    glow_tween.tween_property(end_round_button.material, "shader_parameter/glow_intensity", 0.0, 0.8)
    
    # 播放粒子特效
    if firework_particles:
        firework_particles.restart()
        firework_particles.emitting = true
# 添加设置弃牌区管理器的方法
func _setup_discard_area_manager():
    # 创建管理器实例
    discard_area_manager = load("res://script/discard_area_manager.gd").new()
    add_child(discard_area_manager)
    
    # 初始化管理器
    discard_area_manager.initialize(
        discard_area,  # 弃牌区节点
        tooltip_bubble,  # 提示气泡
        cursor_manager,  # 光标管理器
        AudioManager  # 音频管理器
    )

# 齿轮按钮输入处理
func _on_gear_button_input(event):
    if event is InputEventMouseButton:
        if event.button_index == MOUSE_BUTTON_LEFT:
            if event.pressed:
                _on_gear_button_down()
            else:
                _on_gear_button_up()
                # 只有在按下后释放才触发点击事件
                if gear_button.get_global_rect().has_point(get_global_mouse_position()):
                    _on_gear_button_pressed()

# 齿轮按钮点击处理
func _on_gear_button_pressed():
    AudioManager.play_button_click_sfx()
    if settings_menu_panel:
        settings_menu_panel.show_panel()

# 齿轮按钮动画处理
func _on_gear_button_mouse_entered():
    if gear_button.disabled:
        return
    
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop.mp3")
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(gear_button, "scale", Vector2(1.2, 1.2), 0.2)
    
    # 改变鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)

func _on_gear_button_mouse_exited():
    if gear_button.disabled:
        return
    
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(gear_button, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 恢复鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

func _on_gear_button_down():
    if gear_button.disabled:
        return
    
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(gear_button, "scale", Vector2(0.9, 0.9), 0.1)

func _on_gear_button_up():
    if gear_button.disabled:
        return
    
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(gear_button, "scale", Vector2(1.0, 1.0), 0.2)
    

# 设置区域提示
#func _setup_area_tooltips():
    #var tooltip_instance = hover_tooltip.instantiate()
    #add_child(tooltip_instance)
    #
    #var tooltips = {
        #buff_tooltip_area: "已生效附加效果卡牌",
        #common_tooltip_label: "卡牌不可拖动，但可被交换",
        #hand_tooltip_label: "卡牌可拖动，每阶结算前三张",
        #push_tooltip_label: "消耗行动点，新增手牌/替换公共牌",
        #pull_tooltip_label: "卡牌拖动到此处可弃牌"
    #}
    #
    #for area in tooltips:
        #if area:
            #area.mouse_filter = Control.MOUSE_FILTER_STOP
            #area.mouse_entered.connect(func():
                #tooltip_instance.update_tooltip(tooltips[area])
                #tooltip_instance.show_tooltip()
                #var question = area.get_parent().get_node_or_null("TextureRect")
                #question.modulate = Color(1.0, 1.0, 1.0, 1.0)
            #)
            #area.mouse_exited.connect(func():
                #tooltip_instance.hide_tooltip()
                #var question = area.get_parent().get_node_or_null("TextureRect")
                #question.modulate = Color(1.0, 1.0, 1.0, 0.58)
            #)

# 开始羽毛漂浮动画
func _start_feather_floating_animation():
    if not change_chip or not animation_manager:
        return
        
    animation_manager.play_feather_float_animation(change_chip)

# 鼠标进入羽毛区域
func _on_change_chip_mouse_entered():
    if not change_chip or not animation_manager:
        return
        
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    # 使用动画管理器播放悬停动画
    animation_manager.play_feather_hover_animation(change_chip, true)
    
    # 改变鼠标样式
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)

# 鼠标离开羽毛区域
func _on_change_chip_mouse_exited():
    if not change_chip or not animation_manager:
        return
        
    # 使用动画管理器播放离开动画
    animation_manager.play_feather_hover_animation(change_chip, false)
    
    # 恢复鼠标样式
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

# 处理羽毛的点击事件
func _on_change_chip_gui_input(event):
    if not change_chip or not change_chip_shadow or not change_chip_texture or not animation_manager:
        return
        
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        _on_change_magic()

func _on_change_magic():
    # 切换羽毛图案
    is_magic = !is_magic
    if magic_particles:
        magic_particles.emitting = is_magic
    var feather01_pic = "res://assert/items/feather01.png"
    var feather02_pic = "res://assert/items/feather02.png"
    var texture_path = feather01_pic if is_magic else feather02_pic
    
    # 使用动画管理器播放燃烧切换动画
    animation_manager.play_feather_burn_animation(change_chip, change_chip_shadow, change_chip_texture, texture_path)
    if is_magic:
        change_chip_shader.visible = true
        change_chip_shader2.visible = true
        change_draw_card_shader.visible = true
        add_pic.visible = false
        change_pic.visible = true
    else:
        change_chip_shader.visible = false
        change_chip_shader2.visible = false
        change_draw_card_shader.visible = false
        add_pic.visible = true
        change_pic.visible = false

# 添加输入处理
func _unhandled_key_input(event):
    if event.is_pressed() and event.keycode == KEY_QUOTELEFT and OS.is_debug_build():  # 检测 ~ 键
        if debug_panel:
            debug_panel.toggle()

# 卷轴鼠标进入
func _on_scroll_mouse_entered():
    if scroll_hover_tween:
        scroll_hover_tween.kill()
    
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    # 使用动画管理器播放悬停动画
    scroll_hover_tween = animation_manager.play_scroll_hover_animation(scroll, true)
    
    # 改变鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.HOVER)

# 卷轴鼠标离开
func _on_scroll_mouse_exited():
    if scroll_hover_tween:
        scroll_hover_tween.kill()
    
    # 使用动画管理器播放离开动画
    scroll_hover_tween = animation_manager.play_scroll_hover_animation(scroll, false)
    
    # 恢复鼠标指针
    if cursor_manager:
        cursor_manager.set_cursor_state(cursor_manager.CursorState.NORMAL)

# 卷轴点击
func _on_scroll_pressed():
    if scroll_hover_tween:
        scroll_hover_tween.kill()
    
    # 使用动画管理器播放点击动画
    scroll_hover_tween = animation_manager.play_scroll_click_animation(scroll)
    
    # 显示牌型规则面板
    card_rules_panel.show_panel()

# 开始浮动动画
func _start_scroll_float_animation():
    if scroll_float_tween:
        scroll_float_tween.kill()

    # 使用动画管理器播放浮动动画
    scroll_float_tween = animation_manager.play_scroll_float_animation(scroll)

# 重新启动所有循环动画（用于关卡切换后）
func restart_loop_animations():
    if animation_manager:
        # 重新启动羽毛漂浮动画
        if change_chip:
            animation_manager.play_feather_float_animation(change_chip)

        # 重新启动卷轴浮动动画
        if scroll:
            scroll_float_tween = animation_manager.play_scroll_float_animation(scroll)

    # 重新启动管理器的呼吸动画
    if discard_area_manager and discard_area_manager.has_method("_start_image_breath"):
        discard_area_manager._start_image_breath()

    if deck_discard_manager and deck_discard_manager.has_method("_start_deck_discard_image_breath"):
        deck_discard_manager._start_deck_discard_image_breath()

func _on_buff_tooltip_requested(buff_data: Dictionary, global_pos: Vector2):
    # 如果已经有一个tooltip实例，先移除它
    if current_tooltip_instance:
        current_tooltip_instance.queue_free()

    # 创建新的tooltip实例
    current_tooltip_instance = hover_tooltip.instantiate()
    add_child(current_tooltip_instance)

    # 构建包含叠加次数的描述文本
    var description = buff_data.description
    # var stack_count = buff_data.get("stack_count", 1)

    # 如果有叠加次数信息，在描述末尾添加
    # if stack_count > 1:
    #     description += "\nx " + str(stack_count)

    # 更新并显示tooltip
    current_tooltip_instance.update_tooltip(description)
    current_tooltip_instance.show_tooltip()
    current_tooltip_instance.global_position = global_pos

func _on_buff_tooltip_hide_requested():
    # 如果有活动的tooltip实例，隐藏并移除它
    if current_tooltip_instance:
        current_tooltip_instance.hide_tooltip()
        current_tooltip_instance.queue_free()
        current_tooltip_instance = null

# 当buff_manager发出永久BUFF添加信号时调用
func _on_permanent_buff_added(buff_data: Dictionary):
    print("BUFF永久区获取到信号："+str(buff_data))
    forever_buff_area.add_buff(buff_data)

func _on_tutorial_completed() -> void:
    # 教程完成后的处理逻辑
    pass

# 处理骰子总值变化
func _on_dice_total_value_changed(total_value: int):
    if dice_num_label:
        dice_num_label.text = "[i]" + str(total_value) + "[/i]"

# 计算手牌布局参数
func _calculate_hand_layout_params():
    var container_width = hand_container.size.x
    var min_spacing = 10.0
    var max_spacing = 30.0
    var hover_scale = 1.15  # 悬停时的放大比例
    var total_cards = hand_card_nodes.size() - 1  # 减去被拖拽的卡牌
    var dragged_card_width = base_card_width * hover_scale
    var actual_spacing = max_spacing
    
    # 计算总宽度和实际间距
    var total_width = total_cards * base_card_width + dragged_card_width + total_cards * max_spacing
    if total_width > container_width:
        actual_spacing = min_spacing
        var min_spacing_total_width = total_cards * base_card_width + dragged_card_width + total_cards * min_spacing
        if min_spacing_total_width > container_width:
            var available_width = container_width - dragged_card_width
            var remaining_cards = total_cards
            var required_width_per_card = available_width / remaining_cards if remaining_cards > 0 else base_card_width
            required_width_per_card = max(required_width_per_card, 40.0)
            actual_spacing = -(base_card_width - required_width_per_card)
    
    # 重新计算实际总宽度
    total_width = total_cards * base_card_width + dragged_card_width + total_cards * actual_spacing
    var start_x = (container_width - total_width) / 2
    
    return {
        "container_width": container_width,
        "base_card_width": base_card_width,
        "dragged_card_width": dragged_card_width,
        "actual_spacing": actual_spacing,
        "total_cards": total_cards,
        "start_x": start_x,
        "total_width": total_width
    }

extends Control

# 提示框的内容
var custom_tooltip_text: String = ""
# 提示框的偏移量
var offset = Vector2(40, 20)  # 默认向右偏移40像素，向下偏移20像素

func _ready():
    # 初始时隐藏
    hide()
    # 设置鼠标过滤模式为忽略
    mouse_filter = Control.MOUSE_FILTER_IGNORE
    # 设置提示框显示在最上层
    z_index = 5
    # 确保子节点也在最上层
    z_as_relative = false

func update_tooltip(text: String):
    custom_tooltip_text = text
    $Panel/Label.text = text
    # 调整面板大小以适应文本
    await get_tree().process_frame
    var label_size = $Panel/Label.get_minimum_size()
    # 增加更大的内边距，左右各30像素，上下各20像素
    $Panel.custom_minimum_size = label_size + Vector2(60, 40)
    # 确保最小宽度至少200像素
    if $Panel.custom_minimum_size.x < 200:
        $Panel.custom_minimum_size.x = 200
    # 重新计算标签位置以确保居中
    await get_tree().process_frame

func _process(_delta):
    if visible:
        # 跟随鼠标位置，但保持在屏幕内
        var mouse_pos = get_global_mouse_position()
        var panel_size = $Panel.size
        var viewport_size = get_viewport_rect().size
        
        # 计算位置，确保提示框不会超出屏幕边界
        var pos_x = mouse_pos.x + offset.x
        var pos_y = mouse_pos.y + offset.y
        
        # 如果提示框会超出右边界，将其放在鼠标左侧
        if pos_x + panel_size.x > viewport_size.x:
            pos_x = mouse_pos.x - panel_size.x - offset.x
        
        # 如果提示框会超出下边界，将其向上移动
        if pos_y + panel_size.y > viewport_size.y:
            pos_y = mouse_pos.y - panel_size.y - offset.y
        
        global_position = Vector2(pos_x, pos_y)

func show_tooltip():
    show()

func hide_tooltip():
    hide() 

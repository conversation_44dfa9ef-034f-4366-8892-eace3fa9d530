[gd_scene load_steps=7 format=3 uid="uid://l42qff8bgprv"]

[ext_resource type="Script" uid="uid://j035akl8o5e2" path="res://script/forever_buff_icon.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://dcvesmnk72vqs" path="res://assert/victory_panel/card_poke.png" id="2_fbbmj"]
[ext_resource type="Shader" uid="uid://b3op4x8bkqxvd" path="res://shader/forever_buff_icon_shader.gdshader" id="2_ypg04"]
[ext_resource type="Texture2D" uid="uid://bnmb0x0qopolx" path="res://assert/sign/dice_count_or_rolls.png" id="3_fbbmj"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ypg04"]
shader = ExtResource("2_ypg04")
shader_parameter/Line_Smoothness = 0.045
shader_parameter/Line_Width = 0.09
shader_parameter/Brightness = 3.0
shader_parameter/Rotation_deg = 30.0
shader_parameter/Distortion = 1.8
shader_parameter/Speed = 0.3
shader_parameter/Position = 0.0
shader_parameter/Position_Min = 0.25
shader_parameter/Position_Max = 0.5
shader_parameter/Alpha = 0.5
shader_parameter/corner_radius = 0.13
shader_parameter/edge_smoothness = 0.01

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fbbmj"]
bg_color = Color(1, 0.568627, 0, 1)
corner_radius_top_left = 15
corner_radius_top_right = 15
corner_radius_bottom_right = 15
corner_radius_bottom_left = 15
anti_aliasing = false
anti_aliasing_size = 0.412

[node name="ForeverBuffIcon" type="Control"]
custom_minimum_size = Vector2(24, 40)
layout_mode = 3
anchors_preset = 0
offset_right = 24.0
offset_bottom = 40.0
script = ExtResource("1_script")

[node name="Ball" type="Control" parent="."]
anchors_preset = 0
offset_right = 24.0
offset_bottom = 40.0
pivot_offset = Vector2(12, 20)

[node name="Shadow" type="Sprite2D" parent="Ball"]
modulate = Color(0, 0, 0, 0.286275)
position = Vector2(8.90316, 45.0615)
scale = Vector2(0.08, 0.0220939)
skew = 0.785398
texture = ExtResource("2_fbbmj")
offset = Vector2(0, -225)

[node name="Background" type="TextureRect" parent="Ball"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_fbbmj")
expand_mode = 1
stretch_mode = 5

[node name="BuffIcon" type="TextureRect" parent="Ball"]
unique_name_in_owner = true
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = -10.0
offset_right = 10.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(10, 10)
texture = ExtResource("3_fbbmj")
expand_mode = 1
stretch_mode = 5

[node name="Shader" type="ColorRect" parent="Ball"]
material = SubResource("ShaderMaterial_ypg04")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 2.0
offset_bottom = -1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(12, 20)
mouse_filter = 2

[node name="ColorRect" type="Panel" parent="Ball"]
layout_mode = 0
offset_left = 8.0
offset_top = -8.0
offset_right = 28.0
offset_bottom = 12.0
theme_override_styles/panel = SubResource("StyleBoxFlat_fbbmj")

[node name="Label" type="Label" parent="Ball/ColorRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 14
text = "5"
horizontal_alignment = 1
vertical_alignment = 1

extends Control

# 信号定义
signal rule_button_pressed
signal settings_button_pressed
signal exit_button_pressed

# 节点引用
@onready var continue_button = %ContinueButton
@onready var rule_button = %RuleButton
@onready var settings_button = %SettingsButton
@onready var exit_button = %ExitButton
@onready var region_hint_checkbox = %RegionHintCheckBox
@onready var quick_settlement_checkbox = %QuickSettlementCheckBox
@onready var title_label = $CenterContainer/PanelContainer/MarginContainer/VBoxContainer/SettingLabel
@onready var panel = $CenterContainer/PanelContainer

# 类型提示
@onready var UIManager := get_node_or_null("/root/UIManager") as Node
@onready var background_draw := get_tree().get_root().get_node_or_null("GameScene/Background/Draw") as Node
@onready var animation_manager := get_node_or_null("/root/AnimationManager") as Node

const SETTING_PATH = "user://settings.cfg"

func _ready():
    # 初始隐藏面板
    visible = false
    
    # 连接按钮信号
    continue_button.pressed.connect(_on_continue_button_pressed)
    rule_button.pressed.connect(_on_rule_button_pressed)
    settings_button.pressed.connect(_on_settings_button_pressed)
    exit_button.pressed.connect(_on_exit_button_pressed)
    region_hint_checkbox.toggled.connect(_on_region_hint_toggled)
    quick_settlement_checkbox.toggled.connect(_on_quick_settlement_toggled)
    
    # 检查并打印节点状态
    print("Background/Draw node found: ", background_draw != null)
    
    # 应用UI管理器的主题和动画
    if UIManager:
        # 设置面板支点
        pivot_offset = size / 2
        # 初始状态
        modulate.a = 0
        scale = Vector2(0.9, 0.9)
        # 应用UI管理器的主题
        UIManager.init_scene_ui(self)
    
    # 设置面板和按钮样式
    _setup_panel_style()
    _setup_button_style()
    # 设置标题样式
    _setup_title_style()
    
    # 加载复选框状态
    _load_checkbox_settings()

# 设置面板样式
func _setup_panel_style():
    # 创建面板样式
    var panel_style = StyleBoxFlat.new()
    panel_style.bg_color = Color(0.1, 0.1, 0.2, 0.95)
    panel_style.corner_radius_top_left = 25
    panel_style.corner_radius_top_right = 25
    panel_style.corner_radius_bottom_left = 25
    panel_style.corner_radius_bottom_right = 25
    panel_style.border_width_left = 4
    panel_style.border_width_top = 4
    panel_style.border_width_right = 4
    panel_style.border_width_bottom = 4
    panel_style.border_color = Color(0.3, 0.3, 0.7, 1.0)
    panel_style.shadow_color = Color(0, 0, 0, 0.5)
    panel_style.shadow_size = 15
    panel_style.shadow_offset = Vector2(2, 2)
    
    # 应用样式
    panel.add_theme_stylebox_override("panel", panel_style)
    
    # 调整面板大小
    panel.custom_minimum_size = Vector2(400, 500)
    
    # 设置旋转中心点
    panel.pivot_offset = panel.size / 2

# 设置按钮样式
func _setup_button_style():
    var buttons = [continue_button, rule_button, settings_button, exit_button]
    
    for button in buttons:
        if not button:
            continue
            
        # 设置按钮大小
        button.custom_minimum_size = Vector2(271, 60)
        
        # 设置旋转中心点
        button.pivot_offset = button.custom_minimum_size / 2
        
        # 创建按钮样式
        var normal_style = StyleBoxFlat.new()
        normal_style.bg_color = Color(0.2, 0.4, 0.8, 1.0)
        normal_style.corner_radius_top_left = 15
        normal_style.corner_radius_top_right = 15
        normal_style.corner_radius_bottom_left = 15
        normal_style.corner_radius_bottom_right = 15
        normal_style.border_width_left = 4
        normal_style.border_width_top = 4
        normal_style.border_width_right = 4
        normal_style.border_width_bottom = 4
        normal_style.border_color = Color(0.4, 0.6, 1.0, 1.0)
        #normal_style.expand_margin_left = 10
        #normal_style.expand_margin_right = 10
        
        # 创建悬停样式
        var hover_style = normal_style.duplicate()
        hover_style.bg_color = Color(0.3, 0.5, 0.9, 1.0)
        hover_style.border_color = Color(0.5, 0.7, 1.0, 1.0)
        
        # 创建按下样式
        var pressed_style = normal_style.duplicate()
        pressed_style.bg_color = Color(0.1, 0.3, 0.7, 1.0)
        pressed_style.border_color = Color(0.4, 0.6, 1.0, 1.0)
        pressed_style.border_width_bottom = 2  # 按下时底部边框变细
        
        # 应用样式
        button.add_theme_stylebox_override("normal", normal_style)
        button.add_theme_stylebox_override("hover", hover_style)
        button.add_theme_stylebox_override("pressed", pressed_style)
        
        # 设置字体样式
        button.add_theme_font_size_override("font_size", 24)
        button.add_theme_color_override("font_color", Color(1.0, 1.0, 1.0, 1.0))
        button.add_theme_color_override("font_hover_color", Color(1.0, 1.0, 1.0, 1.0))
        button.add_theme_color_override("font_pressed_color", Color(0.9, 0.9, 0.9, 1.0))
        button.add_theme_constant_override("outline_size", 2)
        button.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 0.5))

# 设置标题样式
func _setup_title_style():
    # 检查title_label是否存在
    if !is_instance_valid(title_label):
        print("警告: 标题节点未找到")
        return
        
    # 设置标题文本
    title_label.text = "菜单"
    
    # 设置标题样式
    title_label.add_theme_font_size_override("font_size", 36)
    title_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2, 1.0))  # 金色
    title_label.add_theme_constant_override("outline_size", 6)
    title_label.add_theme_color_override("font_outline_color", Color(0.7, 0.0, 0.0, 1.0))
    title_label.add_theme_constant_override("shadow_size", 4)
    title_label.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.5))
    
    # 设置旋转中心点
    title_label.pivot_offset = title_label.size / 2
    
    # 播放标题动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    title_label.scale = Vector2(0.1, 0.1)
    tween.tween_property(title_label, "scale", Vector2(1.2, 1.2), 0.5)
    tween.tween_property(title_label, "scale", Vector2(1.0, 1.0), 0.3)

# 显示面板
func show_panel():
    # 使用UI管理器的弹出动画
    if UIManager:
        await UIManager.create_panel_popup(self)
    else:
        # 后备方案：自己实现动画
        _play_show_animation()

# 隐藏面板
func hide_panel():
    # 使用UI管理器的淡出动画
    if UIManager:
        await UIManager.create_panel_fade_out(self)
    else:
        # 后备方案：自己实现动画
        _play_hide_animation()
        await get_tree().create_timer(0.3).timeout

# 按钮点击处理
func _on_rule_button_pressed():
    emit_signal("rule_button_pressed")
    hide_panel()

func _on_settings_button_pressed():
    emit_signal("settings_button_pressed")
    hide_panel()

func _on_exit_button_pressed():
    emit_signal("exit_button_pressed")
    hide_panel()

# 继续游戏按钮点击处理
func _on_continue_button_pressed():
    hide_panel()

# 加载复选框设置
func _load_checkbox_settings():
    var config = ConfigFile.new()
    var err = config.load(SETTING_PATH)
    
    if err == OK:
        # 从配置文件加载设置
        var region_hint = config.get_value("gameplay", "region_hint", true)  # 默认为true
        var quick_settlement = config.get_value("gameplay", "quick_settlement", false)  # 默认为false
        
        # 设置复选框状态
        region_hint_checkbox.button_pressed = region_hint
        quick_settlement_checkbox.button_pressed = quick_settlement
        
        # 立即应用设置
        if background_draw:
            background_draw.visible = region_hint
        if animation_manager:
            animation_manager.set_quick_settlement(quick_settlement)
            
        print("已成功加载复选框设置")
    else:
        print("没有找到设置文件或加载失败，使用默认设置")
        # 使用默认值
        region_hint_checkbox.button_pressed = true
        quick_settlement_checkbox.button_pressed = false
        
        # 保存默认设置
        _save_checkbox_settings()

# 保存复选框设置
func _save_checkbox_settings():
    var config = ConfigFile.new()
    
    # 先尝试加载现有配置，以免覆盖其他设置
    config.load(SETTING_PATH)
    
    # 保存复选框状态
    config.set_value("gameplay", "region_hint", region_hint_checkbox.button_pressed)
    config.set_value("gameplay", "quick_settlement", quick_settlement_checkbox.button_pressed)
    
    # 保存配置文件
    var err = config.save(SETTING_PATH)
    SteamInfo.fileUpload("Settings",config)

# 区域提示复选框处理
func _on_region_hint_toggled(button_pressed: bool):
    print("Region hint toggled: ", button_pressed)  # 添加调试输出
    if background_draw:
        background_draw.visible = button_pressed
        print("Set Background/Draw visibility to: ", button_pressed)  # 添加调试输出
    else:
        print("Warning: Background/Draw node not found!")  # 添加错误提示
        # 尝试重新获取节点
        background_draw = get_tree().get_root().get_node_or_null("Game/Background/Draw")
        if background_draw:
            background_draw.visible = button_pressed
            print("Successfully found and updated Background/Draw node")
    
    # 保存设置
    _save_checkbox_settings()

# 快速结算复选框处理
func _on_quick_settlement_toggled(button_pressed: bool):
    if animation_manager:
        animation_manager.set_quick_settlement(button_pressed)
    
    # 保存设置
    _save_checkbox_settings()

# 后备方案：显示动画
func _play_show_animation():
    # 重置面板状态
    modulate.a = 0.0
    scale = Vector2(0.8, 0.8)
    visible = true
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(1.0, 1.0), 0.5)

# 后备方案：隐藏动画
func _play_hide_animation():
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 0.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(0.9, 0.9), 0.3)

# 按钮动画处理
func _on_button_mouse_entered(button: Button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.2)

func _on_button_mouse_exited(button: Button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.2)

func _on_button_down(button: Button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(0.9, 0.9), 0.1)

func _on_button_up(button: Button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.2)

[remap]

importer="wav"
type="AudioStreamWAV"
uid="uid://do2i35wbsmxvi"
path="res://.godot/imported/card-deck-shuffle.wav-89b090cc9332dbaf084a86e14eda40a7.sample"

[deps]

source_file="res://assert/audio/sfx/card-deck-shuffle.wav"
dest_files=["res://.godot/imported/card-deck-shuffle.wav-89b090cc9332dbaf084a86e14eda40a7.sample"]

[params]

force/8_bit=false
force/mono=false
force/max_rate=false
force/max_rate_hz=44100
edit/trim=false
edit/normalize=false
edit/loop_mode=0
edit/loop_begin=0
edit/loop_end=-1
compress/mode=2

extends Node

# 抽牌/弃牌区域管理器
# 负责处理抽牌和弃牌区域的交互逻辑

signal card_drawn # 抽牌信号
signal magic_deck_operation_completed # 魔法状态下替换卡牌完成的信号
signal mouse_entered_deck_area(is_magic: bool)
signal mouse_exited_deck_area
signal insufficient_points # 新增：消耗不足信号

# 节点引用
var deck_discard_area: Control # 抽牌/弃牌区域
var deck_discard_label: Label # 区域提示文字
var deck_count_label: Label # 牌堆数量标签
var tooltip_bubble: Control # 提示气泡
var deck_discard_image: TextureRect # 抽牌/弃牌图片节点
var deck_discard_shadow: TextureRect # 阴影节点
var discard_rect: TextureRect # 弃牌特效节点
var card_vortex # 卡牌旋涡效果节点
var parent_scene: Node # 父场景引用
var action_points_display: Control  # 新增：行动点显示引用

# 状态变量
var deck_area_tween = null # 存储当前的Tween动画引用
var image_breath_tween = null # DeckDiscardImage呼吸动画
var mouse_is_in_discard_area = false # 鼠标是否在区域内
var is_end_round_processing = false # 是否正在处理阶结束
var is_draw_card_processing = false # 是否正在抽牌
var next_card_indicator = null

# 引用其他管理器
var game_manager = null 
var card_system = null
var animation_manager = null
var audio_manager = null


# 初始化方法
func initialize(area_node, area_label, count_label, tooltip, parent_node, points_display):
    # 保存节点引用
    deck_discard_area = area_node
    deck_discard_label = area_label
    deck_count_label = count_label
    tooltip_bubble = tooltip
    parent_scene = parent_node
    action_points_display = points_display  # 新增：保存行动点显示引用
    
    # 获取全局管理器引用
    game_manager = parent_node.get_node_or_null("/root/GameManager")
    card_system = parent_node.get_node_or_null("/root/CardSystem")
    animation_manager = parent_node.get_node_or_null("/root/AnimationManager")
    audio_manager = parent_node.get_node_or_null("/root/AudioManager")
    
    # 获取DeckDiscardImage节点
    deck_discard_image = deck_discard_area.get_node_or_null("DeckDiscardImage")
    deck_discard_shadow = deck_discard_area.get_node_or_null("DeckDiscardShadow")
    discard_rect = deck_discard_area.get_node_or_null("DiscardRect")
    
    # 设置初始状态
    if discard_rect:
        discard_rect.visible = false
    
    # 设置阴影材质
    if deck_discard_image:
        deck_discard_image.pivot_offset = deck_discard_image.size / 2
        deck_discard_image.resized.connect(_on_deck_discard_image_size_changed)
        _start_deck_discard_image_breath()
    
    # 连接信号
    deck_discard_area.mouse_entered.connect(_on_deck_discard_area_mouse_entered)
    deck_discard_area.mouse_exited.connect(_on_deck_discard_area_mouse_exited)
    deck_discard_area.gui_input.connect(_on_deck_discard_area_gui_input)
    
    # 设置抽牌/弃牌区域样式
    _setup_deck_discard_area()

    # 确保脚本节点是 active 的
    set_process(true)

# 设置抽牌/弃牌区域样式
func _setup_deck_discard_area():
    # 只设置区域样式，不再设置label
    var shader_material = ShaderMaterial.new()
    shader_material.shader = load("res://shader/deck_button_effect.gdshader")
    var color_rect = deck_discard_area.get_node_or_null("ColorRect")
    if color_rect:
        color_rect.material = shader_material

# DeckDiscardImage自适应pivot_offset
func _on_deck_discard_image_size_changed():
    if deck_discard_image:
        deck_discard_image.pivot_offset = deck_discard_image.size / 2

# 启动DeckDiscardImage呼吸动画
func _start_deck_discard_image_breath():
    if not deck_discard_image:
        return
    if image_breath_tween and image_breath_tween.is_valid():
        image_breath_tween.kill()
    # 立即重置scale，防止残留
    deck_discard_image.scale = Vector2(1, 1)
    image_breath_tween = create_tween()
    # image_breath_tween.set_loops()
    image_breath_tween.set_trans(Tween.TRANS_SINE)
    image_breath_tween.set_ease(Tween.EASE_IN_OUT)
    image_breath_tween.tween_property(deck_discard_image, "scale", Vector2(1.05, 1.05), 1.5)
    image_breath_tween.parallel().tween_property(deck_discard_shadow, "scale", Vector2(1.03, 1.03), 1.5)
    image_breath_tween.parallel().tween_property(deck_discard_shadow, "modulate:a", 0.2, 1.5)
    image_breath_tween.tween_property(deck_discard_image, "scale", Vector2(1.0, 1.0), 1.5)
    image_breath_tween.parallel().tween_property(deck_discard_shadow, "scale", Vector2(1.0, 1.0), 1.5)
    image_breath_tween.parallel().tween_property(deck_discard_shadow, "modulate:a", 0.4, 1.5)

# 停止DeckDiscardImage呼吸动画
func _stop_deck_discard_image_breath():
    if image_breath_tween and image_breath_tween.is_valid():
        image_breath_tween.kill()
        image_breath_tween = null
    if deck_discard_image:
        deck_discard_image.scale = Vector2(1, 1)

# 停止呼吸动画（外部调用）
func stop_breath_animation():
    _stop_deck_discard_image_breath()

# 创建随机噪声纹理
func _create_noise_texture() -> NoiseTexture2D:
    var noise = FastNoiseLite.new()
    noise.noise_type = FastNoiseLite.TYPE_PERLIN
    noise.seed = randi()  # 使用随机种子
    noise.frequency = 0.04
    noise.fractal_octaves = 4
    noise.fractal_lacunarity = 2.0
    noise.fractal_gain = 0.5
    
    var texture = NoiseTexture2D.new()
    texture.width = 256
    texture.height = 256
    texture.seamless = true
    texture.noise = noise
    
    return texture

# 鼠标进入抽牌/弃牌区域
func _on_deck_discard_area_mouse_entered():
    if is_end_round_processing:
        return
        
    mouse_is_in_discard_area = true
    _stop_deck_discard_image_breath()
    if deck_area_tween and deck_area_tween.is_valid():
        deck_area_tween.kill()
    AudioManager.play_sfx("res://assert/audio/sfx/button_pop02.mp3")
    deck_discard_area.pivot_offset = deck_discard_area.size / 2
    if deck_discard_label:
        deck_discard_label.pivot_offset = deck_discard_label.size / 2
    deck_area_tween = create_tween()
    deck_area_tween.set_ease(Tween.EASE_OUT)
    deck_area_tween.set_trans(Tween.TRANS_ELASTIC)
    deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(1.1, 1.1), 0.3)
    if deck_discard_label:
        deck_area_tween.parallel().tween_property(deck_discard_label, "theme_override_colors/font_color", Color(0, 0, 0.5, 1), 0.3)
        deck_area_tween.parallel().tween_property(deck_discard_label, "scale", Vector2(1.1, 1.1), 0.3)
    
    if parent_scene.is_magic:
        if GameManager.consume_score:
            parent_scene.top_bar_manager.show_reduction_hint(GameManager.change_pool_action)
        else:
            action_points_display.show_reduction_hint(GameManager.change_pool_action)  # 魔法状态消耗5点
        # 为公共池三张牌启用边框效果
        for pool_card in parent_scene.pool_card_nodes:
            # 设置边框效果的参数
            var material = pool_card.get_material()
            if material:
                material.set_shader_parameter("enable_border_effect", true)
                material.set_shader_parameter("border_noise_texture", _create_noise_texture())
                material.set_shader_parameter("border_radius", 0.4)
                material.set_shader_parameter("border_effect_control", 0.3)
                material.set_shader_parameter("border_burn_speed", 0.1)
                material.set_shader_parameter("border_shape", 1.0)
    else:
        if GameManager.consume_score:
            parent_scene.top_bar_manager.show_reduction_hint(GameManager.draw_card_aciton)
        else:
            action_points_display.show_reduction_hint(GameManager.draw_card_aciton)  # 普通状态消耗1点
        # 只有在没有卡牌正在拖动时才显示下一张卡牌指示器
        if not parent_scene.is_dragging:
            show_next_card_indicator()

# 鼠标离开抽牌/弃牌区域
func _on_deck_discard_area_mouse_exited():
    mouse_is_in_discard_area = false
    _start_deck_discard_image_breath()
    if deck_area_tween and deck_area_tween.is_valid():
        deck_area_tween.kill()
    deck_area_tween = create_tween()
    deck_area_tween.set_ease(Tween.EASE_OUT)
    deck_area_tween.set_trans(Tween.TRANS_CUBIC)
    deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(1.0, 1.0), 0.2)
    if deck_discard_label:
        deck_area_tween.parallel().tween_property(deck_discard_label, "theme_override_colors/font_color", Color(1, 1, 1, 1), 0.2)
        deck_area_tween.parallel().tween_property(deck_discard_label, "scale", Vector2(1.0, 1.0), 0.2)

    # 关闭公共池牌的边框效果
    for pool_card in parent_scene.pool_card_nodes:
        var material = pool_card.get_material()
        if material:
            material.set_shader_parameter("enable_border_effect", false)

    # 隐藏卡牌指示器
    if animation_manager and animation_manager.drop_indicator:
        animation_manager.hide_drop_indicator(animation_manager.drop_indicator)

    # 隐藏行动点扣减提示
    if GameManager.consume_score:
        parent_scene.top_bar_manager.hide_reduction_hint()
    else:
        action_points_display.hide_reduction_hint()

# 显示下一张卡牌位置指示器
func show_next_card_indicator():
    # 计算下一张卡牌的位置
    var hand_card_nodes = parent_scene.hand_card_nodes
    var next_card_position = Vector2.ZERO
    if hand_card_nodes.size() > 0:
        var last_card = hand_card_nodes[hand_card_nodes.size() - 1]
        next_card_position = last_card.position + Vector2(last_card.size.x + 10, 0)  # 在最后一张卡右侧
    
    # 获取自适应布局参数
    var layout = parent_scene._calculate_adaptive_layout()
    
    # 使用 AnimationManager 创建指示器
    if animation_manager:
        var indicator = animation_manager.create_drop_indicator(parent_scene.hand_container, layout)
        indicator.position = next_card_position
        indicator.visible = true

# 抽牌/弃牌区域输入处理
func _on_deck_discard_area_gui_input(event):
    # 如果正在进行阶结算，则忽略输入
    if is_end_round_processing:
        return
        
    # 处理点击事件 - 抽牌
    if event is InputEventMouseButton:
            if event.pressed:  # 按下时
                _stop_deck_discard_image_breath()
                # 如果存在正在进行的动画，先取消它
                if deck_area_tween and deck_area_tween.is_valid():
                    deck_area_tween.kill()
                
                # 按下缩小效果 - Q弹效果
                deck_area_tween = create_tween()
                deck_area_tween.set_ease(Tween.EASE_OUT)
                deck_area_tween.set_trans(Tween.TRANS_CUBIC)
                deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(0.95, 0.95), 0.1)
            else:  # 释放时
                _start_deck_discard_image_breath()
                # 如果存在正在进行的动画，先取消它
                if deck_area_tween and deck_area_tween.is_valid():
                    deck_area_tween.kill()
                
                # 恢复悬停大小并执行抽牌
                deck_area_tween = create_tween()
                deck_area_tween.set_ease(Tween.EASE_OUT)
                deck_area_tween.set_trans(Tween.TRANS_ELASTIC)  # 使用弹性过渡实现Q弹效果
                deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(1.05, 1.05), 0.3)
                if mouse_is_in_discard_area:
                    if event.button_index == MOUSE_BUTTON_LEFT:
                        # 执行抽牌逻辑
                        await draw_card()
                    elif event.button_index == MOUSE_BUTTON_RIGHT:
                        # 执行燃烧逻辑
                        parent_scene._on_change_magic()
                        # await get_tree().create_timer(0.5).timeout
                    _reshow_border_and_indicator(event)
                    # _on_deck_discard_area_mouse_exited()
                    # _on_deck_discard_area_mouse_entered()

# 重新展示魔法边框效果和非魔法指示器位置
func _reshow_border_and_indicator(event):
    # 重置
    if deck_area_tween and deck_area_tween.is_valid():
        deck_area_tween.kill()
    deck_area_tween = create_tween()
    deck_area_tween.set_ease(Tween.EASE_OUT)
    deck_area_tween.set_trans(Tween.TRANS_CUBIC)
    deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(1.0, 1.0), 0.2)
    if deck_discard_label:
        deck_area_tween.parallel().tween_property(deck_discard_label, "theme_override_colors/font_color", Color(1, 1, 1, 1), 0.2)
        deck_area_tween.parallel().tween_property(deck_discard_label, "scale", Vector2(1.0, 1.0), 0.2)
    
    # 关闭公共池牌的边框效果
    for pool_card in parent_scene.pool_card_nodes:
        var material = pool_card.get_material()
        if material:
            material.set_shader_parameter("enable_border_effect", false)
    # 隐藏卡牌指示器
    if animation_manager and animation_manager.drop_indicator:
        animation_manager.hide_drop_indicator(animation_manager.drop_indicator)
    # 隐藏行动点扣减提示
    if GameManager.consume_score:
        parent_scene.top_bar_manager.hide_reduction_hint()
    else:
        action_points_display.hide_reduction_hint()

    # 再展示
    if deck_area_tween and deck_area_tween.is_valid():
        deck_area_tween.kill()
    deck_discard_area.pivot_offset = deck_discard_area.size / 2
    if deck_discard_label:
        deck_discard_label.pivot_offset = deck_discard_label.size / 2
    deck_area_tween = create_tween()
    deck_area_tween.set_ease(Tween.EASE_OUT)
    deck_area_tween.set_trans(Tween.TRANS_ELASTIC)
    deck_area_tween.tween_property(deck_discard_area, "scale", Vector2(1.1, 1.1), 0.3)
    if deck_discard_label:
        deck_area_tween.parallel().tween_property(deck_discard_label, "theme_override_colors/font_color", Color(0, 0, 0.5, 1), 0.3)
        deck_area_tween.parallel().tween_property(deck_discard_label, "scale", Vector2(1.1, 1.1), 0.3)
    if parent_scene.is_magic:
        if mouse_is_in_discard_area:
            if GameManager.consume_score:
                parent_scene.top_bar_manager.show_reduction_hint(GameManager.change_pool_action)
            else:
                action_points_display.show_reduction_hint(GameManager.change_pool_action)  # 魔法状态消耗5点
            # 为公共池三张牌启用边框效果
            for pool_card in parent_scene.pool_card_nodes:
                # 设置边框效果的参数
                var material = pool_card.get_material()
                if material:
                    material.set_shader_parameter("enable_border_effect", true)
                    material.set_shader_parameter("border_noise_texture", _create_noise_texture())
                    material.set_shader_parameter("border_radius", 0.4)
                    material.set_shader_parameter("border_effect_control", 0.3)
                    material.set_shader_parameter("border_burn_speed", 0.1)
                    material.set_shader_parameter("border_shape", 1.0)
    else:
        if GameManager.consume_score:
            parent_scene.top_bar_manager.show_reduction_hint(GameManager.draw_card_aciton)
        else:
            action_points_display.show_reduction_hint(GameManager.draw_card_aciton)  # 普通状态消耗1点
        # 只有在没有卡牌正在拖动时才显示下一张卡牌指示器
        if not parent_scene.is_dragging:
            show_next_card_indicator()
    

# 抽牌处理
func draw_card():
    # 如果正在处理阶结算，则不允许抽牌
    if is_end_round_processing or is_draw_card_processing:
        print("正在处理阶结算或抽牌中")
        return
    
    # 检查全局管理器是否有效
    if not game_manager or not card_system:
        return
    
    # 检查是否有足够的行动点
    var required_points = game_manager.change_pool_action if parent_scene.is_magic else game_manager.draw_card_aciton
    var is_not_consume = false
    if game_manager.consume_score:
        is_not_consume = game_manager.total_score < required_points
    else:
        is_not_consume = game_manager.action_points < required_points
    if is_not_consume:
        # 显示提示气泡
        if tooltip_bubble:
            tooltip_bubble.show_tooltip("消耗不足!")
        # 发出消耗不足信号
        insufficient_points.emit()
        return
    
    is_draw_card_processing = true
    if parent_scene.is_magic:
        # 对每张公共牌应用溶解特效
        for pool_card in parent_scene.pool_card_nodes:
            pool_card.enable_dissolve_effect()
        # 等待一小段时间让溶解特效显示
        await get_tree().create_timer(0.5).timeout
        # 弃掉所有公共牌
        card_system.discard_common_pool_cards()
        if parent_scene.pool_card_nodes:
            for pool_card in parent_scene.pool_card_nodes:
                pool_card.queue_free()
        parent_scene.pool_card_nodes.clear()

        # 断开信号
        if card_system.card_added_to_pool.is_connected(parent_scene._on_card_added_to_pool):
            card_system.card_added_to_pool.disconnect(parent_scene._on_card_added_to_pool)

        var spacing = 30.0

        var pool_container_width = parent_scene.pool_container.size.x
        var total_pool_width = 3 * parent_scene.base_card_width + 2 * spacing
        var pool_start_x = (pool_container_width - total_pool_width) / 2
        # 重新抽三张公共牌
        card_system.deal_to_common_pool(3)

        # 为每张公共牌池卡牌创建动画
        for i in range(3):
            # 等待一小段时间，创造顺序感
            await get_tree().create_timer(0.1).timeout  # 从0.1减少到0.05秒
            
            # 创建卡牌节点
            var card_node = parent_scene.CARD_NODE_SCENE.instantiate()
            parent_scene.pool_container.add_child(card_node)
            
            # 设置卡牌数据
            card_node.set_card_data(card_system.common_pool[i], false, i)
            
            # 连接信号
            card_node.card_clicked.connect(parent_scene._on_pool_card_clicked)
            card_node.card_right_clicked.connect(parent_scene._on_card_right_clicked)
            card_node.can_drag = false
            card_node.set_draggable(false)
            
            # 设置初始位置为抽牌区
            card_node.global_position = deck_discard_area.global_position
            
            # 计算最终位置（在公共牌池区）
            var target_position = parent_scene.pool_container.global_position + Vector2(
                pool_start_x + i * (parent_scene.base_card_width + spacing),
                0
            )
            
            # 播放抽牌动画
            if animation_manager:
                animation_manager.play_draw_card_animation(card_node, deck_discard_area.global_position, target_position)
                # 不等待动画完成，直接开始下一张卡牌的动画
                get_tree().create_timer(0.15).timeout.connect(  # 在动画进行到一定程度后播放落地效果
                    func(): animation_manager.play_card_landing_effect(card_node)
                )
            # 添加到公共牌池节点列表
            parent_scene.pool_card_nodes.append(card_node)

        # 重新连接信号
        card_system.card_added_to_pool.connect(parent_scene._on_card_added_to_pool)
        # 标记禁用的卡牌
        parent_scene._mark_forbidden_cards()

        # 发出魔法状态下替换卡牌完成的信号
        magic_deck_operation_completed.emit()
    else:
        # 从牌堆抽一张牌
        var is_success = card_system.deal_to_player(1)
        if not is_success:
            is_draw_card_processing = false
            return

        # 如果有手牌上限增加的永久BUFF，播放膨胀动画
        if game_manager.hand_limit > 5:  # 默认是5张，如果大于5说明有BUFF
            AnimationManager.play_permanent_buff_bounce_animation("hand_limit")

        # 发出抽牌信号
        card_drawn.emit()
    # 消耗行动点数或积分
    if game_manager.consume_score:
        game_manager.consume_score_points(required_points)
    else:
        game_manager.consume_action_points(required_points)
    
    is_draw_card_processing = false

# 更新牌堆牌数显示
func update_deck_count():
    # 检查全局管理器是否有效
    if card_system and deck_count_label:
        # 更新牌堆牌数显示
        deck_count_label.text = "牌数: %d" % card_system.deck.size()

# 设置阶结算处理状态
func set_end_round_processing(is_processing: bool):
    is_end_round_processing = is_processing
    
    # 根据状态设置区域的鼠标过滤器
    if deck_discard_area:
        deck_discard_area.mouse_filter = Control.MOUSE_FILTER_IGNORE if is_processing else Control.MOUSE_FILTER_STOP

# 获取当前区域的全局矩形（用于检测拖放）
func get_global_rect():
    return deck_discard_area.get_global_rect()

# 设置区域高亮（用于弃牌提示）
func set_highlight(highlight: bool):
    if deck_discard_area:
        deck_discard_area.modulate = Color(1, 0.5, 0.5, 1.0) if highlight else Color(1, 1, 1)
        
    # 当高亮时，显示弃牌特效并隐藏普通显示
    if highlight:
        if discard_rect:
            discard_rect.visible = true
            discard_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
        if deck_discard_image:
            deck_discard_image.visible = false
        if deck_discard_shadow:
            deck_discard_shadow.visible = false
    else:
        # 恢复正常显示
        if discard_rect:
            discard_rect.visible = false
            discard_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE
        if deck_discard_image:
            deck_discard_image.visible = true
        if deck_discard_shadow:
            deck_discard_shadow.visible = true

# 获取区域中心位置
func get_center_position() -> Vector2:
    return deck_discard_area.global_position + Vector2(deck_discard_area.size.x/2, deck_discard_area.size.y/2)

# 处理方法
func _process(delta):
    if deck_discard_image and deck_discard_shadow:
        deck_discard_shadow.scale = deck_discard_image.scale * 1.05 

func _exit_tree():
    if image_breath_tween:
        image_breath_tween.kill()

extends Node

# 奖励权重管理器 - 动态调整奖励获得概率

# 奖励选择计数器
var reward_selection_counts = {}

# 奖励权重配置
var reward_weight_configs = {}

# 当前权重
var current_weights = {}

func _ready():
    # 加载奖励权重配置
    _load_reward_weight_configs()
    # 初始化权重
    _initialize_weights()

# 加载奖励权重配置
func _load_reward_weight_configs():
    var config_file = ConfigFile.new()
    var err = config_file.load("res://config/buff_rewards.cfg")
    
    if err != OK:
        print("无法加载奖励配置文件：", err)
        return
    
    # 遍历所有奖励配置
    for section in config_file.get_sections():
        var reward_type = config_file.get_value(section, "type", "")
        if reward_type != "":
            reward_weight_configs[reward_type] = {
                "initial_weight": config_file.get_value(section, "initial_weight", 10),
                "max_selections": config_file.get_value(section, "max_selections", 999),
                "weight_decrease_start": config_file.get_value(section, "weight_decrease_start", 999),
                "weight_decrease_rate": config_file.get_value(section, "weight_decrease_rate", 0)
            }
            print("加载奖励权重配置 [", reward_type, "]: ", reward_weight_configs[reward_type])

# 初始化权重
func _initialize_weights():
    reward_selection_counts.clear()
    current_weights.clear()
    
    for reward_type in reward_weight_configs.keys():
        reward_selection_counts[reward_type] = 0
        current_weights[reward_type] = reward_weight_configs[reward_type].initial_weight

# 获取奖励的当前权重
func get_reward_weight(reward_type: String) -> float:
    if not reward_type in current_weights:
        return 0.0
    return current_weights[reward_type]

# 获取所有可用奖励的权重列表
func get_available_rewards_with_weights() -> Array:
    var available_rewards = []
    
    for reward_type in reward_weight_configs.keys():
        var weight = get_reward_weight(reward_type)
        if weight > 0:
            available_rewards.append({
                "type": reward_type,
                "weight": weight
            })
    
    return available_rewards

# 根据权重选择奖励
func select_rewards_by_weight(count: int) -> Array:
    var available_rewards = get_available_rewards_with_weights()
    var selected_rewards = []
    
    for i in range(count):
        if available_rewards.is_empty():
            break
            
        # 计算总权重
        var total_weight = 0.0
        for reward in available_rewards:
            total_weight += reward.weight
        
        if total_weight <= 0:
            break
            
        # 随机选择
        var random_value = randf() * total_weight
        var current_weight = 0.0
        
        for j in range(available_rewards.size()):
            current_weight += available_rewards[j].weight
            if random_value <= current_weight:
                selected_rewards.append(available_rewards[j].type)
                # 从可用列表中移除已选择的奖励（避免重复）
                available_rewards.remove_at(j)
                break
    
    return selected_rewards

# 记录奖励被选择
func record_reward_selection(reward_type: String):
    if not reward_type in reward_selection_counts:
        reward_selection_counts[reward_type] = 0
    
    reward_selection_counts[reward_type] += 1
    print("奖励 [", reward_type, "] 被选择，当前次数：", reward_selection_counts[reward_type])
    
    # 更新权重
    _update_reward_weight(reward_type)

# 更新奖励权重
func _update_reward_weight(reward_type: String):
    if not reward_type in reward_weight_configs:
        return
    
    var config = reward_weight_configs[reward_type]
    var selection_count = reward_selection_counts[reward_type]
    
    # 检查是否达到最大选择次数
    if selection_count >= config.max_selections:
        current_weights[reward_type] = 0.0
        print("奖励 [", reward_type, "] 达到最大选择次数，权重设为0")
        return
    
    # 检查是否需要开始降低权重
    if selection_count >= config.weight_decrease_start:
        var decrease_times = selection_count - config.weight_decrease_start + 1
        var new_weight = max(0.0, config.initial_weight - (decrease_times * config.weight_decrease_rate))
        current_weights[reward_type] = new_weight
        print("奖励 [", reward_type, "] 权重降低至：", new_weight)
    else:
        # 保持初始权重
        current_weights[reward_type] = config.initial_weight

# 重置所有权重（新游戏时调用）
func reset_all_weights():
    _initialize_weights()
    print("所有奖励权重已重置")

# 获取奖励选择统计信息
func get_selection_stats() -> Dictionary:
    return reward_selection_counts.duplicate()

# 调试：打印当前权重状态
func print_weight_status():
    print("=== 奖励权重状态 ===")
    for reward_type in reward_weight_configs.keys():
        var count = reward_selection_counts.get(reward_type, 0)
        var weight = current_weights.get(reward_type, 0)
        var config = reward_weight_configs[reward_type]
        print("%s: 选择次数=%d/%d, 当前权重=%.1f" % [reward_type, count, config.max_selections, weight])
    print("==================")

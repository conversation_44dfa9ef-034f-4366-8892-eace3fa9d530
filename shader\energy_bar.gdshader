shader_type canvas_item;

// 能量条着色器 - 实现透明蓝色的动态波纹效果

// 基础颜色和波纹颜色
uniform vec4 base_color : source_color = vec4(0.2, 0.5, 1.0, 0.7);
uniform vec4 wave_color : source_color = vec4(0.4, 0.7, 1.0, 0.8);

// 波纹参数
uniform float wave_speed = 1.0;           // 波纹移动速度
uniform float wave_amplitude = 0.02;      // 波纹幅度
uniform float wave_frequency = 10.0;      // 波纹频率

// 进度条填充程度 (0.0 - 1.0)
uniform float progress : hint_range(0.0, 1.0) = 0.0;

// 发光效果参数
uniform bool glow_enabled = true;
uniform float glow_intensity = 0.5;

void fragment() {
    // 基础颜色
    vec4 color = base_color;

    // 只在进度条内部显示
    if (UV.x <= progress) {
        // 创建多层波浪效果
        float wave1 = sin((UV.x * wave_frequency + TIME * wave_speed) * 3.14159) * wave_amplitude;
        float wave2 = sin((UV.x * wave_frequency * 1.3 + TIME * wave_speed * 0.8) * 3.14159) * wave_amplitude * 0.7;
        float wave = wave1 + wave2;

        // 动态波纹效果
        if (UV.y > 0.5 + wave && UV.y < 0.55 + wave) {
            color = mix(color, wave_color, 0.8);
        }

        // 第二道波纹
        if (UV.y > 0.3 + wave2 && UV.y < 0.35 + wave2) {
            color = mix(color, wave_color, 0.5);
        }

        // 添加亮度渐变
        color.rgb *= (1.0 - UV.y * 0.3);

        // 顶部高光
        if (UV.y < 0.2) {
            color.rgb = mix(color.rgb, vec3(1.0), (1.0 - UV.y * 5.0) * 0.3);
        }

        // 添加发光效果
        if (glow_enabled) {
            // 进度边缘发光
            float edge_distance = abs(UV.x - progress);
            if (edge_distance < 0.05) {
                float glow = (1.0 - edge_distance / 0.05) * glow_intensity;
                color.rgb += wave_color.rgb * glow;
            }
        }
    } else {
        // 未填充部分设为半透明
        color.a *= 0.2;
    }

    // 添加边框
    float border = 0.02;
    if (UV.x < border || UV.x > 1.0 - border || UV.y < border || UV.y > 1.0 - border) {
        color = mix(color, vec4(0.4, 0.6, 1.0, 1.0), 0.3);
    }

    COLOR = color;
}
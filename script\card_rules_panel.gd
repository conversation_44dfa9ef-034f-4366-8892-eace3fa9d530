extends Panel

# 牌型规则面板 - 仅显示牌型规则信息

# 信号
signal rules_panel_closed()

# 节点引用
@onready var rule_content = $VBoxContainer/ScrollContainer/RuleContent
@onready var close_button = $CloseButton
@onready var animation_manager = get_node("/root/AnimationManager")

# 规则数据
var rules_data: Dictionary = {}
var rules_values: Dictionary = {}

# 配置文件路径
const RULES_PATH = "res://data/game_rules.json"

# 颜色常量
const COLOR_HEADER = "#6600ff" 
const COLOR_NAME = "#6800ff"
const COLOR_SCORE = "#d600ff"
const COLOR_DESC = "#ff00ff"

# 初始化
func _ready():
    # 初始隐藏面板
    visible = false
    
    # 连接关闭按钮信号
    close_button.pressed.connect(hide_panel)
    
    # 设置中心点，用于缩放动画
    pivot_offset = size / 2
    
    # 加载规则数据
    _load_rules_data()
    
    # 连接语言变化信号
    #TranslationServer.connect("locale_changed", _on_locale_changed)

# 当语言改变时更新规则显示
func _on_locale_changed():
    _update_rules_display()

# 加载规则数据
func _load_rules_data():
    # 加载国际化规则数据
    var file = FileAccess.open(RULES_PATH, FileAccess.READ)
    if file:
        var json = JSON.new()
        var parse_result = json.parse(file.get_as_text())
        if parse_result == OK:
            rules_data = json.get_data()
        file.close()
    
    # 从 BuffManager 获取规则值数据
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager and buff_manager.game_rules_values:
        # 将 BuffManager 的格式转换为面板需要的格式
        rules_values = {"rules": []}
        for rule_id in buff_manager.game_rules_values.keys():
            var rule_data = buff_manager.game_rules_values[rule_id]
            rules_values.rules.append({
                "id": rule_id,
                "base_score": rule_data.base_score,
                "bonus_score": rule_data.bonus_score
            })
        print("从 BuffManager 加载了 %d 个规则值" % rules_values.rules.size())
    else:
        print("无法从 BuffManager 获取规则值数据")
        rules_values = {"rules": []}
    
    _update_rules_display()

# 获取当前语言的规则数据
func _get_current_language_rules() -> Dictionary:
    var current_locale = TranslationServer.get_locale()
    
    if rules_data.has(current_locale):
        return rules_data[current_locale]
    elif rules_data.has("en"):  # 如果找不到当前语言，回退到英语
        return rules_data["en"]
    else:
        push_error("No valid language data found in rules")
        return {}

# 更新规则显示
func _update_rules_display():
    var current_rules = _get_current_language_rules()
    if current_rules.is_empty() or not current_rules.has("rules") or not rules_values.has("rules"):
        return
        
    var bbcode = "[table=4]\n"
    
    # 添加表头 - 从当前语言的规则数据中获取
    if current_rules.has("headers"):
        var headers = current_rules.headers
        bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.type]
        bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.score]
        bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.bonus]
        bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]\n" % [COLOR_HEADER, headers.desc]
    
    # 添加规则数据，使用规则值数据中的分数
    for rule in current_rules.rules:
        # 查找对应的规则值
        var rule_values = null
        for value_rule in rules_values.rules:
            if value_rule.id == rule.id:
                rule_values = value_rule
                break
        
        if rule_values == null:
            continue
        
        var total_score = rule_values.base_score + rule_values.bonus_score
        
        # 牌型名称列
        bbcode += "[cell border=2][center][color=%s]%s[/color][/center][/cell]" % [COLOR_NAME, rule.name]
        
        # 基础分数列
        bbcode += "[cell border=2][center][color=%s]%d[/color][/center][/cell]" % [COLOR_SCORE, total_score]
        
        # 加成分值列
        bbcode += "[cell border=2][center][color=%s]%s[/color][/center][/cell]" % [COLOR_SCORE, "+" + str(int(rule_values.bonus_score))]
        
        # 说明列
        bbcode += "[cell border=2][color=%s]%s[/color][/cell]\n" % [COLOR_DESC, rule.description]
    
    bbcode += "[/table]"
    
    rule_content.text = bbcode

# 显示面板
func show_panel():
    # 重新加载数据以确保显示最新规则
    _load_rules_data()
    
    # 显示面板
    show()
    
    # 使用动画管理器播放显示动画
    animation_manager.play_scroll_show_animation(self)

# 隐藏面板
func hide_panel():
    # 使用动画管理器播放隐藏动画
    await animation_manager.play_scroll_hide_animation(self)
    hide()
    # 发出关闭信号
    emit_signal("rules_panel_closed")

func _input(event):
    # 如果面板可见且点击了鼠标左键
    if visible and event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
        hide_panel()
        # 消耗事件，防止点击穿透
        get_viewport().set_input_as_handled()

extends GPUParticles2D

@onready var timer = $PulsateTimer

var base_color = Color(0.3, 0.8, 0.2, 1)
var highlight_color = Color(0.5, 1, 0.3, 1)
var current_particles = []
var max_glow_particles = 5
var rng = RandomNumberGenerator.new()
var time_passed = 0.0
var movement_time = 0.0
var direction_change_time = 0.0

# 运动目标参数
var target_radial_min = -5.0
var target_radial_max = 5.0
var target_tangential_min = -3.0
var target_tangential_max = 3.0
var target_noise_speed = Vector3(0.1, 0.1, 0.1)
var current_direction = 1.0  # 1.0 为一个方向，-1.0 为相反方向

func _ready():
    rng.randomize()
    timer.timeout.connect(_on_pulsate_timer_timeout)
    _initialize_particles()
    
    # 初始化随机运动方向
    _randomize_movement_targets()

func _initialize_particles():
    current_particles.clear()
    # 创建初始粒子
    restart()

func _process(delta):
    time_passed += delta
    movement_time += delta
    direction_change_time += delta
    
    # 每1-3秒更新一次运动参数
    if movement_time > rng.randf_range(1.0, 3.0):
        movement_time = 0
        _update_movement_parameters()
    
    # 每3-8秒更改一次整体运动趋势
    if direction_change_time > rng.randf_range(3.0, 8.0):
        direction_change_time = 0
        _randomize_movement_targets()
        
        # 有20%的几率改变整体方向
        if rng.randf() < 0.2:
            current_direction *= -1.0

func _randomize_movement_targets():
    # 随机设定新的运动目标
    target_radial_min = rng.randf_range(-10.0, -2.0) * current_direction
    target_radial_max = rng.randf_range(2.0, 10.0) * current_direction
    
    target_tangential_min = rng.randf_range(-8.0, -1.0)
    target_tangential_max = rng.randf_range(1.0, 8.0)
    
    target_noise_speed = Vector3(
        rng.randf_range(0.05, 0.3),
        rng.randf_range(0.05, 0.3),
        rng.randf_range(0.05, 0.3)
    )

func _update_movement_parameters():
    # 获取粒子材质
    var material = process_material as ParticleProcessMaterial
    if material:
        # 渐进式调整当前参数向目标参数靠拢，创造平滑过渡
        material.radial_accel_min = lerp(material.radial_accel_min, target_radial_min, 0.2)
        material.radial_accel_max = lerp(material.radial_accel_max, target_radial_max, 0.2)
        
        material.tangential_accel_min = lerp(material.tangential_accel_min, target_tangential_min, 0.2)
        material.tangential_accel_max = lerp(material.tangential_accel_max, target_tangential_max, 0.2)
        
        # 平滑更新湍流噪声参数
        material.turbulence_noise_speed.x = lerp(material.turbulence_noise_speed.x, target_noise_speed.x, 0.1)
        material.turbulence_noise_speed.y = lerp(material.turbulence_noise_speed.y, target_noise_speed.y, 0.1)
        material.turbulence_noise_speed.z = lerp(material.turbulence_noise_speed.z, target_noise_speed.z, 0.1)
        
        # 随机调整湍流影响，使萤火虫不时有小幅度变化
        material.turbulence_influence_min = rng.randf_range(0.01, 0.02)
        material.turbulence_influence_max = rng.randf_range(0.02, 0.04)

func _on_pulsate_timer_timeout():
    # 随机选择几个粒子进行闪烁
    var material = process_material as ParticleProcessMaterial
    
    if material and material.color_ramp:
        var gradient = material.color_ramp.gradient as Gradient
        
        # 随机改变颜色亮度来创建闪烁效果
        var new_brightness = rng.randf_range(0.8, 1.2)
        var new_color = highlight_color * new_brightness
        
        # 保持透明度
        new_color.a = highlight_color.a
        
        # 更新梯度中的高亮颜色
        if gradient.colors.size() >= 3:
            gradient.colors[2] = new_color 

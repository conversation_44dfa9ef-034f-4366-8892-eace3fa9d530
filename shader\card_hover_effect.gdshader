shader_type canvas_item;

// 鼠标悬停效果参数
uniform float hover_scale : hint_range(1.0, 1.5) = 1.2;  // 悬停时的放大系数
uniform bool is_hovered = false;                        // 是否被悬停
uniform float transition_speed : hint_range(1.0, 10.0) = 5.0; // 过渡速度

// 当前缩放值（在代码中更新）
uniform float current_scale = 1.0;

void vertex() {
    // 应用缩放效果
    if (current_scale > 1.0) {
        // 从中心点缩放
        vec2 center = vec2(0.5, 0.5);
        VERTEX = (VERTEX - center) * current_scale + center;
    }
}

void fragment() {
    // 保持原始颜色
    COLOR = texture(TEXTURE, UV);
    
    // 如果被悬停，可以添加轻微的高亮效果
    if (is_hovered) {
        float highlight = 0.1;
        COLOR.rgb += vec3(highlight, highlight, highlight);
    }
}
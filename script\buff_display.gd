extends Control

# BUFF显示区域 - 负责在游戏界面左侧显示激活的BUFF卡牌

# 激活的BUFF卡牌节点列表
var active_buff_nodes = []
# 待激活的BUFF卡牌数据（阶结束后才显示）
var pending_buff_data = []
const CARD_NODE_SCENE = preload("res://scence/card_node.tscn")

# 新增信号，用于通知外部右键点击事件
signal buff_card_right_clicked(card_data)

var base_card_width = 45
var base_card_height = 68

# 布局相关常量
const MAX_CARD_SPACING = 60  # 最大卡牌间距（轻微遮挡）
const HORIZONTAL_OFFSET = -5  # 水平偏移量
const VERTICAL_MARGIN = 10   # 顶部和底部的边距

# 初始化
func _ready():
    # 确保使用 IGNORE，让鼠标事件穿透到下层节点
    mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 连接信号
    var card_system = get_node("/root/CardSystem")
    if card_system:
        card_system.connect("buff_activated", _on_buff_activated)
    
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        game_manager.connect("game_state_changed", _on_game_state_changed)
    
    # 获取BUFF管理器，连接BUFF过期信号
    var buff_manager = get_node("/root/BuffManager")
    if buff_manager:
        buff_manager.connect("buff_expired", _on_buff_expired)
        buff_manager.connect("buff_rounds_updated", _on_buff_rounds_updated)
    
    # 设置布局
    custom_minimum_size = Vector2(70, 230)
    size_flags_horizontal = Control.SIZE_EXPAND_FILL
    size_flags_vertical = Control.SIZE_EXPAND_FILL

# 计算卡牌间距
func _calculate_card_spacing() -> float:
    var num_cards = active_buff_nodes.size()
    if num_cards <= 1:
        return 0  # 单张卡牌不需要间距
    
    # 计算可用的垂直空间
    var available_height = custom_minimum_size.y
    
    # 计算所需的总间距（总高度减去第一张卡的高度）
    var required_spacing = (available_height - base_card_height) / (num_cards - 1)
    
    # 限制最大间距
    return min(required_spacing, MAX_CARD_SPACING)

# 计算卡牌的Y轴位置
func _calculate_card_y_position(index: int, total_cards: int, spacing: float) -> float:
    # 如果只有一张卡牌，居中显示
    if total_cards == 1:
        return (custom_minimum_size.y - base_card_height) / 2
    
    # 直接返回基于间距的位置
    return index * spacing

# 当BUFF被激活时
func _on_buff_activated(buff_data):
    # 将BUFF数据添加到待激活列表，等待阶结束后显示
    if not BuffManager._is_one_time_buff(buff_data.type):
        pending_buff_data.append(buff_data)

# 当游戏状态改变时
func _on_game_state_changed(new_state):
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
    
    # 当从ACTION状态切换到PREPARATION状态时（即阶结束时）
    if new_state == game_manager.GameState.PREPARATION and pending_buff_data.size() > 0:
        # 显示所有待激活的BUFF卡牌
        for buff_data in pending_buff_data:
            if buff_data.rounds_left > 0:
                display_buff_card(buff_data)
        
        # 清空待激活列表
        pending_buff_data.clear()

# 显示BUFF卡牌
func display_buff_card(buff_data):
    # 创建卡牌节点  
    var card_node = CARD_NODE_SCENE.instantiate()
    card_node.base_card_width = base_card_width
    card_node.base_card_height = base_card_height
    
    add_child(card_node)

    # 设置卡牌数据
    card_node.set_card_data(buff_data.card, false)
    card_node.set_as_active_buff_card()
    card_node.set_draggable(false)
    card_node.is_in_hand = false
    card_node.card_right_clicked.connect(_on_buff_display_card_right_clicked)

    # 存储BUFF数据的引用，用于震动效果匹配
    card_node.set_meta("buff_data", buff_data)
    
    # 设置初始位置为最后一张卡牌的位置（如果有的话）
    if active_buff_nodes.size() > 0:
        var last_card = active_buff_nodes[active_buff_nodes.size() - 1]
        card_node.position = last_card.position
    else:
        # 如果是第一张卡牌，设置在中心位置
        card_node.position = Vector2(0, (custom_minimum_size.y - base_card_height) / 2)
    
    # 添加到显示区域
    active_buff_nodes.append(card_node)
    
    # 重新排列所有卡牌
    _rearrange_buff_cards()
    
    card_node.is_buff_diplay_area = true
    
    # 设置标签样式
    if card_node.buff_label:
        card_node.buff_label.show_behind_parent = false
        card_node.buff_label.add_theme_font_size_override("font_size", 10)
    if card_node.rounds_label:
        card_node.rounds_label.add_theme_font_size_override("font_size", 10)
    
    # 确保BUFF效果可见
    if card_node.buff_effect:
        card_node.buff_effect.visible = false
    
    # 确保BUFF显示区域可见
    visible = true

# 新增处理BUFF卡牌右键点击的函数
func _on_buff_display_card_right_clicked(card_node):
    if card_node and card_node.card_data:
        # 发射信号，将卡牌数据传递给外部
        emit_signal("buff_card_right_clicked", card_node.card_data)

# 清除所有BUFF显示
func clear_all_buffs():
    for node in active_buff_nodes:
        node.queue_free()
    active_buff_nodes.clear()
    pending_buff_data.clear()

# 当BUFF过期时
func _on_buff_expired(buff_data):
    # 寻找并移除对应的BUFF卡牌
    for i in range(active_buff_nodes.size()):
        var card_node = active_buff_nodes[i]
        # 检查BUFF数据是否匹配过期的BUFF
        var is_match = false
        if card_node.has_meta("buff_data") and card_node.get_meta("buff_data") == buff_data:
            is_match = true
        elif card_node.card_data and "buff" in card_node.card_data and card_node.card_data.buff == buff_data:
            is_match = true

        if is_match:
            # 从显示列表中移除
            active_buff_nodes.remove_at(i)
            # BuffManager.buff_area.remove_at(i)
            
            # 创建动画使卡牌淡出
            var tween = create_tween()
            tween.tween_property(card_node, "modulate", Color(1, 1, 1, 0), 0.5)
            tween.tween_callback(card_node.queue_free)
            
            # 重新排列剩余的BUFF卡牌
            _rearrange_buff_cards()
            
            # 只移除一张卡牌（因为一个BUFF只对应一张卡牌）
            break

# 当BUFF阶数更新时
func _on_buff_rounds_updated(buff_data):
    # 更新对应BUFF卡牌的阶显示
    for card_node in active_buff_nodes:
        if card_node.card_data == buff_data.card:
            # 更新卡牌上的阶数显示
            card_node.update_rounds_display()
            break

# 重新排列BUFF卡牌
func _rearrange_buff_cards():
    var spacing = _calculate_card_spacing()
    var total_cards = active_buff_nodes.size()
    
    for i in range(total_cards):
        var card_node = active_buff_nodes[i]
        # 使用tween平滑移动到新位置
        var tween = create_tween()
        var new_y_position = _calculate_card_y_position(i, total_cards, spacing)
        var new_x_position = i * HORIZONTAL_OFFSET
        tween.tween_property(card_node, "position", Vector2(new_x_position, new_y_position), 0.5)

# 获取BUFF卡牌的目标位置
func get_buff_position(card_data) -> Vector2:
    # 计算新卡牌应该放置的位置
    var target_index = active_buff_nodes.size()  # 新卡牌将放在最后
    var y_position = 20 + target_index * 80  # 使用与_display_buff_card相同的间距

    # 返回全局坐标
    return global_position + Vector2(5, y_position)

# 根据BUFF数据查找对应的卡牌节点
func get_buff_card_node_by_buff(buff_data):
    for card_node in active_buff_nodes:
        # 首先检查是否有直接的buff_data匹配（使用meta数据）
        if card_node.has_meta("buff_data") and card_node.get_meta("buff_data") == buff_data:
            return card_node

        # 兼容旧的匹配方式
        if card_node.card_data and "buff" in card_node.card_data:
            # 检查BUFF数据是否匹配
            if card_node.card_data.buff == buff_data:
                return card_node
            # 也检查卡牌数据是否匹配（用于关联的卡牌BUFF）
            if "card" in buff_data and buff_data.card == card_node.card_data:
                return card_node
    return null

# 获取当前活跃的BUFF节点列表
func get_active_buff_nodes():
    return active_buff_nodes

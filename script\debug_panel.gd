extends Control

# 节点引用
@onready var level_input = %LevelInput
@onready var round_input = %RoundInput
@onready var action_points_input = %ActionPointsInput
@onready var total_score_input = %TotalScoreInput
@onready var limit_score_input = %LimitScoreInput

# 按钮引用
@onready var level_button = %LevelButton
@onready var round_button = %RoundButton
@onready var action_points_button = %ActionPointsButton
@onready var total_score_button = %TotalScoreButton
@onready var limit_score_button = %LimitScoreButton

func _ready():
    # 连接按钮信号
    level_button.pressed.connect(_on_level_button_pressed)
    round_button.pressed.connect(_on_round_button_pressed)
    action_points_button.pressed.connect(_on_action_points_button_pressed)
    total_score_button.pressed.connect(_on_total_score_button_pressed)
    limit_score_button.pressed.connect(_on_limit_score_button_pressed)
    
    # 初始化当前值
    _update_current_values()
    
    # 设置面板初始隐藏
    hide()

# 更新所有输入框的当前值
func _update_current_values():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        level_input.text = str(game_manager.current_level)
        round_input.text = str(game_manager.current_round)
        action_points_input.text = str(game_manager.action_points)
        total_score_input.text = str(game_manager.total_score)
        limit_score_input.text = str(game_manager.limit_score)

# 按钮点击处理函数
func _on_level_button_pressed():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var new_level = int(level_input.text)
        game_manager.set_level(new_level)

func _on_round_button_pressed():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var new_round = int(round_input.text)
        game_manager.set_round(new_round)

func _on_action_points_button_pressed():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var new_points = int(action_points_input.text)
        game_manager.set_action_points(new_points)

func _on_total_score_button_pressed():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var new_score = int(total_score_input.text)
        game_manager.set_total_score(new_score)

func _on_limit_score_button_pressed():
    var game_manager = get_node("/root/GameManager")
    if game_manager:
        var new_limit = int(limit_score_input.text)
        game_manager.set_limit_score(new_limit)

# 切换面板显示状态
func toggle():
    if visible:
        hide()
    else:
        _update_current_values()
        show()

[gd_scene load_steps=10 format=3 uid="uid://dt817mnqxn5ak"]

[ext_resource type="Script" uid="uid://c15g3548nn40v" path="res://script/aurora_particles.gd" id="1_v8mkl"]

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_tlhaw"]
blend_mode = 1
light_mode = 1

[sub_resource type="Gradient" id="Gradient_aurora"]
offsets = PackedFloat32Array(0, 0.00649351, 0.493506, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 0.8, 1, 1, 1, 0.8, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_qcj7k"]
gradient = SubResource("Gradient_aurora")
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 0)

[sub_resource type="Gradient" id="Gradient_lrjyg"]
offsets = PackedFloat32Array(0, 0.2, 0.6, 1)
colors = PackedColorArray(0, 0.454902, 0.462745, 0, 0.0627451, 0.615686, 0.690196, 0.72549, 0.121569, 0.545098, 0.619608, 0.552941, 0.137255, 0.239216, 0.482353, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_5g0x2"]
gradient = SubResource("Gradient_lrjyg")

[sub_resource type="Curve" id="Curve_yf2j3"]
_data = [Vector2(0, 0), 0.0, 2.0, 0, 0, Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_emnxy"]
curve = SubResource("Curve_yf2j3")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_kl85a"]
lifetime_randomness = 0.3
particle_flag_align_y = true
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(600, 10, 1)
direction = Vector3(0, -1, 0)
spread = 10.0
initial_velocity_min = 20.0
initial_velocity_max = 40.0
gravity = Vector3(0, 0, 0)
damping_min = 1.0
damping_max = 5.0
scale_curve = SubResource("CurveTexture_emnxy")
color_ramp = SubResource("GradientTexture1D_5g0x2")
turbulence_enabled = true
turbulence_noise_strength = 5.0
turbulence_noise_scale = 8.0
turbulence_noise_speed = Vector3(0.2, 0.5, 0)
turbulence_influence_min = 0.05
turbulence_initial_displacement_min = -100.0
turbulence_initial_displacement_max = 100.0

[node name="AuroraParticles" type="GPUParticles2D"]
modulate = Color(1.2, 1.2, 1, 0.6)
material = SubResource("CanvasItemMaterial_tlhaw")
position = Vector2(576, 620)
amount = 50
texture = SubResource("GradientTexture2D_qcj7k")
lifetime = 10.0
preprocess = 5.0
speed_scale = 0.5
randomness = 1.0
visibility_rect = Rect2(-600, -700, 1200, 800)
local_coords = true
draw_order = 0
process_material = SubResource("ParticleProcessMaterial_kl85a")
script = ExtResource("1_v8mkl")

[node name="WaveTimer" type="Timer" parent="."]
wait_time = 2.0
autostart = true

shader_type canvas_item;

// 光泽颜色
uniform vec4 shine_color : source_color = vec4(1.0, 1.0, 0.8, 0.8);
// 光泽条宽度
uniform float shine_width : hint_range(0.01, 0.5) = 0.2;
// 光泽速度
uniform float shine_speed : hint_range(0.1, 3.0) = 0.5;
// 光泽进度（用于动画控制）
uniform float shine_progress : hint_range(0.0, 1.0) = 0.0;

void fragment() {
    // 获取原始颜色
    vec4 original_color = texture(TEXTURE, UV);
    
    // 计算光泽位置
    float shine_pos = shine_progress * (1.0 + shine_width * 2.0) - shine_width;
    
    // 计算UV到光泽中心的距离
    float dist_to_shine = abs(UV.x - shine_pos);
    
    // 创建光泽效果（中心强，边缘弱）
    float shine_intensity = 1.0 - smoothstep(0.0, shine_width, dist_to_shine);
    
    // 应用光泽效果
    vec3 shine_effect = shine_color.rgb * shine_intensity * shine_color.a;
    
    // 混合原始颜色和光泽效果
    vec4 final_color = original_color;
    final_color.rgb += shine_effect;
    
    // 输出最终颜色
    COLOR = final_color;
} 
[gd_scene load_steps=3 format=3 uid="uid://bxnv6cucb8e66"]

[ext_resource type="Script" uid="uid://c2xw7r1fcqluw" path="res://script/settings_panel.gd" id="1_setting"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.2, 0.2, 0.2, 0.8)
corner_radius_top_left = 20
corner_radius_top_right = 20
corner_radius_bottom_right = 20
corner_radius_bottom_left = 20
shadow_color = Color(0, 0, 0, 0.4)
shadow_size = 8

[node name="SettingsPanel" type="Panel"]
z_index = 80
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_panel")
script = ExtResource("1_setting")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.2, 0)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 15

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "游戏设置"
horizontal_alignment = 1

[node name="ResolutionContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/ResolutionContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "分辨率："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="ResolutionOption" type="OptionButton" parent="VBoxContainer/ResolutionContainer"]
layout_mode = 2
size_flags_horizontal = 3
alignment = 1

[node name="FullscreenContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/FullscreenContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "全屏："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="FullscreenCheck" type="CheckBox" parent="VBoxContainer/FullscreenContainer"]
layout_mode = 2

[node name="BorderlessContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/BorderlessContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "无边框："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="BorderlessCheck" type="CheckBox" parent="VBoxContainer/BorderlessContainer"]
layout_mode = 2

[node name="SoundTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "音乐设置"
horizontal_alignment = 1

[node name="MusicContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/MusicContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "背景音乐音量："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="MusicSlider" type="HSlider" parent="VBoxContainer/MusicContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
max_value = 1.0
step = 0.01
value = 0.5

[node name="SFXContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/SFXContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "特效音量："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="SFXSlider" type="HSlider" parent="VBoxContainer/SFXContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
max_value = 1.0
step = 0.01
value = 0.5

[node name="LanguageTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "语言设置"
horizontal_alignment = 1

[node name="LanguageContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/LanguageContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "语言："
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="LanguageOption" type="OptionButton" parent="VBoxContainer/LanguageContainer"]
layout_mode = 2
size_flags_horizontal = 3
alignment = 1

[node name="Spacer" type="Control" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ReturnButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
size_flags_horizontal = 4
text = "返回"

extends Node

# 动画管理器 - 负责处理游戏中的所有动画效果

# 信号
signal animation_started(animation_type)
signal animation_finished(animation_type)

# 动画类型枚举
enum AnimationType {
    CARD_MOVE,          # 卡牌移动
    CARD_SWAP,          # 卡牌交换
    CARD_LANDING,       # 卡牌落地
    CARD_DISSOLVE,      # 卡牌溶解
    SCORE_CHANGE,       # 积分变化
    LIMIT_PULSE,        # 积分限制脉冲
    DISCARD_CARD,       # 弃牌
    DRAW_CARD,          # 抽牌
    IMPACT,             # 撞击
    SETTLEMENT,         # 结算
    LABLE_SHOW,         # 标签展示
    DROP_INDICATOR,     # 放置指示器动画
    COIN_ELASTIC,       # 金币弹性效果
    COIN_JUMP,          # 金币跳跃
    COIN_SHINE,         # 金币闪光
    COIN_INCREASE,      # 金币增加
    COIN_DECREASE,      # 金币减少
    FEATHER_FLOAT,      # 羽毛漂浮
    FEATHER_HOVER,      # 羽毛悬停
    FEATHER_BURN,       # 羽毛燃烧
    SCROLL_SHOW,        # 卷轴显示
    SCROLL_HIDE,        # 卷轴隐藏
    SCROLL_FLOAT,       # 卷轴浮动
    SCROLL_HOVER,       # 卷轴悬停
    SCROLL_CLICK,       # 卷轴点击
    BUFF_ICON_FLOAT,   # BUFF图标浮动
    BUFF_ICON_SPAWN,   # BUFF图标出现
    BUFF_CARD_FLY,     # BUFF卡牌飞行
    ROUND_PROGRESS,    # 阶进度条动画
    BUTTON_HEARTBEAT   # 按钮心跳动画
}

# 变量
var is_animation_playing = false
var animation_queue = []
var card_node_scene = null
var active_swap_animations = []  # 记录当前正在进行的交换动画
var drop_indicator = null       # 放置位置指示器引用
var is_quick_settlement = false # 快速结算模式标志
var current_game_scene = null   # 当前游戏场景引用

# 基准分辨率，用于计算缩放因子
const BASE_VIEWPORT_WIDTH = 1152.0
const BASE_VIEWPORT_HEIGHT = 648.0

# 动画时间缩放因子
const QUICK_ANIMATION_SCALE = 0.3  # 快速模式下动画时间缩放为0.5倍

var float_tween = null
var rotate_tween = null
var scroll_float_tween = null
var buff_icon_float_tween = null

# 初始化
func _ready():
    # 加载卡牌节点场景
    card_node_scene = load("res://scence/card_node.tscn")
    
    # 订阅场景树变化通知
    get_tree().node_added.connect(_on_node_added)
    get_tree().node_removed.connect(_on_node_removed)

# 处理新节点添加
func _on_node_added(node: Node) -> void:
    # 检查是否是主游戏场景
    if node.get_name() == "GameScene" and node.has_signal("scene_exiting"):
        # 更新当前游戏场景引用
        current_game_scene = node
        # 连接退出信号
        if not current_game_scene.scene_exiting.is_connected(_on_game_scene_exiting):
            current_game_scene.scene_exiting.connect(_on_game_scene_exiting)

# 处理节点移除
func _on_node_removed(node: Node) -> void:
    # 如果移除的是当前游戏场景，清除引用
    if node == current_game_scene:
        current_game_scene = null

# 添加场景退出处理函数
func _on_game_scene_exiting():
    print("接收到主游戏场景退出信号！")
    # 停止所有动画
    stop_all_loop_animations()

# 停止所有循环动画的函数
func stop_all_loop_animations():
    print("停止所有循环动画")
    # 停止所有动画
    is_animation_playing = false
    animation_queue.clear()
    active_swap_animations.clear()

    # 发送所有动画类型的完成信号
    for type in AnimationType.values():
        emit_signal("animation_finished", type)

    # 停止所有无限循环的Tween
    if float_tween:
        float_tween.kill()
        float_tween = null
    if rotate_tween:
        rotate_tween.kill()
        rotate_tween = null
    if scroll_float_tween:
        scroll_float_tween.kill()
        scroll_float_tween = null
    if buff_icon_float_tween:
        buff_icon_float_tween.kill()
        buff_icon_float_tween = null

    # 通知其他管理器停止循环动画
    _notify_managers_stop_loops()

    # 清理放置指示器
    clean_drop_indicator()

    # 强制清理所有可能的Tween引用
    _force_cleanup_all_tweens()

# 通知其他管理器停止循环动画
func _notify_managers_stop_loops():
    var game_scene = get_tree().current_scene
    if not game_scene:
        return

    # 停止骰子面板的脉冲动画
    var dice_panel = game_scene.get_node_or_null("DicePanel")
    if dice_panel and dice_panel.has_method("stop_pulse_animation"):
        dice_panel.stop_pulse_animation()

    # 停止游戏结束面板的心跳动画
    var game_over_panel = game_scene.get_node_or_null("GameOverPanel")
    if game_over_panel and game_over_panel.has_method("stop_heartbeat_animation"):
        game_over_panel.stop_heartbeat_animation()

    # 停止弃牌区管理器的呼吸动画（通过游戏场景的属性访问）
    if "discard_area_manager" in game_scene and game_scene.discard_area_manager != null:
        var discard_area_manager = game_scene.discard_area_manager
        if discard_area_manager.has_method("stop_breath_animation"):
            discard_area_manager.stop_breath_animation()

    # 停止抽牌区管理器的呼吸动画（通过游戏场景的属性访问）
    if "deck_discard_manager" in game_scene and game_scene.deck_discard_manager != null:
        var deck_discard_manager = game_scene.deck_discard_manager
        if deck_discard_manager.has_method("stop_breath_animation"):
            deck_discard_manager.stop_breath_animation()

# 强制清理所有可能的Tween引用
func _force_cleanup_all_tweens():
    # 由于Tween在Godot 4中不是Node的子类，我们无法通过场景树遍历来清理
    # 这里主要是确保我们自己的Tween引用被清理
    print("强制清理所有Tween引用完成")

# 设置快速结算模式
func set_quick_settlement(enabled: bool):
    is_quick_settlement = enabled

# 获取动画时间
func get_animation_duration(base_duration: float) -> float:
    return base_duration * (QUICK_ANIMATION_SCALE if is_quick_settlement else 0.7)

# 计算自适应缩放因子
func _calculate_adaptive_scale():
    if not get_tree() or not get_tree().get_root(): # 确保在有效的场景树中
        return 1.0 # 返回默认值
        
    var viewport_size = get_viewport().get_visible_rect().size
    
    var scale_factor_width = viewport_size.x / BASE_VIEWPORT_WIDTH
    var scale_factor_height = viewport_size.y / BASE_VIEWPORT_HEIGHT
    
    return min(scale_factor_width, scale_factor_height)

# 播放卡牌移动动画
func play_card_move_animation(card_node, start_position, end_position, duration = 0.5):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.CARD_MOVE)
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 设置卡牌移动动画
    tween.tween_property(card_node, "global_position", end_position, duration)
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.CARD_MOVE)

# 播放卡牌交换动画
func play_card_swap_animation(hand_card, pool_card, hand_position, pool_position, duration = 0.5, mouse_drop_position = null):
    # 将这次动画添加到活动动画列表
    var animation_id = randi()  # 生成一个随机ID标识这次动画
    var animation_info = {
        "id": animation_id,
        "type": AnimationType.CARD_SWAP,
        "hand_card": hand_card,
        "pool_card": pool_card,
        "completed": false
    }
    active_swap_animations.append(animation_info)
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.CARD_SWAP)
    
    # 先隐藏原始卡牌 - 但不影响其实际状态
    # 创建临时视觉节点来表示卡牌，这样不会影响原有逻辑
    var temp_pool_card = card_node_scene.instantiate()
    var temp_hand_card = card_node_scene.instantiate()
    
    # 获取两张卡牌的父节点，以添加临时卡牌
    var parent_node = hand_card.get_parent()
    parent_node.add_child(temp_pool_card)
    parent_node.add_child(temp_hand_card)
    
    # 设置临时卡牌数据，使其外观和原卡牌一致
    if pool_card.card_data:
        temp_hand_card.set_card_data(pool_card.card_data, false, pool_card.card_index)
    else:
        temp_hand_card.set_card_data(null, false, -1)
        
    if hand_card.card_data:
        temp_pool_card.set_card_data(hand_card.card_data, true, hand_card.card_index)
    else:
        temp_pool_card.set_card_data(null, true, -1)
    
    # 设置临时卡牌的位置
    # 手牌A应该从鼠标位置或手牌原始位置开始动画，移动到公共池牌B的位置
    if mouse_drop_position:
        temp_hand_card.global_position = mouse_drop_position  # 手牌A从鼠标释放位置开始
    else:
        temp_hand_card.global_position = hand_position  # 手牌A从原位置开始
    
    # 公共池牌B从原位置开始动画，移动到手牌A的原始位置
    temp_pool_card.global_position = pool_position  # 公共池牌B从原位置开始
    
    # 隐藏原始卡牌，使用临时卡牌进行动画展示
    hand_card.visible = false
    pool_card.visible = false
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 设置交换动画 - 只对临时卡牌进行动画，不影响原卡牌
    # 手牌A从当前位置移动到公共池牌B的位置
    tween.tween_property(temp_hand_card, "global_position", pool_position, duration)
    # 公共池牌B从当前位置移动到手牌A的原始位置
    tween.parallel().tween_property(temp_pool_card, "global_position", hand_position, duration)
    
    # 添加轻微的旋转和缩放效果，让动画更生动
    tween.parallel().tween_property(temp_hand_card, "rotation_degrees", temp_hand_card.rotation_degrees + 5, duration/2)
    tween.parallel().tween_property(temp_pool_card, "rotation_degrees", temp_pool_card.rotation_degrees - 5, duration/2)
    
    # 回到原始角度
    var second_tween = create_tween()
    second_tween.set_ease(Tween.EASE_OUT)
    second_tween.set_trans(Tween.TRANS_BACK)
    second_tween.tween_property(temp_hand_card, "rotation_degrees", 0, duration/2).set_delay(duration/2)
    second_tween.parallel().tween_property(temp_pool_card, "rotation_degrees", 0, duration/2).set_delay(duration/2)
    
    # 等待动画完成
    await tween.finished
    
    # 动画完成后，显示原始卡牌，删除临时卡牌
    hand_card.visible = true
    pool_card.visible = true
    temp_pool_card.queue_free()
    temp_hand_card.queue_free()
    
    # 标记此动画已完成
    for anim in active_swap_animations:
        if anim.id == animation_id:
            anim.completed = true
            break
    
    # 检查是否所有交换动画都已完成
    var all_completed = true
    for anim in active_swap_animations:
        if not anim.completed:
            all_completed = false
            break
    
    # 只有当所有交换动画都完成时，才标记整体动画结束
    if all_completed:
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.CARD_SWAP)
        # 清理已完成的动画记录
        active_swap_animations.clear()

# 播放卡牌落地粒子特效
func play_card_landing_effect(card_node):
    AudioManager.play_sfx("res://assert/audio/sfx/add_card.mp3")
    # AudioManager.play_sfx("res://assert/audio/sfx/flap_card.mp3")
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.CARD_LANDING)
    
    var scale_factor = _calculate_adaptive_scale()
    
    # 创建尘土粒子效果 - 使用CPUParticles2D替代GPUParticles2D
    var dust_particles = CPUParticles2D.new()
    card_node.get_parent().add_child(dust_particles)
    
    # 设置粒子位置为卡牌位置 - 放在卡牌中心
    dust_particles.global_position = card_node.global_position + Vector2(card_node.size.x / 2, card_node.size.y / 2)
    
    # 创建简单的圆形粒子贴图
    var particle_texture = GradientTexture2D.new()
    var texture_gradient = Gradient.new()
    texture_gradient.add_point(0.0, Color(1, 1, 1, 0.8))  # 中心为白色，略微透明
    texture_gradient.add_point(0.4, Color(0.95, 0.95, 0.95, 0.4))  # 中间区域更加透明
    texture_gradient.add_point(0.8, Color(0.9, 0.9, 0.9, 0.2))  # 边缘更加模糊
    texture_gradient.add_point(1.0, Color(0.9, 0.9, 0.9, 0))  # 边缘完全透明
    particle_texture.gradient = texture_gradient
    particle_texture.fill = GradientTexture2D.FILL_RADIAL
    particle_texture.width = 10  # 更小的纹理
    particle_texture.height = 10
    particle_texture.fill_from = Vector2(0.5, 0.5)
    particle_texture.fill_to = Vector2(1.0, 2.0)
    dust_particles.texture = particle_texture
    dust_particles.modulate = Color(1,1,1,0.3)
    
    # 直接配置CPUParticles2D属性
    dust_particles.amount = 60
    dust_particles.lifetime = 0.4
    dust_particles.explosiveness = 0.9
    dust_particles.randomness = 0.6
    dust_particles.one_shot = true
    dust_particles.emitting = true
    
    # 配置粒子发射属性 - 使用圆形发射
    dust_particles.emission_shape = CPUParticles2D.EMISSION_SHAPE_SPHERE
    dust_particles.emission_sphere_radius = (card_node.size.length() / 3.0)# 应用缩放
    
    # 配置粒子参数
    dust_particles.direction = Vector2(0, 0)
    dust_particles.spread = 180.0
    dust_particles.gravity = Vector2(0, 0)
    dust_particles.initial_velocity_min = 150.0 * scale_factor # 应用缩放
    dust_particles.initial_velocity_max = 210.0 * scale_factor # 应用缩放
    dust_particles.damping_min = 15.0
    dust_particles.damping_max = 25.0
    dust_particles.scale_amount_min = 1.8 * scale_factor # 应用缩放
    dust_particles.scale_amount_max = 2.8 * scale_factor # 应用缩放
    dust_particles.radial_accel_min = 30.0 * scale_factor # 应用缩放
    dust_particles.radial_accel_max = 50.0 * scale_factor # 应用缩放
    
    # 配置粒子大小变化曲线
    var size_curve = Curve.new()
    size_curve.add_point(Vector2(0.0, 0.5))
    size_curve.add_point(Vector2(0.2, 1.0))
    size_curve.add_point(Vector2(1.0, 0.0))
    dust_particles.scale_amount_curve = size_curve
    
    # 添加轻微震动效果
    var original_position = card_node.position
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(card_node, "position", original_position + Vector2(0, 5 * scale_factor), 0.1) # 应用缩放
    tween.tween_property(card_node, "position", original_position, 0.2)
    
    # 播放落地音效
    # AudioManager.play_sound("card_landing")
    
    # 等待粒子效果完成后自动销毁
    await get_tree().create_timer(dust_particles.lifetime + 0.2).timeout
    if dust_particles:
        dust_particles.queue_free()
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.CARD_LANDING)

# 播放积分变化动画
func play_score_animation(score_label, old_score, new_score):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCORE_CHANGE)
    
    var scale_factor = _calculate_adaptive_scale()
    
    # 保存原始字体大小和颜色
    var original_font_size = score_label.get_theme_font_size("font_size")
    if not original_font_size:
        original_font_size = 16 # 默认字体大小
    score_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    var original_score_color = score_label.get_theme_color("font_color")
    
    # 创建Tween动画
    var tween = create_tween()
    
    # 数字滚动效果 - 从旧积分滚动到新积分
    tween.parallel().tween_method(func(value): score_label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 膨胀效果
    tween.parallel().tween_property(score_label, "scale", Vector2(1.5, 1.5) * scale_factor, 0.3) # 应用缩放
    tween.parallel().tween_property(score_label, "theme_override_colors/font_color", Color(1, 0.9, 0, 1), 0.3) # 变为亮金色
    
    # 添加轻微抖动效果
    tween.parallel().tween_property(score_label, "rotation", -0.05, 0.2)
    tween.tween_property(score_label, "rotation", 0, 0.1)
    
    # 恢复正常大小和颜色
    tween.parallel().tween_property(score_label, "scale", Vector2(1.0, 1.0) * scale_factor, 0.2) # 应用缩放
    tween.parallel().tween_property(score_label, "theme_override_colors/font_color", original_score_color, 0.2) # 恢复原始颜色
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.SCORE_CHANGE)

# 播放积分限制标签动画
func play_limit_label_animation(limit_label):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.LIMIT_PULSE)
    
    var scale_factor = _calculate_adaptive_scale()
    
    # 创建Tween动画
    var tween = create_tween()
    
    # 脉冲效果
    tween.tween_property(limit_label, "scale", Vector2(1.2, 1.2) * scale_factor, 0.3) # 应用缩放
    tween.tween_property(limit_label, "scale", Vector2(1.0, 1.0) * scale_factor, 0.3) # 应用缩放
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.LIMIT_PULSE)

# 播放弃牌动画
func play_discard_animation(card_node, discard_area_position, duration = 0.5):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.DISCARD_CARD)
    
    var scale_factor = _calculate_adaptive_scale()
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 设置卡牌飞向弃牌区的动画
    tween.tween_property(card_node, "global_position", discard_area_position, duration)
    tween.parallel().tween_property(card_node, "rotation", randf_range(-PI/4, PI/4), duration)
    tween.parallel().tween_property(card_node, "scale", Vector2(0.8, 0.8) * scale_factor, duration) # 应用缩放
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.DISCARD_CARD)

# 播放抽牌动画
func play_draw_card_animation(card_node, start_position, end_position, duration = 0.2):  # 从0.5减少到0.3秒
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.DRAW_CARD)
    AudioManager.play_sfx("res://assert/audio/sfx/draw_card.mp3")
    
    # 设置初始位置和旋转
    card_node.global_position = start_position
    card_node.rotation = randf_range(0, PI/2)
    card_node.target_scale = Vector2(1.0, 1.0)
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 设置卡牌飞行动画
    tween.tween_property(card_node, "global_position", end_position, duration)
    tween.parallel().tween_property(card_node, "rotation", 0, duration) # 旋转
    tween.parallel().tween_property(card_node, "scale", Vector2(0.9, 0.9) , duration) # 应用缩放
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.DRAW_CARD)

# 播放保留卡牌移动动画
func play_retained_card_move_animation(card_node, target_position, duration = 0.15):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.CARD_MOVE)

    AudioManager.play_sfx("res://assert/audio/sfx/flap_card.mp3")
    
    var scale_factor = _calculate_adaptive_scale()
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 设置卡牌移动动画
    tween.tween_property(card_node, "global_position", target_position, duration)
    tween.parallel().tween_property(card_node, "scale", Vector2(1.0, 1.0) * scale_factor, duration)
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.CARD_MOVE)



# 播放卡牌溶解动画
func play_card_dissolve_animation(card_node, duration = 1.0):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.CARD_DISSOLVE)
    
    # 应用溶解shader
    var dissolve_material = ShaderMaterial.new()
    dissolve_material.shader = load("res://shader/card_dissolve_effect.gdshader")
    card_node.material = dissolve_material
    
    # 创建Tween动画控制溶解进度
    var tween = create_tween()
    tween.tween_method(func(value): card_node.material.set_shader_parameter("dissolve_value", value), 0.0, 1.0, duration)
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.CARD_DISSOLVE)

# 检查是否有动画正在播放
func is_any_animation_playing():
    return is_animation_playing

# 创建放置位置指示器
func create_drop_indicator(container, layout):
    # 如果已存在指示器，先移除它
    if drop_indicator != null and is_instance_valid(drop_indicator):
        drop_indicator.queue_free()
        
    # 获取布局参数
    var ind_card_width = layout.card_width
    var ind_card_height = layout.card_height
    var ind_base_card_w = layout.base_card_width
    var ind_base_card_h = layout.base_card_height

    # 创建Panel
    var panel = Panel.new()
    panel.size = Vector2(ind_card_width, ind_card_height)

    # 添加圆角效果 - 使用StyleBoxFlat
    var style_box = StyleBoxFlat.new()
    style_box.bg_color = Color(0, 0, 0, 0.2)  # 半透明黑色
    
    var base_corner_radius = 15.0
    # 使用新的 ind_base_card_w 和 ind_base_card_h 保证比例正确
    var card_size_ratio = min(panel.size.x / ind_base_card_w, panel.size.y / ind_base_card_h) 
    var corner_radius = base_corner_radius * card_size_ratio
    
    # 确保圆角大小合适
    corner_radius = clamp(corner_radius, 5.0 * layout.scale_factor, min(panel.size.x, panel.size.y) * 0.15) 
    
    style_box.corner_radius_top_left = corner_radius
    style_box.corner_radius_top_right = corner_radius
    style_box.corner_radius_bottom_left = corner_radius
    style_box.corner_radius_bottom_right = corner_radius
    
    panel.add_theme_stylebox_override("panel", style_box)
    container.add_child(panel)
    
    # 应用着色器
    var indicator_shader = ShaderMaterial.new()
    indicator_shader.shader = load("res://shader/card_indicator_effect.gdshader")
    indicator_shader.set_shader_parameter("amplitude", 2.0 * layout.scale_factor)
    indicator_shader.set_shader_parameter("frequency", 1.2)
    indicator_shader.set_shader_parameter("flash_speed", 3.0)
    indicator_shader.set_shader_parameter("min_alpha", 0.4)
    indicator_shader.set_shader_parameter("max_alpha", 0.7)
    indicator_shader.set_shader_parameter("edge_softness", 0.05)
    indicator_shader.set_shader_parameter("color", Color(1.0, 1.0, 0.0, 0.6))
    panel.material = indicator_shader
    
    # 初始状态为隐藏
    panel.visible = false
    
    # 存储指示器引用
    drop_indicator = panel
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.DROP_INDICATOR)
    
    return panel


# 显示放置指示器
func show_drop_indicator(indicator):
    if is_instance_valid(indicator):
        indicator.visible = true
        
        # 标记动画开始
        is_animation_playing = true
        emit_signal("animation_started", AnimationType.DROP_INDICATOR)

# 隐藏放置指示器
func hide_drop_indicator(indicator):
    if is_instance_valid(indicator):
        indicator.visible = false
        
        # 标记动画结束
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.DROP_INDICATOR)

# 清理放置指示器
func clean_drop_indicator():
    if drop_indicator != null and is_instance_valid(drop_indicator):
        drop_indicator.queue_free()
        drop_indicator = null

# 播放金币Q弹膨胀效果
func play_coin_elastic_effect(coin_node, scale_tween=null):
    if not coin_node:
        return null
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.COIN_ELASTIC)
    
    # 创建新的tween如果没有提供
    if scale_tween == null:
        scale_tween = create_tween()
        
    # 金币Q弹膨胀效果
    scale_tween.set_ease(Tween.EASE_OUT)
    scale_tween.set_trans(Tween.TRANS_ELASTIC)
    scale_tween.parallel().tween_property(coin_node, "scale", Vector2(1.4, 1.4), get_animation_duration(0.3))
    scale_tween.tween_property(coin_node, "scale", Vector2(1.0, 1.0), get_animation_duration(0.2))
    
    # 等待动画完成
    scale_tween.tween_callback(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.COIN_ELASTIC)
    )
    
    return scale_tween

# 播放金币跳跃动画
func play_coin_jump_animation(coin_node, score_value_label=null):
    if not coin_node:
        return null
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.COIN_JUMP)
    
    # 创建跳跃动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BOUNCE)
    
    # 跳跃动画序列 - 更高的跳跃高度
    tween.tween_property(coin_node, "position:y", coin_node.position.y - 30, 0.2)
    tween.tween_property(coin_node, "position:y", coin_node.position.y, 0.35)
    
    # 同时添加旋转和缩放效果
    var parallel_tween = create_tween()
    parallel_tween.set_ease(Tween.EASE_OUT)
    parallel_tween.set_trans(Tween.TRANS_BACK)
    
    # 添加缩放效果
    parallel_tween.tween_property(coin_node, "scale", Vector2(1.2, 1.2), 0.2)
    parallel_tween.tween_property(coin_node, "scale", Vector2(1.0, 1.0), 0.35)
    
    # 添加旋转效果
    var rotation_tween = create_tween()
    rotation_tween.set_ease(Tween.EASE_OUT)
    rotation_tween.set_trans(Tween.TRANS_SINE)
    rotation_tween.tween_property(coin_node, "rotation_degrees", 360, 0.55)
    rotation_tween.tween_property(coin_node, "rotation_degrees", 0, 0.01)
    
    # 添加发光效果（如果有积分标签）
    if score_value_label:
        var glow_tween = create_tween()
        glow_tween.tween_property(score_value_label, "modulate", Color(1.5, 1.5, 0.5, 1.0), 0.2)
        glow_tween.tween_property(score_value_label, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.35)
    
    # 完成后发送完成信号
    tween.tween_callback(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.COIN_JUMP)
    )
    
    return tween

# 播放分数增加时的Q弹膨胀特效
func play_coin_increase_animation(coin_node, score_value_label, old_score, new_score):
    if not coin_node or not score_value_label:
        return null
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.COIN_INCREASE)
    
    # 数字变化动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_method(func(value): score_value_label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 播放Q弹膨胀效果
    play_coin_elastic_effect(coin_node, tween)
    
    # 数字也有膨胀效果
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(score_value_label, "scale", Vector2(1.3, 1.3), 0.3)
    tween.tween_property(score_value_label, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 完成后发送完成信号
    tween.tween_callback(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.COIN_INCREASE)
    )
    
    return tween

# 播放分数减少时的抖动特效
func play_coin_decrease_animation(coin_node, score_value_label, old_score, new_score):
    if not coin_node or not score_value_label:
        return null
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.COIN_DECREASE)
    
    # 数字变化动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_method(func(value): score_value_label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 金币抖动特效
    var shake_tween = create_tween()
    shake_tween.set_ease(Tween.EASE_OUT)
    shake_tween.set_trans(Tween.TRANS_SINE)
    
    # 金币快速抖动
    for i in range(8):
        var offset = 3.0 * (1.0 - i/8.0)  # 抖动幅度逐渐减小
        var direction = 1 if i % 2 == 0 else -1
        shake_tween.tween_property(coin_node, "position:x", 
                             coin_node.position.x + offset * direction, 0.05)
    
    # 最后回到原位
    shake_tween.tween_property(coin_node, "position:x", coin_node.position.x, 0.05)
    
    # 颜色变红后恢复
    var color_tween = create_tween()
    color_tween.tween_property(coin_node, "modulate", Color(1.5, 0.5, 0.5, 1.0), 0.2)
    color_tween.tween_property(coin_node, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.3)
    
    # 数字闪红
    var label_color_tween = create_tween()
    label_color_tween.tween_property(score_value_label, "theme_override_colors/font_color", Color(1.0, 0.3, 0.3, 1.0), 0.2)
    label_color_tween.tween_property(score_value_label, "theme_override_colors/font_color", Color(1.0, 0.84, 0.0, 1.0), 0.3)
    
    # 完成后发送完成信号
    tween.tween_callback(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.COIN_DECREASE)
    )
    
    return tween

# 播放积分变化动画
func play_score_change_animation(score_label, coin_node, old_score, new_score):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCORE_CHANGE)
    
    # 创建tween动画
    var tween = create_tween()
    
    # 数字滚动效果 - 从旧积分滚动到新积分
    tween.parallel().tween_method(func(value): score_label.text = "%d" % value, old_score, new_score, get_animation_duration(0.3))
    
    # 膨胀效果
    tween.parallel().tween_property(score_label, "scale", Vector2(1.5, 1.5), get_animation_duration(0.3))
    tween.parallel().tween_property(score_label, "theme_override_colors/font_color", Color(1, 0.9, 0, 1), get_animation_duration(0.3)) # 变为亮金色
    
    # 添加轻微抖动效果
    tween.parallel().tween_property(score_label, "rotation", -0.05, get_animation_duration(0.2))
    tween.tween_property(score_label, "rotation", 0, get_animation_duration(0.1))
    
    # 恢复正常大小和颜色
    tween.parallel().tween_property(score_label, "scale", Vector2(1.0, 1.0), 0.2)
    tween.parallel().tween_property(score_label, "theme_override_colors/font_color", Color(1.0, 0.84, 0.0, 1.0), get_animation_duration(0.2)) # 恢复金色
    # 使用单独提取的方法播放金币膨胀效果
    if coin_node:
        play_coin_elastic_effect(coin_node, tween)
    
    # 动画结束后标记动画结束
    tween.tween_callback(func(): 
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.SCORE_CHANGE)
    )
    
    return tween

# 播放分数变化动画
func play_label_change_animation(label, coin_node, old_score, new_score):
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCORE_CHANGE)
    
    # 创建tween动画
    var tween = create_tween()
    
    # 数字滚动效果 - 从旧积分滚动到新积分
    tween.parallel().tween_method(func(value): label.text = "%d" % value, old_score, new_score, 0.3)
    
    # 膨胀效果
    tween.parallel().tween_property(label, "scale", Vector2(1.5, 1.5), get_animation_duration(0.3))
    tween.parallel().tween_property(label, "theme_override_colors/font_color", Color(1, 0.9, 0, 1), get_animation_duration(0.3)) # 变为亮金色
    
    # 添加轻微抖动效果
    tween.parallel().tween_property(label, "rotation", -0.05, get_animation_duration(0.2))
    tween.tween_property(label, "rotation", 0, get_animation_duration(0.1))
    
    # 使用单独提取的方法播放金币膨胀效果
    if coin_node:
        play_coin_elastic_effect(coin_node, tween)
    
    # 恢复正常大小和颜色
    tween.parallel().tween_property(label, "scale", Vector2(1.0, 1.0), get_animation_duration(0.2))
    tween.parallel().tween_property(label, "theme_override_colors/font_color", Color(1.0, 1.0, 1.0, 1.0), get_animation_duration(0.2))
    
    # 动画结束后标记动画结束
    tween.tween_callback(func(): 
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.SCORE_CHANGE)
    )
    
    return tween

func _fly_to_deck_discard_area(fly_tween,card_node,deck_position):
    # 关闭BUFF效果显示
    card_node.buff_effect.visible = false
    card_node.buff_ray_effect.visible = false

    AudioManager.play_sfx("res://assert/audio/sfx/whip03.mp3")

    # 为所有公共牌池节点添加飞向抽牌区的动画
    fly_tween.set_ease(Tween.EASE_IN)
    fly_tween.set_trans(Tween.TRANS_CUBIC)
    fly_tween.set_parallel(true)
    
    # 创建飞向抽牌区的动画，卡牌从头到脚逐渐缩小
    fly_tween.tween_property(card_node, "global_position", deck_position, get_animation_duration(0.3))
    fly_tween.parallel().tween_property(card_node, "scale", Vector2(0.01, 0.01), get_animation_duration(0.3))
    
    # 添加旋转效果，使卡牌头部朝上（卡牌头部为上方）
    var current_rotation = card_node.rotation
    fly_tween.parallel().tween_property(card_node, "rotation", current_rotation - PI/2 , get_animation_duration(0.3))
    fly_tween.parallel().tween_property(card_node, "modulate:a", 0.0, get_animation_duration(0.3))

    return fly_tween

func discard_fly_to_deck_area(card_node,deck_position):
    if not card_node:
        return
        
    card_node.scale = Vector2(0.6,0.6)
    card_node.visible = true
    
    # 关闭BUFF效果显示
    card_node.buff_effect.visible = false
    card_node.buff_ray_effect.visible = false
    
    # 播放音效
    AudioManager.play_sfx("res://assert/audio/sfx/draw_card.mp3")
    
    # 创建飞行动画
    var card_tween = create_tween()
    card_tween.set_parallel(true)
    card_tween.tween_property(card_node, "global_position", deck_position, 0.3)
    card_tween.tween_property(card_node, "scale", Vector2(0.01, 0.01), 0.3)
    # 添加旋转效果，使卡牌头部朝上
    var current_rotation = card_node.rotation
    card_tween.tween_property(card_node, "rotation", current_rotation - PI/2, 0.3)
    card_tween.tween_property(card_node, "modulate:a", 0.0, 0.3)
    
    # 动画完成后删除节点
    card_tween.finished.connect(func(): card_node.queue_free())
    
    return card_tween

func _highlight_front_cards(card_nodes, tween):
    for i in range(min(3, card_nodes.size())):
        var card_node = card_nodes[i]
        # 保存原始位置和缩放
        card_node.original_position = card_node.position
        var original_scale = card_node.scale
        
        # 放大卡牌
        tween.tween_property(card_node, "scale", original_scale * 1.2, get_animation_duration(0.2))
        # 提高卡牌位置
        # tween.tween_property(card_node, "position:y", card_node.original_position.y - 30, get_animation_duration(0.2))
        # 增加发光效果可以在这里添加
        # card_node.set_glow_effect(true)

# 播放标签弹性动画效果
func _play_label_animation(label, scale_to, position_y, rotation=0, highlight_color=null):

    emit_signal("animation_started", AnimationType.LABLE_SHOW)
    # 创建新的动画控制器
    var tween = create_tween()
    tween.set_parallel(false)
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC) # 使用弹性过渡
    
    # 播放气泡音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/bubble2.mp3")
    
    # 计算基于标签长度的保持时间
    var hold_time = 0.0
    if label and label.text:
        # 基于文本长度计算保持时间
        # 每个字符增加0.05秒，最大不超过1秒
        hold_time = min(label.text.length() * 0.05, 1.0)
        # 如果文本很短（少于5个字符），不需要额外保持时间
        if label.text.length() < 5:
            hold_time = 0.0
    
    # 淡入
    tween.tween_property(label, "modulate:a", 1.0, get_animation_duration(0.5))
    
    # 放大和跳动效果
    tween.parallel().tween_property(label, "scale", scale_to, get_animation_duration(0.4))
    tween.parallel().tween_property(label, "position:y", position_y, get_animation_duration(0.5))
    
    # 旋转效果
    if rotation != 0:
        tween.parallel().tween_property(label, "rotation", rotation, get_animation_duration(0.4))
    
    # 颜色闪烁效果
    if highlight_color and label:
        var original_color = label.get_theme_color("font_color")
        tween.parallel().tween_property(label, "theme_override_colors/font_color", highlight_color, get_animation_duration(0.4))
        tween.tween_property(label, "theme_override_colors/font_color", original_color, get_animation_duration(0.5))
    
    # 添加保持时间
    # if hold_time > 0:
    #     tween.tween_interval(get_animation_duration(hold_time))
    
    # 回弹效果
    tween.tween_property(label, "scale", Vector2(1.0, 1.0), get_animation_duration(0.5))
    tween.parallel().tween_property(label, "position:y", label.position.y, get_animation_duration(0.5))
    
    # 恢复正常角度
    if rotation != 0:
        tween.parallel().tween_property(label, "rotation", 0, get_animation_duration(0.4))
    
    # 淡出效果
    tween.parallel().tween_property(label, "modulate:a", 0.0, get_animation_duration(0.5))
    
    return tween

# 还原卡牌状态
func _restore_cards_state(card_nodes):
    var tween = create_tween()
    tween.set_parallel(true)
    
    for card_node in card_nodes:
        if card_node!= null:
            if card_node.has_method("set_draggable"):
                card_node.set_draggable(true)
            card_node.mouse_filter = Control.MOUSE_FILTER_STOP
        
        # 恢复原始位置和大小
        if "original_position" in card_node:
            tween.tween_property(card_node, "position", card_node.original_position, get_animation_duration(0.2))
            tween.tween_property(card_node, "scale", Vector2(1, 1), get_animation_duration(0.2))
            # 移除发光效果
            # card_node.set_glow_effect(false)
    return tween


# 总积分和限制积分撞击动画
func play_score_limit_collision_animation(total_score, limit_score, score_label, LimitLabel):
    if not score_label or not LimitLabel:
        return
    
    emit_signal("animation_started", AnimationType.IMPACT)
    # 保存原始位置和样式
    var score_original_position = score_label.global_position
    var limit_original_position = LimitLabel.global_position
    var limit_original_scale = LimitLabel.scale
    
    # 确保标签有初始颜色
    if not score_label.has_theme_color_override("font_color"):
        score_label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    if not LimitLabel.has_theme_color_override("font_color"):
        LimitLabel.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    
    var score_original_color = score_label.get_theme_color("font_color")
    var limit_original_color = LimitLabel.get_theme_color("font_color")
    
    # 计算结果（总积分减去限制积分）
    var result_score = max(0, int(total_score - limit_score))
    
    # 创建扣减值标签
    var reduction_label = Label.new()
    reduction_label.text = "-%d" % limit_score
    reduction_label.add_theme_font_size_override("font_size", 18)
    reduction_label.add_theme_color_override("font_color", Color(1, 0, 0, 1))
    reduction_label.add_theme_constant_override("outline_size", 2)
    reduction_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 1))
    reduction_label.modulate.a = 0.0
    
    # 将标签添加到积分标签的父节点
    score_label.add_child(reduction_label)
    
    # 设置初始位置（在积分标签正上方）
    reduction_label.global_position = Vector2(
        score_original_position.x - 5 ,
        score_original_position.y
    )
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_parallel(true)
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 第一阶段：limit_label Q弹膨胀
    # score_label保持原位不动
    tween.tween_property(LimitLabel, "scale", limit_original_scale * 2.0, get_animation_duration(0.4))
    
    # 等待膨胀完成
    await tween.finished

    # 播放撞击音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/whip.wav")
    
    # 第二阶段：limit_label快速飞向score_label
    tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_QUINT) # 使用更加剧烈的过渡效果
    
    # 计算碰撞位置（score_label左侧一点点，避免重叠）
    var collision_position = score_original_position - Vector2(score_label.size.x * 0.6, 0)
    
    # limit_label飞向score_label
    tween.tween_property(LimitLabel, "global_position", collision_position, get_animation_duration(0.3))
    
    # 等待飞行完成
    await tween.finished
    
    if not score_label or not LimitLabel:
        return
    create_level_up_particles(score_label.global_position)
    
    # 碰撞时两个标签变红
    tween = create_tween()
    tween.set_parallel(true)
    
    # 标签变红
    var collision_color = Color(1, 0, 0, 1) # 鲜红色
    tween.tween_property(score_label, "theme_override_colors/font_color", collision_color, get_animation_duration(0.1))
    tween.tween_property(LimitLabel, "theme_override_colors/font_color", collision_color, get_animation_duration(0.1))
    
    # 标签震动效果
    tween.tween_property(score_label, "rotation", 0.1, get_animation_duration(0.05))
    tween.tween_property(LimitLabel, "rotation", -0.1, get_animation_duration(0.05))

    # 播放撞击音效
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/impact.mp3")
    
    # 等待变红完成
    await tween.finished
    
    # 震动反向
    tween = create_tween()
    tween.set_parallel(true)
    tween.tween_property(score_label, "rotation", -0.1, get_animation_duration(0.05))
    tween.tween_property(LimitLabel, "rotation", 0.1, get_animation_duration(0.05))
    
    await tween.finished
    
    # 震动归位
    tween = create_tween()
    tween.set_parallel(true)
    tween.tween_property(score_label, "rotation", 0, get_animation_duration(0.05))
    tween.tween_property(LimitLabel, "rotation", 0, get_animation_duration(0.05))
    
    # 更新标签文本显示计算结果
    score_label.text = str(result_score)
    LimitLabel.text = "0"
    
    # 创建扣减值浮动动画
    var float_tween = create_tween()
    float_tween.set_ease(Tween.EASE_OUT)
    float_tween.set_trans(Tween.TRANS_CUBIC)
    
    # 向上浮动并淡出
    float_tween.tween_property(reduction_label, "global_position:y", reduction_label.global_position.y - 30, 0.5)
    float_tween.parallel().tween_property(reduction_label, "modulate:a", 1.0, 0.5)
    # float_tween.parallel().tween_property(reduction_label, "modulate:a", 0.0, 0.5)
    
    # 动画完成后移除标签
    float_tween.tween_callback(func(): reduction_label.queue_free())
    
    # 第四阶段：恢复原状
    tween = create_tween()
    tween.set_parallel(true)
    
    # 标签颜色恢复
    tween.tween_property(score_label, "theme_override_colors/font_color", score_original_color, get_animation_duration(0.5))
    tween.tween_property(LimitLabel, "theme_override_colors/font_color", limit_original_color, get_animation_duration(0.5))
    
    # limit_label回到原位
    tween.tween_property(LimitLabel, "global_position", limit_original_position, get_animation_duration(0.5))
    tween.tween_property(LimitLabel, "scale", limit_original_scale, get_animation_duration(0.5))
    
    await tween.finished

# 播放积分结算动画
func play_score_settlement_animation(hand_card_nodes,pool_card_nodes,score_value_label,score_coin,hand_container,buff_display,limit_label,score_display,action_points_label,scroll_area,result):
    # 获取卡牌系统和游戏管理器
    var card_system = get_node("/root/CardSystem")
    var game_manager = get_node("/root/GameManager")
    var buff_manager = get_node("/root/BuffManager")

    emit_signal("animation_started", AnimationType.SETTLEMENT)
    
    if not card_system or not game_manager or not buff_manager or not hand_container:
        return
    
    # 确保至少有3张牌
    if card_system.player_hand.size() < 3:
        return
    
    # 禁用所有卡牌的鼠标交互
    _disable_cards_interaction(hand_card_nodes,pool_card_nodes)
    
    # 获取前三张牌
    var front_cards = card_system.player_hand.slice(0, 3)
    var front_card_nodes = hand_card_nodes.slice(0, 3)
    
    # 创建动画控制器
    var tween = create_tween()
    tween.set_parallel(true)
    
    # 放大并高亮前三张牌
    AnimationManager._highlight_front_cards(front_card_nodes, tween)
    
    # 等待放大动画完成
    await tween.finished
    
    # 保存当前总积分，用于动画效果
    var current_score = game_manager.total_score
    
    # 显示每张牌的牌面积分
    var running_score = 0
    for i in range(3):
        if i < front_cards.size() and i < front_card_nodes.size():
            var card = front_cards[i]
            var card_node = front_card_nodes[i]
            
            # 跳过被禁用的卡牌
            if "is_disabled" in card and card.is_disabled:
                continue
            
            # 播放卡牌抖动动画
            var shake_tween = play_card_shake_animation(card_node, 10.0, get_animation_duration(0.3))
            await shake_tween.finished

            # 检查双枪手效果
            var final_card_value = card.value
            var is_double_gunner_triggered = false
            if game_manager.double_gunner_chance > 0:
                var random_chance = randf() * 100.0  # 生成0-100的随机数
                if random_chance < game_manager.double_gunner_chance:
                    final_card_value = card.value * 2
                    is_double_gunner_triggered = true

            # 如果双枪手触发，先显示特效
            if is_double_gunner_triggered:
                # 播放永久BUFF区域的膨胀效果
                play_permanent_buff_bounce_animation("double_gunner")

                # 播放额外的震动效果
                var double_shake_tween = play_card_shake_animation(card_node, 15.0, get_animation_duration(0.2))
                await double_shake_tween.finished

                # 显示双枪手触发标签
                var trigger_label = BuffManager._create_score_label(tr("DOUBLE_GUNNER_TRIGGERED"), card_node, 28, Color.PURPLE, Color(1.0, 1.0, 1.0, 1), Vector2(0, -30))
                var trigger_tween = _play_label_animation(trigger_label, Vector2(1.5, 1.5), card_node.size.y/8, 0.2)
                await trigger_tween.finished
                trigger_label.queue_free()

            # 创建单个卡牌积分标签并播放动画
            var score_text = "+%d" % final_card_value
            var score_color = Color(1, 0.9, 0, 1)
            if is_double_gunner_triggered:
                score_text = "+%d" % final_card_value
                score_color = Color(1, 0.9, 0, 1)  # 金色表示双枪手触发

            var hand_score_label = BuffManager._create_score_label(score_text, card_node, 40, score_color, Color(0.8, 0.4, 0, 1))

            # 显示积分标签动画 - 使用弹性缓动效果
            var label_scale = Vector2(1.8, 1.8)
            if is_double_gunner_triggered:
                label_scale = Vector2(2.2, 2.2)  # 双枪手触发时更大的缩放
            tween = _play_label_animation(hand_score_label, label_scale, card_node.size.y/6, 0.2)

            # 累加积分
            running_score += final_card_value
            # 更新顶部积分标签
            var increment_score = current_score + running_score
            
            # 使用统一的积分动画方法
            tween = play_score_change_animation(score_value_label, score_coin, increment_score - card.value, increment_score)
            
            await tween.finished
            if hand_score_label:
                hand_score_label.queue_free()

    # 牌型加成BUFF结算
    # 获取BUFF展示区节点
    var buff_area_nodes = null
    if buff_display and buff_display.has_method("get_active_buff_nodes"):
        buff_area_nodes = buff_display.get_active_buff_nodes()

    await BuffManager.hand_type_buff(hand_container, hand_card_nodes, buff_area_nodes)
    
    # 显示牌型名称和基础积分
    # 优先使用从game_rules.json获取的本地化名称，如果没有则使用原来的方法
    var hand_type_name = result.get("type_name", "")
    if hand_type_name == "":
        hand_type_name = card_system.get_hand_type_name(result.type)

    var base_score = result.base_score
    var face_value = result.face_value

    if not hand_container:
        return
    
    # 创建牌型标签
    var type_label = BuffManager._create_score_label(hand_type_name, hand_container, 40, Color(0, 0.8, 1, 1), null, Vector2(0 , hand_container.size.y))
    
    # 创建基础积分标签
    var base_score_label = BuffManager._create_score_label("+%d" % base_score, hand_container, 48, Color(1, 0.6, 0, 1), Color(0.8, 0.2, 0, 1), Vector2(type_label.size.x, hand_container.size.y))
    
    # 播放卷轴抖动动画
    tween = play_card_shake_animation(scroll_area, 10.0, get_animation_duration(0.3))
    # 播放牌型标签动画
    tween = _play_label_animation(type_label, Vector2(1.5, 1.5), hand_container.size.y/6, 0.1)
    
    await tween.finished
    

    # 播放卷轴抖动动画
    tween = play_card_shake_animation(scroll_area, 10.0, get_animation_duration(0.3))
    # 播放基础积分标签动画
    tween = _play_label_animation(base_score_label, Vector2(2.2, 2.2), hand_container.size.y/6, 0.1, Color(1.0, 0.9, 0, 1))
    
    
    # 更新顶部积分标签 - 添加最终积分动画
    var final_score = current_score + running_score + base_score
    
    # 使用统一的积分动画方法
    tween = play_score_change_animation(score_value_label, score_coin, current_score + running_score, final_score)
    await tween.finished
    
    # 清理临时标签
    if type_label:
        type_label.queue_free()
    if base_score_label:
        base_score_label.queue_free()
    
    # 处理BUFF效果
    if buff_manager.active_buffs.size() > 0 or buff_manager.buff_area.size() > 0:
        print("active_buffs:",buff_manager.active_buffs.size(),"buff_area:",buff_manager.buff_area.size())
        running_score = await BuffManager.process_buffs(buff_manager, current_score, running_score, base_score,hand_container,buff_display,score_value_label,score_coin,limit_label,hand_card_nodes,action_points_label,face_value)
    
    # 等待一小段时间让玩家查看最终积分
    await get_tree().create_timer(get_animation_duration(0.5)).timeout

    var sum_score = current_score + running_score + base_score

    # 应用恶魔低语效果
    var demon_effect = game_manager.calculate_demon_whisper_effect()
    if demon_effect != 0:
        # 播放永久BUFF区域的膨胀效果
        play_permanent_buff_bounce_animation("demon_whisper")
        await _play_demon_whisper_effect(demon_effect, sum_score, hand_container, score_value_label, score_coin)
        sum_score += demon_effect

    # 总积分和限制积分撞击动画
    await play_score_limit_collision_animation(sum_score, game_manager.limit_score,score_value_label,limit_label)
    game_manager.total_score = sum_score
    
    # 恢复卡牌状态
    var restore_tween = _restore_cards_state(front_card_nodes)
    await restore_tween.finished
    
    # 恢复所有卡牌的鼠标交互
    # 只有在没有到达积分限制时才恢复交互，否则卡牌会被阶结束代码处理
    if sum_score < game_manager.limit_score:
        for card_node in hand_card_nodes:
            card_node.mouse_filter = Control.MOUSE_FILTER_STOP
            card_node.set_draggable(true)
            
        for card_node in pool_card_nodes:
            card_node.mouse_filter = Control.MOUSE_FILTER_STOP
    
    score_display.text = ""

# 播放恶魔低语效果动画
func _play_demon_whisper_effect(demon_effect: int, current_sum_score: int, hand_container, score_value_label, score_coin):
    # 创建恶魔低语效果标签
    var effect_text = ""
    var label_color = Color.RED

    if demon_effect > 0:
        effect_text = tr("DEMON_WHISPER_TRIGGERED") + " +%d" % demon_effect
        label_color = Color.PURPLE
    else:
        effect_text = tr("DEMON_WHISPER_TRIGGERED") + " %d" % demon_effect
        label_color = Color.RED

    # 创建效果标签
    var demon_label = BuffManager._create_score_label(
        effect_text,
        hand_container,
        28,  # 字体大小
        label_color,
        Color(0.1, 0.1, 0.1, 1),  # 描边颜色
        Vector2(0, -50)  # 位置偏移
    )

    # 播放标签动画
    var label_tween = _play_label_animation(demon_label, Vector2(1.5, 1.5), 30, 0)
    await label_tween.finished

    # 更新分数显示
    var new_score = current_sum_score + demon_effect
    var score_tween = play_score_change_animation(score_value_label, score_coin, current_sum_score, new_score)
    await score_tween.finished

    # 清理标签
    demon_label.queue_free()

# 禁用所有卡牌的鼠标交互
func _disable_cards_interaction(hand_card_nodes,pool_card_nodes):
    for card_node in hand_card_nodes:
        card_node.mouse_filter = Control.MOUSE_FILTER_IGNORE
        card_node.set_draggable(false)
        
    for card_node in pool_card_nodes:
        card_node.mouse_filter = Control.MOUSE_FILTER_IGNORE
        
# 创建通用升级粒子效果
func create_level_up_particles(position, color = Color(1.0, 0.8, 0.0, 1.0)):
    # 创建粒子节点
    var particles = CPUParticles2D.new()
    
    # 添加到场景树
    get_tree().get_root().add_child(particles)
    
    # 设置粒子位置
    particles.global_position = position
    particles.texture = load("res://assert/particles/32x32_Star_8.png")
    
    # 配置粒子参数
    particles.amount = 20
    particles.lifetime = 0.5
    particles.explosiveness = 0.9
    particles.one_shot = true
    particles.emitting = true
    particles.direction = Vector2(0, -1)
    particles.spread = 180
    particles.gravity = Vector2(0, 50)
    particles.initial_velocity_min = 180
    particles.initial_velocity_max = 220
    particles.scale_amount_min = 0.5
    particles.scale_amount_max = 1
    particles.color = color  # 使用传入的颜色
    particles.color_ramp = null
    
    
    # 自动销毁粒子节点
    var timer = Timer.new()
    particles.add_child(timer)
    timer.wait_time = 2.5
    timer.one_shot = true
    timer.timeout.connect(func(): particles.queue_free())
    timer.start()

# 播放羽毛漂浮动画
func play_feather_float_animation(feather_node):
    if not feather_node:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.FEATHER_FLOAT)
    
    # 创建一个循环的漂浮动画（使用递归重启而不是set_loops）
    _start_feather_float_cycle(feather_node)

# 开始羽毛漂浮循环
func _start_feather_float_cycle(feather_node):
    if not is_instance_valid(feather_node):
        return

    # 创建垂直漂浮动画
    float_tween = create_tween()
    float_tween.set_trans(Tween.TRANS_SINE)
    float_tween.set_ease(Tween.EASE_IN_OUT)

    # 垂直漂浮动画
    var original_y = feather_node.position.y -10
    float_tween.tween_property(feather_node, "position:y", original_y - 10, 1.5)
    float_tween.tween_property(feather_node, "position:y", original_y + 10, 1.5)

    # 添加轻微的旋转动画
    rotate_tween = create_tween()
    rotate_tween.set_trans(Tween.TRANS_SINE)
    rotate_tween.set_ease(Tween.EASE_IN_OUT)

    rotate_tween.tween_property(feather_node, "rotation", -0.1, 2.0)
    rotate_tween.tween_property(feather_node, "rotation", 0.1, 2.0)

    # 当动画完成时，重新开始循环
    float_tween.finished.connect(_on_feather_float_cycle_finished.bind(feather_node))

# 羽毛漂浮循环完成回调
func _on_feather_float_cycle_finished(feather_node):
    if is_instance_valid(feather_node) and float_tween != null:
        _start_feather_float_cycle(feather_node)

# 播放羽毛悬停动画
func play_feather_hover_animation(feather_node, is_hovering):
    if not feather_node:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.FEATHER_HOVER)
    
    # 创建动画
    var tween = create_tween()
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.set_ease(Tween.EASE_OUT)
    
    if is_hovering:
        # 悬停时放大
        tween.tween_property(feather_node, "scale", Vector2(1.2, 1.2), 0.3)
    else:
        # 离开时恢复
        tween.tween_property(feather_node, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.FEATHER_HOVER)

# 播放羽毛切换动画
func play_feather_burn_animation(feather_node, shadow_node, texture_node, new_texture_path):
    if not feather_node or not shadow_node or not texture_node:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.FEATHER_BURN)
    
    # 创建Q弹缩小动画
    var tween = create_tween()
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.set_ease(Tween.EASE_IN_OUT)
    
    # 播放切换音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/bubble1.mp3")
    
    # 缩小动画
    tween.tween_property(feather_node, "scale", Vector2(0.01, 0.01), 0.3)
    
    # 等待缩小动画完成
    await tween.finished
    
    # 切换纹理
    shadow_node.texture = load(new_texture_path)
    texture_node.texture = load(new_texture_path)
    
    # 创建Q弹放大动画
    tween = create_tween()
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.set_ease(Tween.EASE_OUT)
    
    # 从小到大的Q弹放大动画
    tween.tween_property(feather_node, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 等待放大动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.FEATHER_BURN)

# 播放卷轴显示动画
func play_scroll_show_animation(scroll_panel):
    if not scroll_panel:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCROLL_SHOW)
    
    # 设置初始状态
    scroll_panel.modulate.a = 0
    scroll_panel.scale = Vector2(0.1, 0.1)
    scroll_panel.pivot_offset = scroll_panel.size / 2
    
    # 创建显示动画
    var tween = create_tween()
    tween.set_parallel(true)
    tween.set_trans(Tween.TRANS_BACK)
    tween.set_ease(Tween.EASE_OUT)
    
    # 淡入和缩放动画
    tween.tween_property(scroll_panel, "modulate:a", 1.0, get_animation_duration(0.3))
    tween.tween_property(scroll_panel, "scale", Vector2(1.0, 1.0), get_animation_duration(0.3))
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.SCROLL_SHOW)

# 播放卷轴隐藏动画
func play_scroll_hide_animation(scroll_panel):
    if not scroll_panel:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCROLL_HIDE)
    
    # 创建隐藏动画
    var tween = create_tween()
    tween.set_parallel(true)
    tween.set_trans(Tween.TRANS_BACK)
    tween.set_ease(Tween.EASE_IN)
    
    # 淡出和缩放动画
    tween.tween_property(scroll_panel, "modulate:a", 0.0, get_animation_duration(0.3))
    tween.tween_property(scroll_panel, "scale", Vector2(0.1, 0.1), get_animation_duration(0.3))
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.SCROLL_HIDE)

# 播放卷轴浮动动画
func play_scroll_float_animation(scroll_node):
    if not scroll_node:
        return null
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCROLL_FLOAT)
    
    # 创建循环浮动动画（使用递归重启而不是set_loops）
    _start_scroll_float_cycle(scroll_node)

    return scroll_float_tween

# 开始卷轴浮动循环
func _start_scroll_float_cycle(scroll_node):
    if not is_instance_valid(scroll_node):
        return

    # 创建浮动动画
    scroll_float_tween = create_tween()
    scroll_float_tween.set_trans(Tween.TRANS_SINE)
    scroll_float_tween.set_ease(Tween.EASE_IN_OUT)

    # 保存原始位置
    var original_y = scroll_node.position.y -5

    # 上下浮动动画
    scroll_float_tween.tween_property(scroll_node, "position:y", original_y - 5, 1.0)
    scroll_float_tween.parallel().tween_property(scroll_node, "rotation", 0.02, 1.0)
    scroll_float_tween.tween_property(scroll_node, "position:y", original_y + 5, 1.0)
    scroll_float_tween.parallel().tween_property(scroll_node, "rotation", -0.02, 1.0)

    # 当动画完成时，重新开始循环
    scroll_float_tween.finished.connect(_on_scroll_float_cycle_finished.bind(scroll_node))

# 卷轴浮动循环完成回调
func _on_scroll_float_cycle_finished(scroll_node):
    if is_instance_valid(scroll_node) and scroll_float_tween != null:
        _start_scroll_float_cycle(scroll_node)

# 播放卷轴悬停动画
func play_scroll_hover_animation(scroll_node, is_hovering):
    if not scroll_node:
        return null
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCROLL_HOVER)
    
    # 创建悬停动画
    var hover_tween = create_tween()
    hover_tween.set_trans(Tween.TRANS_ELASTIC) # 使用弹性过渡
    hover_tween.set_ease(Tween.EASE_OUT)
    
    if is_hovering:
        # 鼠标悬停时放大并轻微旋转
        hover_tween.set_parallel(true)
        hover_tween.tween_property(scroll_node, "scale", Vector2(1.15, 1.15), 0.5)
    else:
        # 鼠标离开时恢复
        hover_tween.set_parallel(true)
        hover_tween.tween_property(scroll_node, "scale", Vector2(1.0, 1.0), 0.5)
        hover_tween.tween_property(scroll_node, "rotation", 0.0, 0.3)
    
    # 添加完成回调
    hover_tween.finished.connect(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.SCROLL_HOVER)
    )
    
    return hover_tween

# 播放卷轴点击动画
func play_scroll_click_animation(scroll_node):
    if not scroll_node:
        return null
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.SCROLL_CLICK)
    
    # 创建点击动画
    var click_tween = create_tween()
    click_tween.set_trans(Tween.TRANS_ELASTIC) # 使用弹性过渡
    click_tween.set_ease(Tween.EASE_OUT)
    
    # 点击时的Q弹压缩和旋转效果
    click_tween.set_parallel(true)
    # 快速压缩
    click_tween.tween_property(scroll_node, "scale", Vector2(0.85, 0.85), 0.15)
    click_tween.tween_property(scroll_node, "rotation", -0.1, 0.15)
    
    # 弹回并略微超出
    click_tween.chain().set_parallel(true)
    click_tween.tween_property(scroll_node, "scale", Vector2(1.2, 1.2), 0.35)
    click_tween.tween_property(scroll_node, "rotation", 0.15, 0.35)
    
    # 最终回到悬停状态
    click_tween.chain().set_parallel(true)
    click_tween.tween_property(scroll_node, "scale", Vector2(1.15, 1.15), 0.25)
    click_tween.tween_property(scroll_node, "rotation", 0.05, 0.25)
    
    # 添加完成回调
    click_tween.finished.connect(func():
        is_animation_playing = false
        emit_signal("animation_finished", AnimationType.SCROLL_CLICK)
    )
    
    return click_tween

# 播放BUFF图标浮动动画
func play_buff_icon_float_animation(ball_node: Control):
    if not ball_node:
        return null
    
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.BUFF_ICON_FLOAT)
    
    # 创建循环浮动动画（使用递归重启而不是set_loops）
    _start_buff_icon_float_cycle(ball_node)

    return buff_icon_float_tween

# 开始BUFF图标浮动循环
func _start_buff_icon_float_cycle(ball_node):
    if not is_instance_valid(ball_node):
        return

    # 创建新的浮动动画
    buff_icon_float_tween = create_tween()
    buff_icon_float_tween.set_trans(Tween.TRANS_SINE)
    buff_icon_float_tween.set_ease(Tween.EASE_IN_OUT)

    # 优化动画序列
    buff_icon_float_tween.set_parallel(false) # 确保动画按顺序执行

    # 在最高点保持一段时间
    buff_icon_float_tween.tween_interval(0.4)

    # 在最低点保持一段时间，然后开始翻转序列
    buff_icon_float_tween.tween_interval(0.4)

    # 完整的翻转序列（快速翻转）
    var flip_sequence = buff_icon_float_tween.chain()
    flip_sequence.set_trans(Tween.TRANS_QUAD)
    flip_sequence.tween_property(ball_node, "scale:x", -1.0, 0.3) # 翻转到背面
    flip_sequence.tween_property(ball_node, "scale:x", 1.0, 0.3)  # 翻转回正面

    # 等待一段时间再开始下一个循环
    buff_icon_float_tween.tween_interval(4.0) # 间隔4秒后再次开始循环

    # 当动画完成时，重新开始循环
    buff_icon_float_tween.finished.connect(_on_buff_icon_float_cycle_finished.bind(ball_node))

# BUFF图标浮动循环完成回调
func _on_buff_icon_float_cycle_finished(ball_node):
    if is_instance_valid(ball_node) and buff_icon_float_tween != null:
        _start_buff_icon_float_cycle(ball_node)

# 播放BUFF图标出现动画
func play_buff_icon_spawn_animation(buff_icon: Control) -> void:
    if not buff_icon:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.BUFF_ICON_SPAWN)
    
    # 设置初始状态
    buff_icon.modulate.a = 0
    buff_icon.scale = Vector2.ZERO
    buff_icon.pivot_offset = buff_icon.size / 2
    
    # 创建出现动画
    var spawn_tween = create_tween()
    spawn_tween.set_trans(Tween.TRANS_ELASTIC)
    spawn_tween.set_ease(Tween.EASE_OUT)
    spawn_tween.set_parallel(true)
    
    # 弹出效果
    spawn_tween.tween_property(buff_icon, "scale", Vector2(1.2, 1.2), 0.3)
    spawn_tween.tween_property(buff_icon, "modulate:a", 1.0, 0.1)
    
    # 等待弹出完成后缩回正常大小
    await spawn_tween.finished
    
    var settle_tween = create_tween()
    settle_tween.set_trans(Tween.TRANS_BOUNCE)
    settle_tween.set_ease(Tween.EASE_OUT)
    settle_tween.tween_property(buff_icon, "scale", Vector2(1.0, 1.0), 0.3)
    
    # 播放出现音效
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/bubble2.mp3")
    
    await settle_tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.BUFF_ICON_SPAWN)

# 播放永久BUFF区域的Q弹膨胀动画
func play_permanent_buff_bounce_animation(buff_type: String):
    # 获取永久BUFF区域
    var forever_buff_area = null
    var scene_tree = get_tree()
    if scene_tree and scene_tree.current_scene:
        forever_buff_area = scene_tree.current_scene.get_node_or_null("ForeverBuffArea")

    if not forever_buff_area:
        return

    # 查找对应类型的BUFF图标
    var buff_icon = _find_buff_icon_by_type(forever_buff_area, buff_type)
    if not buff_icon:
        return

    # 播放Q弹膨胀动画
    var bounce_tween = create_tween()
    bounce_tween.set_trans(Tween.TRANS_ELASTIC)
    bounce_tween.set_ease(Tween.EASE_OUT)

    # 获取ball节点（实际的图标容器）
    var ball_node = buff_icon.get_node_or_null("Ball")
    if ball_node:
        # 膨胀效果：先放大到1.4倍，然后回到正常状态
        var original_scale = ball_node.scale

        # 创建序列动画（不使用parallel）
        bounce_tween.tween_property(ball_node, "scale", original_scale * 1.4, 0.2)
        bounce_tween.tween_property(ball_node, "scale", original_scale, 0.4)

        # 添加颜色闪烁效果（并行）
        var color_tween = create_tween()
        color_tween.set_parallel(true)
        var original_modulate = ball_node.modulate
        color_tween.tween_property(ball_node, "modulate", Color(1.2, 1.2, 0.8, 1.0), 0.1)
        color_tween.tween_property(ball_node, "modulate", original_modulate, 0.5)

# 根据BUFF类型查找对应的BUFF图标
func _find_buff_icon_by_type(forever_buff_area: Control, buff_type: String) -> Control:
    var buff_container = forever_buff_area.get_node_or_null("BuffContainer")
    if not buff_container:
        return null

    # 遍历所有BUFF图标
    for child in buff_container.get_children():
        if "buff_data" in child and child.buff_data is Dictionary:
            if child.buff_data.has("type") and child.buff_data.type == buff_type:
                return child

    return null

# 播放BUFF卡牌飞向buff_display区域的动画
func play_buff_card_fly_animation(card_data, start_position: Vector2, target_position: Vector2, duration: float = 0.2) -> void:
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.BUFF_CARD_FLY)

    AudioManager.play_sfx("res://assert/audio/sfx/whip_shot.wav")
    
    # 创建一个临时卡牌节点用于动画
    var temp_card = card_node_scene.instantiate()
    get_tree().get_root().add_child(temp_card)
    
    # 设置临时卡牌数据和位置
    temp_card.set_card_data(card_data, false, -1)  # 使用false表示这不是手牌
    temp_card.global_position = start_position
    temp_card.z_index = 100  # 确保动画在最上层显示
    temp_card.pivot_offset = Vector2(0, temp_card.size.y / 2)  # 设置缩放中心点为左边中心
    
    # 创建Tween动画
    var tween = create_tween()
    tween.set_parallel(true)  # 允许并行动画
    tween.set_ease(Tween.EASE_IN)  # 使用EASE_IN使动画在结束时加速
    tween.set_trans(Tween.TRANS_CUBIC)  # 使用CUBIC使动画更流畅
    
    # 设置移动动画
    tween.tween_property(temp_card, "global_position", target_position, duration)
    
    # 创建路径跟随动画
    var progress = 0.0
    var original_height = temp_card.size.y
    
    while progress < 1.0:
        progress += get_process_delta_time() / duration
        progress = min(progress, 1.0)
        
        # 第一阶段：上下压缩（0-0.3）
        if progress < 0.3:
            var compress_progress = progress / 0.3
            temp_card.scale.y = 1.0 - (compress_progress * 0.7)  # 压缩到原高度的30%
            temp_card.scale.x = 1.0  # 保持宽度不变
        # 第二阶段：变成细线（0.3-0.7）
        elif progress < 0.7:
            var line_progress = (progress - 0.3) / 0.4
            temp_card.scale.y = 0.3 - (line_progress * 0.25)  # 从30%继续压缩到5%
            temp_card.scale.x = 1.0 - (line_progress * 0.9)  # 开始压缩宽度到10%
        # 第三阶段：完全消失（0.7-1.0）
        else:
            var fade_progress = (progress - 0.7) / 0.3
            temp_card.scale.y = 0.05 * (1.0 - fade_progress)  # 从5%缩小到0
            temp_card.scale.x = 0.1 * (1.0 - fade_progress)  # 从10%缩小到0
            temp_card.modulate.a = 1.0 - fade_progress  # 同时淡出
        
        await get_tree().process_frame
    
    # 删除临时卡牌节点
    temp_card.queue_free()
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.BUFF_CARD_FLY)

# 播放卡牌抖动动画
func play_card_shake_animation(card_node, intensity = 1.0, duration = 0.3):
    if not card_node:
        return null
    
    # 保存原始位置、旋转和缩放
    var original_position = card_node.position
    var original_rotation = card_node.rotation
    var original_scale = card_node.scale
    
    # 设置旋转中心点为卡牌中心
    card_node.pivot_offset = card_node.size / 2
    
    # 创建抖动动画
    var tween = create_tween()
    tween.set_parallel(true)
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 位置抖动
    var shake_offset = 5.0 * intensity
    for i in range(4):  # 4次快速抖动
        var offset = shake_offset * (1.0 - i/4.0)  # 抖动幅度逐渐减小
        var time = duration / 4.0  # 每次抖动的时间
        
        # 水平抖动
        tween.tween_property(card_node, "position:x", 
            original_position.x + (offset if i % 2 == 0 else -offset), 
            time).set_trans(Tween.TRANS_SINE)
        
        # 垂直抖动（较小）
        tween.tween_property(card_node, "position:y",
            original_position.y + (offset * 0.3 if i % 2 == 0 else -offset * 0.3),
            time).set_trans(Tween.TRANS_SINE)
    
    # 旋转抖动
    var rotation_offset = 0.05 * intensity  # 减小旋转角度
    for i in range(4):
        var angle = rotation_offset * (1.0 - i/4.0)
        tween.tween_property(card_node, "rotation",
            original_rotation + (angle if i % 2 == 0 else -angle),
            duration / 4.0).set_trans(Tween.TRANS_SINE)
    
    # 添加轻微的缩放效果增强视觉冲击
    tween.tween_property(card_node, "scale", 
        original_scale * 1.1, duration * 0.2).set_trans(Tween.TRANS_SINE)
    
    # 最后回到原始状态
    tween.chain().tween_property(card_node, "position", original_position, duration * 0.2)
    tween.parallel().tween_property(card_node, "rotation", original_rotation, duration * 0.2)
    tween.parallel().tween_property(card_node, "scale", original_scale, duration * 0.2)
    
    return tween

# 播放阶进度条动画
func play_round_progress_animation(progress_bar, progress_label, old_progress: float, new_progress: float, duration: float = 0.5) -> void:
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.ROUND_PROGRESS)
    
    # 获取实际动画时间
    duration = get_animation_duration(duration)
    
    # 创建主动画tween
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 进度条值动画
    tween.tween_property(progress_bar, "value", new_progress, duration)
    
    # 进度条shader动画
    if progress_bar.material:
        tween.parallel().tween_method(
            func(value): progress_bar.material.set_shader_parameter("progress", value),
            old_progress,
            new_progress,
            duration
        )
    
    # 创建视觉反馈动画
    var feedback_tween = create_tween()
    feedback_tween.set_ease(Tween.EASE_OUT)
    feedback_tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 进度条缩放动画
    feedback_tween.tween_property(progress_bar, "scale", Vector2(1.2, 1.3), duration * 0.5)
    feedback_tween.tween_property(progress_bar, "scale", Vector2(1.0, 1.0), duration * 0.5)
    
    # 如果进度增加，添加发光效果
    if new_progress > old_progress:
        var style = progress_bar.get_theme_stylebox("fill")
        if style:
            var original_color = style.bg_color
            var glow_color = Color(original_color.r * 1.5, original_color.g * 1.5, original_color.b * 1.5, original_color.a)
            
            var color_tween = create_tween()
            color_tween.tween_property(style, "bg_color", glow_color, duration * 0.3)
            color_tween.tween_property(style, "bg_color", original_color, duration * 0.7)
    
    # 等待所有动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.ROUND_PROGRESS)

# 播放按钮心跳动画
func play_button_heartbeat(button: Control, button_shadow: Control = null, is_disabled: bool = false, is_processing: bool = false, is_hovered: bool = false):
    # 只有在按钮可用、没有其他动画播放，且鼠标不在按钮上时进行心跳
    if is_disabled or is_processing or is_hovered:
        return
        
    # 标记动画开始
    is_animation_playing = true
    emit_signal("animation_started", AnimationType.BUTTON_HEARTBEAT)
    
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 确保按钮和阴影的pivot_offset设置正确，使缩放和旋转动画以图片中心为基准
    button.pivot_offset = button.size / 2
    
    # 存储阴影的原始Y位置（如果有阴影的话）
    var origin_pos_y = 0.0
    if button_shadow:
        button_shadow.pivot_offset = button_shadow.size / 2
        origin_pos_y = button_shadow.position.y
        
    # 创建一个序列动画
    # 1. 先向右倾斜并放大
    tween.tween_property(button, "rotation", 0.02, 0.25)
    tween.parallel().tween_property(button, "scale", Vector2(1.1, 1.1), 0.5)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "rotation", 0.02, 0.25)
        tween.parallel().tween_property(button_shadow, "scale", Vector2(1.05, 1.05), 0.5)
        tween.parallel().tween_property(button_shadow, "modulate:a", 0.298, 0.5)
        tween.parallel().tween_property(button_shadow, "position:y", origin_pos_y + 10, 0.5)
    
    # 2. 然后向左倾斜
    tween.tween_property(button, "rotation", -0.02, 0.5)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "rotation", -0.02, 0.5)
    
    # 3. 最后恢复正常位置和大小
    tween.tween_property(button, "rotation", 0, 0.25)
    tween.parallel().tween_property(button, "scale", Vector2(1.0, 1.0), 0.5)
    if button_shadow:
        tween.parallel().tween_property(button_shadow, "rotation", 0, 0.25)
        tween.parallel().tween_property(button_shadow, "scale", Vector2(1.0, 1.0), 0.5)
        tween.parallel().tween_property(button_shadow, "modulate:a", 0.498, 0.5)
        tween.parallel().tween_property(button_shadow, "position:y", origin_pos_y, 0.5)
    
    # 等待动画完成
    await tween.finished
    
    # 标记动画结束
    is_animation_playing = false
    emit_signal("animation_finished", AnimationType.BUTTON_HEARTBEAT)

# 播放积分警告动画（心跳效果）
func play_score_warning_animation(score_label: Label) -> void:
    if not score_label or not is_instance_valid(score_label):
        return
        
    # 保存原始颜色和大小
    var original_color = score_label.get_theme_color("font_color", "Label")
    var original_scale = score_label.scale
    score_label.pivot_offset = score_label.size / 2
    
    # 创建心跳动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 第一次心跳
    tween.tween_property(score_label, "theme_override_colors/font_color", Color(1, 0, 0, 1), 0.2) # 变红
    tween.parallel().tween_property(score_label, "scale", Vector2(1.3, 1.3), 0.2) # 放大
    tween.tween_property(score_label, "scale", Vector2(1.0, 1.0), 0.4) # 恢复大小
    
    # 短暂暂停
    tween.tween_interval(0.2)
    
    # 第二次心跳
    tween.tween_property(score_label, "scale", Vector2(1.3, 1.3), 0.2) # 放大
    tween.tween_property(score_label, "scale", Vector2(1.0, 1.0), 0.4) # 恢复大小
    
    # 恢复原始颜色
    tween.tween_property(score_label, "theme_override_colors/font_color", original_color, 0.2)
    
    # 确保动画结束后完全恢复原始状态
    tween.tween_callback(func():
        if is_instance_valid(score_label):
            score_label.scale = original_scale
            score_label.add_theme_color_override("font_color", original_color)
    )

# 播放阶数标签动画
func play_rounds_label_animation(rounds_label: Label):
    if not rounds_label or not is_instance_valid(rounds_label) and rounds_label.visible:
        return null
        
    # 保存原始大小和旋转
    var original_scale = rounds_label.scale
    var original_rotation = rounds_label.rotation
    var original_position = rounds_label.position
    
    # 确保标签有旋转中心点
    rounds_label.pivot_offset = rounds_label.size / 2
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # Q弹放大并轻微旋转
    tween.tween_property(rounds_label, "scale", original_scale * 2.0, 0.5)
    tween.parallel().tween_property(rounds_label, "rotation", original_rotation + 0.2, 0.5)

    # 恢复原始大小和旋转，带有弹性效果
    tween.tween_property(rounds_label, "scale", original_scale, 0.5)
    tween.parallel().tween_property(rounds_label, "rotation", original_rotation, 0.5)
    
    # 播放音效
    # var audio_manager = get_node_or_null("/root/AudioManager")
    # if audio_manager:
    #     audio_manager.play_sfx("res://assert/audio/sfx/bubble2.mp3")
    
    return tween

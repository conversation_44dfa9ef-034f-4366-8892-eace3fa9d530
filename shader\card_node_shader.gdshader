shader_type canvas_item;

// 效果开关
uniform bool enable_float_effect = false;      // 浮动效果开关
uniform bool enable_hover_effect = false;      // 悬停效果开关
uniform bool enable_buff_effect = false;       // BUFF特效开关
uniform bool enable_dissolve_effect = false;   // 溶解效果开关
uniform bool enable_ripple_effect = false;     // 波纹效果开关
uniform bool enable_shine_effect = false;      // 闪光效果开关
uniform bool enable_gold_flash_effect = false; // 金色闪光效果开关
uniform bool enable_forbidden_effect = false;  // 禁止效果开关
uniform bool enable_3d_effect = false;        // 3D效果开关
uniform bool enable_border_effect = false;     // 边框效果开关
uniform bool enable_shadow_effect = false;     // 阴影效果开关

// ===== 阴影效果参数 =====
uniform vec2 shadow_offset = vec2(10.0, 10.0);  // 阴影偏移（像素）
uniform vec4 shadow_color : source_color = vec4(0.0, 0.0, 0.0, 0.3);  // 阴影颜色（RGBA）
uniform bool is_shadow_pass = false;  // 是否是阴影渲染过程
varying float sprite_rotation;  // 用于存储精灵旋转角度

// ===== 边框效果参数 =====
uniform sampler2D border_noise_texture: repeat_enable;  // 边框噪声纹理
uniform float border_radius: hint_range(0.0, 1.0) = 0.45;  // 边框半径
uniform float border_effect_control: hint_range(0.0, 1.0) = 0.6;  // 边框效果控制
uniform float border_burn_speed: hint_range(0.0, 1.0) = 0.7;  // 边框燃烧速度
uniform float border_shape: hint_range(0.0, 1.0) = 1.0;  // 边框形状混合

// ===== 浮动效果参数 =====
uniform float float_amplitude : hint_range(0.0, 10.0) = 3.0;  // 浮动幅度
uniform float float_frequency : hint_range(0.1, 5.0) = 1.0;   // 浮动频率
uniform float time_offset = 0.0;                            // 时间偏移，用于错开多个卡牌的浮动

// ===== 禁止效果参数 =====
uniform sampler2D forbidden_texture;       // 禁止图标纹理
uniform float forbidden_opacity = 0.4;     // 禁止图标不透明度
uniform float forbidden_scale = 0.7;       // 禁止图标缩放
uniform float forbidden_pulse_speed = 3.0; // 禁止图标脉动速度
uniform float forbidden_pulse_min = 0.6;   // 最小脉动强度
uniform float forbidden_pulse_max = 1.2;   // 最大脉动强度

// ===== 悬停效果参数 =====
uniform float hover_scale : hint_range(1.0, 1.5) = 1.2;       // 悬停时的放大系数
uniform bool is_hovered = false;                            // 是否被悬停
uniform float transition_speed : hint_range(1.0, 10.0) = 5.0; // 过渡速度
uniform float current_scale = 1.0;                          // 当前缩放值（在代码中更新）

// ===== BUFF特效参数 =====
/** Noise used for the glitter shape */
uniform sampler2D buff_noise_texture: repeat_enable, filter_linear_mipmap;
uniform sampler2D buff_texture;
uniform vec4 glitter_color : source_color = vec4(1.0,1.0,1.0,1.0);
uniform float glitter_size: hint_range(0.0, 1.0, 0.01) = 0.3;
uniform float glitter_hardness: hint_range(0.0, 1.0, 0.01) = 0.7;
uniform float highlight_speed = 2.0;
uniform float highlight_intensity: hint_range(0.0, 1.0, 0.05) = 1;
uniform bool highlight_band = true;

// --- NEW BUFF Overlay Params (Added earlier) ---
uniform vec4 buff_color : source_color = vec4(1.0, 1.0, 1.0, 1.0); // Buff 颜色 (包含 alpha)
uniform sampler2D buff_noise;         // Buff 噪声纹理
uniform float buff_tile_scale = 1.0;    // Buff 纹理平铺缩放
uniform float buff_noise_influence = 0.1; // Buff 噪声影响强度
uniform bool enable_buff_overlay = false; // BUFF 叠加效果开关

// ===== 溶解效果参数 =====
uniform float dissolve_progress : hint_range(0.0, 1.0) = 0.0;
uniform sampler2D dissolve_noise_texture : source_color, filter_linear_mipmap;

// ===== 波纹效果参数 =====
uniform float ripple_frequency: hint_range(0, 15, 0.01) = 2.6;
uniform float ripple_amplitude: hint_range(0, 3, 0.1) = 0.9;
uniform float ripple_rate : hint_range(0, 20.0, 1) = 2;
uniform float wave_amplitude: hint_range(0.001, 0.1, 0.001) = 0.03;
uniform float wave_frequency: hint_range(0, 15, 0.01) = 8.0;
uniform sampler2D ripple_noise;
uniform sampler2D SCREEN_TEXTURE: hint_screen_texture, filter_linear_mipmap;
uniform vec4 ripple_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);

// ===== 金色闪光效果参数 =====
uniform float flash_speed : hint_range(0.1, 10.0) = 3.5;  // 闪烁速度
uniform float min_intensity : hint_range(0.0, 1.0) = 0.4;  // 最小闪烁强度
uniform float max_intensity : hint_range(0.5, 2.0) = 1.2;  // 最大闪烁强度
uniform vec4 gold_color : source_color = vec4(1.0, 0.84, 0.0, 0.8);  // 金色
uniform float edge_width : hint_range(0.0, 0.5) = 0.2;  // 边缘宽度

// ====== 闪光效果参数 =====
uniform sampler2D surface: source_color;  // 闪光效果表面纹理
uniform float Line_Smoothness : hint_range(0, 0.1) = 0.045;
uniform float Line_Width : hint_range(0, 0.2) = 0.09;
uniform float Brightness = 3.0;
uniform float Rotation_deg : hint_range(-90, 90) = 30;
uniform float Distortion : hint_range(1, 2) = 1.8;
uniform float Speed = 0.4;
uniform float Position : hint_range(0, 1) = 0;
uniform float Position_Min = 0.25;
uniform float Position_Max = 0.5;
uniform float Alpha : hint_range(0, 1) = 0.8;

// ===== 3D效果参数 =====
uniform float card_width = 120;              // 卡牌宽度
uniform float card_height = 180;             // 卡牌高度
uniform vec2 mouse_position = vec2(0, 0);    // 鼠标位置

// 用于BUFF效果的变量
varying vec2 local_vert;

// 阴影效果辅助函数
vec2 rotate_point(vec2 point, float angle) {
    float s = sin(angle);
    float c = cos(angle);
    return vec2(
        point.x * c - point.y * s,
        point.x * s + point.y * c
    );
}

// 波纹效果辅助函数
vec2 wave(vec2 uv, float time) {
    return vec2(
        uv.x + sin(uv.y * wave_frequency + time) * wave_amplitude,
        uv.y + sin(uv.x * wave_frequency + time) * wave_amplitude
    );
}

void vertex() {
    // 保存原始顶点位置（用于BUFF效果）
    local_vert = VERTEX;

    // 计算精灵旋转角度（用于阴影效果）
    sprite_rotation = atan(MODEL_MATRIX[0][1], MODEL_MATRIX[0][0]);

    // 阴影效果处理
    if (enable_shadow_effect && is_shadow_pass) {
        // 只在阴影渲染过程中应用偏移
        VERTEX.xy += rotate_point(shadow_offset, -sprite_rotation);
    }

    // 浮动效果
    if (enable_float_effect) {
        float time = TIME * float_frequency + time_offset;
        float offset = sin(time) * float_amplitude;

        // 只在Y轴上应用浮动
        VERTEX.y += offset;

        // 添加轻微的旋转效果
        float rotation = sin(time) * 0.01; // 非常轻微的旋转
        vec2 center = vec2(0.5, 0.5);
        vec2 centered_vertex = VERTEX - center;
        float s = sin(rotation);
        float c = cos(rotation);
        VERTEX.x = centered_vertex.x * c - centered_vertex.y * s + center.x;
        VERTEX.y = centered_vertex.x * s + centered_vertex.y * c + center.y;
    }

    // 悬停效果
    if (enable_hover_effect && current_scale > 1.0) {
        // 从中心点缩放
        vec2 center = vec2(0.5, 0.5);
        VERTEX = (VERTEX - center) * current_scale + center;
    }
}

vec2 rotate_uv(vec2 uv, vec2 center, float rotation, bool use_degrees){
		float _angle = rotation;
		if(use_degrees){
			_angle = rotation * (3.1415926/180.0);
		}
		mat2 _rotation = mat2(
			vec2(cos(_angle), -sin(_angle)),
			vec2(sin(_angle), cos(_angle))
		);
		vec2 _delta = uv - center;
		_delta = _rotation * _delta;
		return _delta + center;
	}

void fragment() {
    // 获取原始纹理颜色
    vec4 original_color = texture(TEXTURE, UV);
    vec4 final_color = original_color;
    vec2 final_uv = UV;

    // 阴影效果处理
    if (enable_shadow_effect && is_shadow_pass) {
        // 在阴影渲染过程中，将所有非透明像素转换为阴影颜色
        final_color = vec4(shadow_color.rgb, original_color.a * shadow_color.a);
    } else {
        // 边框效果
        if (enable_border_effect) {
            vec2 centerDistVec = vec2(0.5) - final_uv;

            float distToCircleEdge = length(centerDistVec) * border_radius;
            float distToSquareEdge = 0.5*(0.5 - min(min(final_uv.x, 1.0 - final_uv.x), min(final_uv.y, 1.0 - final_uv.y)));
            float distToEdge = mix(distToCircleEdge, distToSquareEdge, border_shape);

            float gradient = smoothstep(0.5, 0.5 - border_radius, distToEdge);

            vec2 direction = vec2(0, 1) * border_burn_speed;
            float noiseValue = texture(border_noise_texture, final_uv + direction * TIME).r;

            float opacity = step(border_radius, mix(gradient, noiseValue, border_effect_control) - distToEdge);
            final_color *= vec4(1.0, 1.0, 1.0, opacity);
        }

        // 应用边缘模糊（浮动效果的一部分）
        if (enable_float_effect) {
            float edge = smoothstep(0.0, 0.01, final_uv.x) * smoothstep(0.0, 0.01, final_uv.y) *
                         smoothstep(0.0, 0.01, 1.0 - final_uv.x) * smoothstep(0.0, 0.01, 1.0 - final_uv.y);
            final_color.a *= edge;
        }
    }

    COLOR = final_color;
}
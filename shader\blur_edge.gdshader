shader_type canvas_item;

uniform float blur_amount : hint_range(0.0, 5.0) = 1.0;
uniform float edge_threshold : hint_range(0.0, 1.0) = 0.2;

void fragment() {
    vec2 texture_size = 1.0 / TEXTURE_PIXEL_SIZE;
    vec2 uv = UV;
    
    // Calculate distance from center
    vec2 center = vec2(0.5, 0.5);
    float dist = distance(uv, center);
    
    // Sample original color
    vec4 original = texture(TEXTURE, uv);
    
    // Only blur edges
    float blur_factor = smoothstep(0.0, edge_threshold, dist);
    
    if (blur_factor > 0.0) {
        vec4 blurred = vec4(0.0);
        float total_weight = 0.0;
        
        // Simple gaussian blur
        for(float x = -2.0; x <= 2.0; x += 1.0) {
            for(float y = -2.0; y <= 2.0; y += 1.0) {
                vec2 offset = vec2(x, y) * blur_amount / texture_size;
                float weight = exp(-dot(offset, offset));
                blurred += texture(TEXTURE, uv + offset) * weight;
                total_weight += weight;
            }
        }
        
        blurred /= total_weight;
        
        // Mix between original and blurred based on distance from center
        COLOR = mix(original, blurred, blur_factor);
    } else {
        COLOR = original;
    }
} 
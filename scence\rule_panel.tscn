[gd_scene load_steps=3 format=3 uid="uid://de33uixbyqdd6"]

[ext_resource type="Script" uid="uid://cxemjabf406ry" path="res://script/rule_panel.gd" id="1_rule"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.2, 0.2, 0.2, 0.9)
corner_radius_top_left = 25
corner_radius_top_right = 25
corner_radius_bottom_right = 25
corner_radius_bottom_left = 25
shadow_color = Color(0, 0, 0, 0.5)
shadow_size = 15
shadow_offset = Vector2(2, 2)

[node name="RulePanel" type="Panel"]
z_index = 80
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -500.0
offset_top = -350.0
offset_right = 500.0
offset_bottom = 350.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_panel")
script = ExtResource("1_rule")

[node name="Background" type="ColorRect" parent="."]
modulate = Color(1, 1, 0, 1)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.2, 0)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 40
text = "游戏规则"
horizontal_alignment = 1

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="RuleContent" type="RichTextLabel" parent="VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/table_v_separation = 10
theme_override_constants/table_h_separation = 15
theme_override_font_sizes/normal_font_size = 20
theme_override_font_sizes/bold_font_size = 22
bbcode_enabled = true
text = "
"
fit_content = true

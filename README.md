项目结构：

1.文件夹结构
    assert 为资源文件夹，其下存放游戏所需各种资源，包括音频，图像等
    fonts 为字体文件夹
    main 暂时没有用到
    noise 为噪声纹理文件夹
    scence 为游戏场景文件夹，游戏中各类场景都存在这里
    script 为游戏代码文件夹，所有以.gd结尾的代码文件都存放在这里
    shader 为特效文件夹，所有.gdshader结尾的代码都存放在这里
    temp 为临时文件夹，输出的临时文件可放置此处
    text 为文本文件夹，存放游戏的设计蓝图，构思，准备开发项，计划，游戏规则等各种游戏相关的文本文件
    themes 为主题文件夹，存放游戏主题文件
2.代码结构
    主要代码在script文件夹下，有如下文件：
        script\animation_manager.gd 动画管理器，所有游戏中所用到的动画都交给这个manager管理，包括卡牌的移动旋转，文字的跳跃等。
        script\audio_manager.gd 音频管理器，所有游戏中所用到的BGM或者SFX都有此代码统一管理，包括游戏各个场景的背景音乐的播放切换以及各种音效的播放，还有音量的大小调整等。
        script\buff_display.gd BUFF展示区相关的代码，BUFF在游戏主场景的左边，非一次性BUFF卡牌的增减展示都由此代码管理。
        script\buff_manager.gd BUFF统一管理器，所有游戏中卡牌BUFF相关都交由此代码管理
        script\card_detail_panel.gd 卡牌详情页代码，和场景相关联，控制场景展示。在游戏主场景点击鼠标右键即可出现卡牌详情页。
        script\card_node.gd 卡牌节点代码，和卡牌节点场景相关联，控制节点展示。游戏中所有的卡牌展示相关都交由卡牌节点统一管理。
        script\card_system.gd 卡牌系统代码，卡牌的游戏规则计算，卡牌的生成以及消亡逻辑，洗牌逻辑等。通过游戏规则对卡牌进行管理。
        script\cursor_manager.gd 光标管理器，管理鼠标光标的所有动画，图像等内容。
        script\deck_button_particles.gd 发牌按钮的粒子特效，这个后续会被融入到粒子特效管理中,现在没有实际作用。
        script\dice_panel.gd 骰子页面代码，和骰子页面关联，控制骰子页面展示以及掷骰子以及分数的逻辑。
        script\game_manager.gd 游戏管理，游戏状态机切换，整体游戏进度管理。
        script\game_over_panel.gd 结算页面代码，和结算页面场景关联，结算页面的展示，游戏结束时进行展示。
        script\game_scene_full.gd 主游戏代码，和主游戏场景关联，控制主要游戏场景的逻辑，这里有一部分代码并未解耦合导致代码量庞杂，后续需要整体整体代码结构，这里的代码仅负责主要游戏场景逻辑，其他部分需要拆分到其他管理模块。
        script\card_landing_particles.gd 卡牌落地粒子特效，这个后续也会被融入到粒子特效管理中，现在没有实际作用。
        script\main_menu.gd 主菜单页面代码，和主菜单场景关联，进入游戏的第一界面。
        script\rule_panel.gd 规则菜单，和规则菜单场景关联。
        script\setting_panel.gd 设置页面，和设置页面场景关联。
        script\tooltip_bubble.gd 提示弹框代码，所有游戏中需要用到的系统级别的弹框都统一由此管理。
        script\top_bar_manager.gd 主游戏页面分离出来的代码，主要负责顶部栏所有的显示以及逻辑，使原game_scene_full代码更加轻量化。
        script\ui_animation_manager.gd 所有UI元素的动画统一管理器。
        script\ui_manager.gd 所有UI元素的统一管理器。
        script\deck_discard_area_manager.gd 抽牌弃牌管理器，抽牌弃牌组件的所有操作交由此管理。






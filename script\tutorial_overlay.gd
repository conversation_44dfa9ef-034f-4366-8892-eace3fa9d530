extends CanvasLayer

signal tutorial_completed

const TUTORIAL_SECTION = "Tutorial"
const TUTORIAL_COMPLETED_KEY = "completed"
const PANEL_MARGIN = 20  # 面板与高亮节点之间的间距
const TUTORIAL_STEPS_PATH = "res://data/tutorial_steps.json"
const MIN_PANEL_WIDTH = 300  # 最小面板宽度
const MIN_PANEL_HEIGHT = 200  # 最小面板高度
const PANEL_PADDING = 20  # 面板内边距

var current_step := 0
var tutorial_steps := []
var current_language := "zh_CN"  # 默认语言

# 用于跟踪鼠标拖动状态
var is_dragging := false
var drag_start_pos := Vector2()
var drag_distance_sufficient := false  # 新增变量，用于跟踪拖动距离是否足够
const MIN_DRAG_DISTANCE := 50  # 最小拖动距离，可以根据需要调整

@onready var tutorial_text: RichTextLabel = $TutorialPanel/MarginContainer/VBoxContainer/TutorialText
@onready var next_button: Button = $TutorialPanel/MarginContainer/VBoxContainer/NextButton
@onready var tutorial_panel: Panel = $TutorialPanel
@onready var dim_background: ColorRect = $DimBackground
@onready var vbox_container: VBoxContainer = $TutorialPanel/MarginContainer/VBoxContainer

# 用于存储当前高亮的节点
var current_highlight_node: Node = null

func _ready() -> void:
    next_button.text = tr("NEXT")
    next_button.pressed.connect(_on_next_button_pressed)
    dim_background.visible = true
    
    # 设置初始语言并加载教程步骤
    current_language = TranslationServer.get_locale()
    load_tutorial_steps()
    
    # 连接语言变化信号
    #TranslationServer.connect("locale_changed", _on_locale_changed)
    
    # 检查是否已经完成过教程
    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")
    if err == OK:
        if config.has_section_key(TUTORIAL_SECTION, TUTORIAL_COMPLETED_KEY):
            var completed = config.get_value(TUTORIAL_SECTION, TUTORIAL_COMPLETED_KEY)
            if completed:
                queue_free()
                return
    
    show_current_step()

func _on_locale_changed() -> void:
    var new_language = TranslationServer.get_locale()
    if new_language != current_language:
        current_language = new_language
        load_tutorial_steps()
        if current_step < tutorial_steps.size():
            show_current_step()

func load_tutorial_steps() -> void:
    var file = FileAccess.open(TUTORIAL_STEPS_PATH, FileAccess.READ)
    if file:
        var json_text = file.get_as_text()
        file.close()
        
        var json = JSON.new()
        var error = json.parse(json_text)
        if error == OK:
            var data = json.get_data()
            if data.has(current_language):
                tutorial_steps = data[current_language]
            else:
                # 如果找不到当前语言，回退到英语
                if data.has("en"):
                    tutorial_steps = data["en"]
                    push_warning("Language not found in tutorial data: " + current_language + ", falling back to English")
                else:
                    push_error("Neither requested language nor English fallback found in tutorial data")
        else:
            push_error("Failed to parse tutorial steps JSON")
    else:
        push_error("Failed to open tutorial steps file")

func _process(_delta: float) -> void:
    # 如果有高亮节点，实时更新遮罩位置
    if current_highlight_node != null and is_instance_valid(current_highlight_node):
        var rect = current_highlight_node.get_global_rect()
        update_highlight_mask(rect)
    
    # 更新面板大小
    update_panel_size()

func update_panel_size() -> void:
    # 等待一帧以确保内容已经更新
    await get_tree().process_frame
    
    # 获取VBoxContainer的最小尺寸
    var content_size = vbox_container.get_minimum_size()
    
    # 添加内边距
    content_size.x += PANEL_PADDING * 2
    content_size.y += PANEL_PADDING * 2
    
    # 确保不小于最小尺寸
    content_size.x = max(content_size.x, MIN_PANEL_WIDTH)
    content_size.y = max(content_size.y, MIN_PANEL_HEIGHT)
    
    # 设置面板尺寸
    tutorial_panel.custom_minimum_size = content_size
    tutorial_panel.size = content_size
    
    # 如果有高亮节点，重新定位面板
    if current_highlight_node != null and is_instance_valid(current_highlight_node):
        _position_panel_near_node(current_highlight_node.get_path())
    else:
        _center_panel()

func update_highlight_mask(highlight_rect: Rect2) -> void:
    # 创建高亮区域路径（按逆时针方向）
    var mask_path = PackedVector2Array([
        highlight_rect.position,  # 左上
        Vector2(highlight_rect.position.x, highlight_rect.end.y),  # 左下
        highlight_rect.end,  # 右下
        Vector2(highlight_rect.end.x, highlight_rect.position.y),  # 右上
        highlight_rect.position  # 回到左上，封闭路径
    ])
    
    # 更新遮罩
    dim_background.set_meta("mask_path", mask_path)
    dim_background.queue_redraw()

func show_current_step() -> void:
    if current_step == 0:
        next_button.text = tr("BEGIN")
    elif current_step == tutorial_steps.size() - 1:
        next_button.text = tr("COMPLETE")
    elif current_step >= tutorial_steps.size():
        _complete_tutorial()
        return
    else:
         next_button.text = tr("NEXT")
        
    var step = tutorial_steps[current_step]
    tutorial_text.text = step.text
    
    # 如果需要高亮某个节点
    if step.highlight_node != "":
        _highlight_node(step.highlight_node)
        _position_panel_near_node(step.highlight_node)
    else:
        _clear_highlight()
        _center_panel()
    
    # 如果这一步需要玩家操作
    if step.action_required:
        next_button.disabled = true
        next_button.visible = false
        # 连接所需的信号
        if step.has("required_signal"):
            # 断开之前的所有信号连接
            _disconnect_previous_signals()
            
            var signal_path = step.required_signal
            # 特殊处理各种信号
            if signal_path == "discard_cancelled":  # 处理取消弃牌信号
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.discard_area_manager:
                    if not game_scene.discard_area_manager.is_pending_deletion_cancelled.is_connected(_on_required_button_pressed):
                        game_scene.discard_area_manager.is_pending_deletion_cancelled.connect(_on_required_button_pressed)
                return
            elif signal_path == "mouseDrag":
                # 不需要连接信号，会在_input中处理
                return
            elif signal_path == "card_drawn":  # 处理抽牌信号
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.deck_discard_manager:
                    if not game_scene.deck_discard_manager.card_drawn.is_connected(_on_required_button_pressed):
                        game_scene.deck_discard_manager.card_drawn.connect(_on_required_button_pressed)
                    # 同时连接消耗不足信号
                    if not game_scene.deck_discard_manager.insufficient_points.is_connected(_on_required_button_pressed):
                        game_scene.deck_discard_manager.insufficient_points.connect(_on_required_button_pressed)
                return
            elif signal_path == "magic_changed":  # 处理魔法状态变化信号
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.change_chip:
                    # 连接到change_chip的gui_input信号
                    if not game_scene.change_chip.gui_input.is_connected(_on_change_chip_input):
                        game_scene.change_chip.gui_input.connect(_on_change_chip_input)
                return
            elif signal_path == "magic_deck_clicked":  # 处理燃烧状态下点击牌堆
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.deck_discard_manager:
                    # 连接到deck_discard_manager的magic_deck_operation_completed信号
                    if not game_scene.deck_discard_manager.magic_deck_operation_completed.is_connected(_on_required_button_pressed):
                        game_scene.deck_discard_manager.magic_deck_operation_completed.connect(_on_required_button_pressed)
                    # 同时连接消耗不足信号
                    if not game_scene.deck_discard_manager.insufficient_points.is_connected(_on_required_button_pressed):
                        game_scene.deck_discard_manager.insufficient_points.connect(_on_required_button_pressed)
                return
            elif signal_path == "discard_area_clicked":  # 处理点击垃圾桶
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.discard_area:
                    # 连接到discard_area的gui_input信号
                    if not game_scene.discard_area.gui_input.is_connected(_on_discard_area_input):
                        game_scene.discard_area.gui_input.connect(_on_discard_area_input)
                return
            elif signal_path == "card_detail":  # 处理卡牌详情信号
                var game_scene = get_node("/root/GameScene")          
                # 连接玩家区域的卡牌
                if game_scene and game_scene.hand_container:
                    for card_node in game_scene.hand_container.get_children():
                        # 检查节点是否为CardNode类型
                        if card_node.get_script() and card_node.get_script().get_path().ends_with("card_node.gd"):
                            if not card_node.card_right_clicked.is_connected(_on_card_detail_shown):
                                card_node.card_right_clicked.connect(_on_card_detail_shown)
                
                # 连接公共区域的卡牌
                if game_scene and game_scene.pool_container:
                    for card_node in game_scene.pool_container.get_children():
                        # 检查节点是否为CardNode类型
                        if card_node.get_script() and card_node.get_script().get_path().ends_with("card_node.gd"):
                            if not card_node.card_right_clicked.is_connected(_on_card_detail_shown):
                                card_node.card_right_clicked.connect(_on_card_detail_shown)
                return
            elif signal_path == "back_to_game":  # 处理返回游戏信号
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.card_detail_panel:
                    if not game_scene.card_detail_panel.detail_panel_closed.is_connected(_on_required_button_pressed):
                        game_scene.card_detail_panel.detail_panel_closed.connect(_on_required_button_pressed)
                return
            elif signal_path == "rule_back_to_game":  # 处理规则面板返回游戏信号
                var game_scene = get_node("/root/GameScene")
                if game_scene and game_scene.card_rules_panel:
                    if not game_scene.card_rules_panel.rules_panel_closed.is_connected(_on_required_button_pressed):
                        game_scene.card_rules_panel.rules_panel_closed.connect(_on_required_button_pressed)
                return
                
            # 获取目标节点和按钮
            var target_node = get_node("../" + signal_path)
            if target_node is BaseButton:
                if not target_node.pressed.is_connected(_on_required_button_pressed):
                    target_node.pressed.connect(_on_required_button_pressed)
            else:
                push_error("Tutorial target node is not a button: " + signal_path)
    else:
        next_button.disabled = false
        next_button.visible = true

func _position_panel_near_node(node_path: String) -> void:
    # 首先尝试从场景树根节点获取目标节点
    var target_node = get_tree().get_root().get_node_or_null(node_path)
    
    # 如果找不到，尝试从当前节点的父节点开始查找
    if not target_node:
        target_node = get_parent().get_node_or_null(node_path)
    
    # 如果还是找不到，尝试从当前节点开始查找
    if not target_node:
        target_node = get_node_or_null(node_path)
    
    if target_node:
        var node_rect = target_node.get_global_rect()
        var panel_size = tutorial_panel.size
        var viewport_size = get_viewport().get_visible_rect().size
        
        # 计算面板的理想位置（优先显示在右侧）
        var ideal_pos = Vector2()
        
        # 尝试放在右侧
        if node_rect.position.x + node_rect.size.x + panel_size.x + PANEL_MARGIN <= viewport_size.x:
            ideal_pos.x = node_rect.position.x + node_rect.size.x + PANEL_MARGIN
        # 否则尝试放在左侧
        elif node_rect.position.x - panel_size.x - PANEL_MARGIN >= 0:
            ideal_pos.x = node_rect.position.x - panel_size.x - PANEL_MARGIN
        # 如果左右都不行，放在右侧并贴近viewport边缘
        else:
            ideal_pos.x = viewport_size.x - panel_size.x - PANEL_MARGIN
        
        # 垂直位置尽量与节点中心对齐
        ideal_pos.y = node_rect.position.y + (node_rect.size.y - panel_size.y) / 2
        
        # 确保面板不会超出viewport
        ideal_pos.y = clamp(ideal_pos.y, PANEL_MARGIN, viewport_size.y - panel_size.y - PANEL_MARGIN)
        
        # 设置面板位置
        tutorial_panel.global_position = ideal_pos
        
        # 更新当前高亮节点引用
        current_highlight_node = target_node
    else:
        push_warning("Tutorial: Could not find target node: " + node_path)
        _center_panel()

func _center_panel() -> void:
    # 获取viewport大小
    var viewport_size = get_viewport().get_visible_rect().size
    var panel_size = tutorial_panel.size
    
    # 计算中心位置
    var center_pos = (viewport_size - panel_size) / 2
    
    # 设置面板位置
    tutorial_panel.global_position = center_pos

func _highlight_node(node_path: String) -> void:
    # 清除之前的高亮
    _clear_highlight()
    
    # 设置新的高亮节点
    var target_node = get_node("../" + node_path)
    if target_node:
        current_highlight_node = target_node
        var rect = target_node.get_global_rect()
        update_highlight_mask(rect)

func _clear_highlight() -> void:
    current_highlight_node = null
    # 重置遮罩
    dim_background.set_meta("mask_path", PackedVector2Array())
    dim_background.queue_redraw()

func _disconnect_previous_signals() -> void:
    # 获取当前步骤之前的信号配置
    if current_step > 0 and tutorial_steps[current_step - 1].has("required_signal"):
        var previous_signal_path = tutorial_steps[current_step - 1].required_signal
        var game_scene = get_node("/root/GameScene")
        if previous_signal_path == "discard_cancelled":
            # 断开discard_cancelled信号
            if game_scene and game_scene.discard_area_manager and game_scene.discard_area_manager.is_pending_deletion_cancelled.is_connected(_on_required_button_pressed):
                game_scene.discard_area_manager.is_pending_deletion_cancelled.disconnect(_on_required_button_pressed)
            return
        elif previous_signal_path == "card_drawn":
            # 断开card_drawn和insufficient_points信号
            if game_scene and game_scene.deck_discard_manager:
                if game_scene.deck_discard_manager.card_drawn.is_connected(_on_required_button_pressed):
                    game_scene.deck_discard_manager.card_drawn.disconnect(_on_required_button_pressed)
                if game_scene.deck_discard_manager.insufficient_points.is_connected(_on_required_button_pressed):
                    game_scene.deck_discard_manager.insufficient_points.disconnect(_on_required_button_pressed)
            return
        elif previous_signal_path == "magic_changed":
            # 断开change_chip的gui_input信号
            if game_scene and game_scene.change_chip and game_scene.change_chip.gui_input.is_connected(_on_change_chip_input):
                game_scene.change_chip.gui_input.disconnect(_on_change_chip_input)
            return
        elif previous_signal_path == "magic_deck_clicked":
            # 断开magic_deck_operation_completed和insufficient_points信号
            if game_scene and game_scene.deck_discard_manager:
                if game_scene.deck_discard_manager.magic_deck_operation_completed.is_connected(_on_required_button_pressed):
                    game_scene.deck_discard_manager.magic_deck_operation_completed.disconnect(_on_required_button_pressed)
                if game_scene.deck_discard_manager.insufficient_points.is_connected(_on_required_button_pressed):
                    game_scene.deck_discard_manager.insufficient_points.disconnect(_on_required_button_pressed)
            return
        elif previous_signal_path == "discard_area_clicked":
            # 断开discard_area的gui_input信号
            if game_scene and game_scene.discard_area and game_scene.discard_area.gui_input.is_connected(_on_discard_area_input):
                game_scene.discard_area.gui_input.disconnect(_on_discard_area_input)
            return
        elif previous_signal_path == "card_detail":
            # 断开玩家区域的卡牌信号
            if game_scene and game_scene.hand_container:
                for card_node in game_scene.hand_container.get_children():
                    # 检查节点是否为CardNode类型
                    if card_node.get_script() and card_node.get_script().get_path().ends_with("card_node.gd"):
                        if card_node.card_right_clicked.is_connected(_on_card_detail_shown):
                            card_node.card_right_clicked.disconnect(_on_card_detail_shown)
            # 断开公共区域的卡牌信号
            if game_scene and game_scene.pool_container:
                for card_node in game_scene.pool_container.get_children():
                    # 检查节点是否为CardNode类型
                    if card_node.get_script() and card_node.get_script().get_path().ends_with("card_node.gd"):
                        if card_node.card_right_clicked.is_connected(_on_card_detail_shown):
                            card_node.card_right_clicked.disconnect(_on_card_detail_shown)
            return
        elif previous_signal_path == "back_to_game":
            # 断开卡牌详情面板的关闭信号
            if game_scene and game_scene.card_detail_panel and game_scene.card_detail_panel.detail_panel_closed.is_connected(_on_required_button_pressed):
                game_scene.card_detail_panel.detail_panel_closed.disconnect(_on_required_button_pressed)
            return
        elif previous_signal_path == "rule_back_to_game":
            # 断开规则面板的关闭信号
            if game_scene and game_scene.card_rules_panel and game_scene.card_rules_panel.rules_panel_closed.is_connected(_on_required_button_pressed):
                game_scene.card_rules_panel.rules_panel_closed.disconnect(_on_required_button_pressed)
            return
            
        var previous_node = get_node_or_null("../" + previous_signal_path)
        if previous_node is BaseButton and previous_node.pressed.is_connected(_on_required_button_pressed):
            previous_node.pressed.disconnect(_on_required_button_pressed)

func _on_required_button_pressed() -> void:
    # 当需要的按钮被点击时，进入下一步
    current_step += 1
    show_current_step()

func _on_next_button_pressed() -> void:
    current_step += 1
    show_current_step()

func _complete_tutorial() -> void:
    # 保存教程完成状态到配置文件
    var config = ConfigFile.new()
    var err = config.load("user://settings.cfg")
    if err != OK:
        # 如果配置文件不存在，创建新的
        config = ConfigFile.new()
    
    config.set_value(TUTORIAL_SECTION, TUTORIAL_COMPLETED_KEY, true)
    config.save("user://settings.cfg")
    SteamInfo.fileUpload("Settings",config)
    BuffManager.reset_all_buffs()
    emit_signal("tutorial_completed")
    queue_free()

func _input(event: InputEvent) -> void:
    # 首先检查是否是鼠标事件
    if event is InputEventMouse:
        var mouse_event = event as InputEventMouse
        var panel_rect = tutorial_panel.get_global_rect()
        
        # 如果当前有高亮节点，检查点击是否在高亮区域内
        if current_highlight_node != null:
            if current_step == tutorial_steps.size() - 1:
                if not panel_rect.has_point(mouse_event.position):
                    get_viewport().set_input_as_handled()
                    return
            var highlight_rect = current_highlight_node.get_global_rect()
            # 如果点击不在高亮区域内且不在教程面板内，阻止事件传递
            if not highlight_rect.has_point(mouse_event.position) and not panel_rect.has_point(mouse_event.position):
                get_viewport().set_input_as_handled()
                return
        else:
            # 如果没有高亮节点，只允许教程面板区域的点击
            if not panel_rect.has_point(mouse_event.position):
                get_viewport().set_input_as_handled()
                return
    
    # 处理教程相关的输入事件
    if current_step < tutorial_steps.size():
        var step = tutorial_steps[current_step]
        if not step.action_required or not step.has("required_signal"):
            return
            
        # 检查鼠标是否在高亮区域内
        if event is InputEventMouse and current_highlight_node != null:
            var rect = current_highlight_node.get_global_rect()
            if not rect.has_point(event.position):
                return
                
        if step.required_signal == "mouseDrag":
            _handle_mouse_drag(event, step)

func _handle_mouse_drag(event: InputEvent, step: Dictionary) -> void:
    if event is InputEventMouseButton:
        if event.button_index == MOUSE_BUTTON_LEFT:
            if event.pressed:
                # 开始拖动
                is_dragging = true
                drag_distance_sufficient = false  # 重置状态
                drag_start_pos = event.position
            else:
                # 结束拖动
                if is_dragging and drag_distance_sufficient:
                    # 只有在拖动距离足够且松开鼠标时才进入下一步
                    #get_viewport().set_input_as_handled()
                    current_step += 1
                    show_current_step()
                is_dragging = false
    elif event is InputEventMouseMotion and is_dragging:
        # 在拖动过程中检查距离
        var current_drag_distance = (event.position - drag_start_pos).length()
        if current_drag_distance >= MIN_DRAG_DISTANCE:
            drag_distance_sufficient = true

# 处理change_chip的输入事件
func _on_change_chip_input(event: InputEvent) -> void:
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        # 等待一帧，确保状态已经改变
        await get_tree().process_frame
        # 进入下一步
        current_step += 1
        show_current_step()

# 处理deck_discard_area的输入事件
func _on_deck_area_input(event: InputEvent) -> void:
    var game_scene = get_node("/root/GameScene")
    if not game_scene or not game_scene.is_magic:
        return
        
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        # 等待一帧，确保状态已经改变
        await get_tree().process_frame
        # 进入下一步
        current_step += 1
        show_current_step()

# 处理discard_area的输入事件
func _on_discard_area_input(event: InputEvent) -> void:
    if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
        # 等待一帧，确保状态已经改变
        await get_tree().process_frame
        # 进入下一步
        current_step += 1
        show_current_step()

func _on_card_detail_shown(_card_node) -> void:
    # 当任意卡牌显示详情时触发
    current_step += 1
    show_current_step()

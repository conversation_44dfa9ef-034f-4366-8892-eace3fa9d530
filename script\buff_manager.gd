extends Node

# BUFF管理器 - 负责管理游戏中的所有BUFF效果

# BUFF类型枚举
const BuffType: Dictionary = {
    ROUND_SCORE_ADD = "round_score_add",      # 单阶积分 +N
    SAME_RANK_ADD = "same_rank_add",        # 所有相同点数牌面分数 +N
    SAME_TYPE_ADD = "same_type_add",        # 所有相同花色牌面分数 +N
    SAME_RANK_MULTIPLY = "same_rank_multiply",   # 所有相同点数牌面分数 *N
    SAME_TYPE_MULTIPLY = "same_type_multiply",   # 所有相同花色牌面分数 *N
    DICE_COUNT = "dice_count",           # 骰子数量 +N
    DICE_ROLLS = "dice_rolls",           # 掷骰子机会 +N
    HAND_LIMIT = "hand_limit",           # 手牌区上限 +N
    ACTION_POINTS_ADD = "action_points_add",    # 行动点数 +N
    DRAW_COST_REDUCE = "draw_cost_reduce",    # 抽牌消耗 -N
    # --- 牌型 BUFF ---
    HIGH_CARD_ADD = "high_card_add",       # 单张牌型分数 +N
    PAIR_ADD = "pair_add",            # 对子牌型分数 +N
    STRAIGHT_ADD = "straight_add",        # 顺子牌型分数 +N
    FLUSH_ADD = "flush_add",          # 金花牌型分数 +N
    STRAIGHT_FLUSH_ADD = "straight_flush_add", # 顺金牌型分数 +N
    THREE_KIND_ADD = "three_kind_add",     # 豹子牌型分数 +N
    VIP_ADD = "vip_add",           # VIP牌型分数 +N
    # --- 特殊 BUFF ---
    TIGER = "tiger_buff",                # 老虎牌: N阶后获得S分, 每阶扣最后牌分
    RABBIT = "rabbit_buff",               # 兔子牌: 立刻获得S分, N阶内扣最后牌分
    FROG = "frog_buff",                 # 青蛙牌: 分数上限70%, N阶内禁用某花色
    TURTLE = "turtle_buff",               # 乌龟牌: 指定花色牌分数乘以K倍, N阶后扣减Q积分
    GORILLA = "gorilla_buff",              # 猩猩牌: 每阶牌面积分超过S则加S分,否则减N分
    SHARK = "shark_buff",                # 鲨鱼牌: 所有BUFF有效阶数+N，青蛙和乌龟牌失效（一次性BUFF）
    EAGLE = "eagle_buff",                # 老鹰牌: 兔子牌失效，老虎/猩猩牌剩余次数乘以N（一次性BUFF）
    CARP = "carp_buff"                  # 锦鲤牌: 当前积分*N（随机数，范围1.5-2.5，随阶增加而减小）（一次性）
}

# BUFF特效颜色
enum BuffColor {
    WHITE,  # 白色 - 牌堆相关/单阶积分
    GREEN,  # 绿色 - 加法加成
    BLUE,   # 蓝色 - 乘法加成
    PURPLE, # 紫色 - 游戏机制相关
    ORANGE  # 橙色 - 特殊效果 BUFF
}

# 激活的BUFF列表
var active_buffs = []
var buff_area = []

# --- 特殊 BUFF 池 ---
# 每个特殊BUFF包含:
#   type: BuffType 枚举
#   description_template: 描述模板 (例如: "老虎牌: %d阶后+%d分, 阶扣分")
#   base_value_s: 基础分数 S (用于计算随机范围)
#   base_rounds_n: 基础阶数 N (用于计算随机范围)
#   is_enabled: 是否启用 (初始可以设为 false)
var zoo_buff_pool = []  # 改为空数组，将从配置文件加载

# --- 普通 BUFF 池 ---
var normal_buff_pool = [] # 存储普通BUFF配置

# --- 游戏规则值 ---
var game_rules_values = {} # 内存中的游戏规则值，从 data/game_rules_values.json 加载

# 配置文件路径
const DEFAULT_ZOO_BUFF_CONFIG_PATH = "res://config/default_zoo_buff.cfg"
const DEFAULT_NORMAL_BUFF_CONFIG_PATH = "res://config/default_normal_buff.cfg"
const GAME_RULES_VALUES_PATH = "res://data/game_rules_values.json"

# --- 特殊 BUFF 池结束 ---

# BUFF 贴图路径映射 (请根据你的实际文件调整)
const BUFF_TEXTURES = {
    BuffType.ROUND_SCORE_ADD: "res://assert/sign/score_add.png",
    BuffType.SAME_RANK_ADD: "res://assert/sign/rank_or_type_add.png",
    BuffType.SAME_TYPE_ADD: "res://assert/sign/rank_or_type_add.png",
    BuffType.SAME_RANK_MULTIPLY: "res://assert/sign/rank_or_type_multiply.png",
    BuffType.SAME_TYPE_MULTIPLY: "res://assert/sign/rank_or_type_multiply.png",
    BuffType.DICE_COUNT: "res://assert/sign/dice_count_or_rolls.png",
    BuffType.DICE_ROLLS: "res://assert/sign/dice_count_or_rolls.png",
    BuffType.HAND_LIMIT: "res://assert/sign/hand_limit.png",
    BuffType.ACTION_POINTS_ADD: "res://assert/sign/action_points.png",
    BuffType.DRAW_COST_REDUCE: "res://assert/sign/action_reduce_buff.png",
    # --- 牌型 BUFF 贴图 ---
    BuffType.HIGH_CARD_ADD: "res://assert/sign/high_card_buff.png",
    BuffType.PAIR_ADD: "res://assert/sign/pair_buff.png",
    BuffType.STRAIGHT_ADD: "res://assert/sign/straight_buff.png",
    BuffType.FLUSH_ADD: "res://assert/sign/flush_buff.png",
    BuffType.STRAIGHT_FLUSH_ADD: "res://assert/sign/straight_flush_buff.png",
    BuffType.THREE_KIND_ADD: "res://assert/sign/three_kind_buff.png",
    BuffType.VIP_ADD: "res://assert/sign/vip_buff.png",
    # --- 特殊 BUFF 贴图 ---
    BuffType.TIGER: "res://assert/sign/tiger_buff.png", 
    BuffType.RABBIT: "res://assert/sign/rabbit_buff.png", 
    BuffType.FROG: "res://assert/sign/frog_buff.png",      
    BuffType.TURTLE: "res://assert/sign/turtle_buff.png",
    BuffType.GORILLA: "res://assert/sign/gorilla_buff.png",
    BuffType.SHARK: "res://assert/sign/shark_buff.png",
    BuffType.EAGLE: "res://assert/sign/eagle_buff.png",
    BuffType.CARP: "res://assert/sign/carp_buff.png"
}

# BUFF 颜色映射 (可以调整透明度 alpha)
const BUFF_COLORS = {
    BuffColor.WHITE: Color(1.0, 1.0, 1.0, 0.3),   # 白色，稍透明
    BuffColor.GREEN: Color(0.4, 1.0, 0.4, 0.3),  # 亮绿色
    BuffColor.BLUE: Color(0.5, 0.7, 1.0, 0.3),   # 亮蓝色
    BuffColor.PURPLE: Color(0.9, 0.6, 1.0, 0.3), # 亮紫色
    BuffColor.ORANGE: Color(1.0, 0.7, 0.3, 0.3)  # 橙色，稍透明
}

# 信号
signal buff_activated(buff_data)
signal buff_applied(buff_data, target_value, new_value)
signal buff_rounds_updated(buff_data)
signal buff_expired(buff_data)
signal permanent_buff_added(buff_data: Dictionary)

# 当前语言设置
var current_language = "zh_CN" # 默认为中文
var buff_label_font_size = 28

# 初始化
func _ready():
    # 设置初始语言
    current_language = TranslationServer.get_locale()
    
    # 监听语言变化
    LanguageManager.language_changed.connect(_on_language_changed)
    
    # 加载BUFF配置
    load_zoo_buff_config()
    load_normal_buff_config()

    # 加载游戏规则值
    load_game_rules_values()

    # 重置游戏规则配置
    reset_game_configs()

# 重置所有游戏配置
func reset_game_configs():
    # 重置BUFF配置
    # reset_all_buffs()
    
    # 清空激活的BUFF
    clear_active_buffs()

# 加载普通BUFF配置
func load_normal_buff_config():

    var config = ConfigFile.new()

    var err = config.load(DEFAULT_NORMAL_BUFF_CONFIG_PATH)
    
    if err != OK:
        printerr("无法加载普通BUFF配置文件")
        return
    else:
        print("成功加载默认普通BUFF配置")
    
    # 清空当前普通BUFF池
    normal_buff_pool.clear()
    
    # 从配置文件加载BUFF
    var sections = config.get_sections()
    for section in sections:
        var buff_data = {
            "type": config.get_value(section, "type", ""),
            "description_template": config.get_value(section, "description_template", {}),
            "display_name": config.get_value(section, "display_name", {}),
            "explanation_template": config.get_value(section, "explanation_template", {}),
            "base_value": config.get_value(section, "base_value", 1),
            "weight": config.get_value(section, "weight", 1),
            "min_rounds": config.get_value(section, "min_rounds", 1),
            "max_rounds": config.get_value(section, "max_rounds", 1)
        }
        
        # 添加到BUFF池
        normal_buff_pool.append(buff_data)
    
    print("成功加载普通BUFF配置，共加载 ", normal_buff_pool.size(), " 个BUFF")

# 获取本地化的值
func _get_localized_value(value: Variant, language: String) -> String:
    # 如果输入是字符串，直接返回
    if value is String:
        return value
    # 如果输入是字典，尝试获取对应语言的值
    elif value is Dictionary:
        if value.has(language):
            return value[language]
        elif value.has("en"):  
            return value["en"]
        else:
            return ""  # 如果连英文都没有，返回空字符串
    else:
        return ""  # 如果是其他类型，返回空字符串

# 创建本地化字典
func _create_localized_dict(value: Variant) -> Dictionary:
    # 如果已经是字典，直接返回
    if value is Dictionary:
        return value
    # 否则创建一个只包含当前语言的字典
    return {
        current_language: str(value)
    }

# 加载BUFF配置
func load_zoo_buff_config():
    # 直接从默认配置文件加载基础数据
    var config = ConfigFile.new()
    var err = config.load(DEFAULT_ZOO_BUFF_CONFIG_PATH)

    if err != OK:
        print("无法加载默认BUFF配置文件")
        return

    print("成功加载默认BUFF配置")

    # 加载 is_enabled 状态从 settings.cfg
    var enabled_states = _load_zoo_buff_enabled_states()
    
    # 清空当前BUFF池
    zoo_buff_pool.clear()
    
    # 从配置文件加载BUFF
    var sections = config.get_sections()
    for section in sections:
        if section:
            var buff_type = config.get_value(section, "type", 0)
            var buff_data = {
                "type": buff_type,
                "name": config.get_value(section, "name", tr("HAND_TYPE_UNKNOWN")),
                "description_template": config.get_value(section, "description_template", {}),
                "display_name": config.get_value(section, "display_name", {}),
                "explanation_template": config.get_value(section, "explanation_template", {}),
                "base_value_s": config.get_value(section, "base_value_s", 1),
                "base_rounds_n": config.get_value(section, "base_rounds_n", 1),
                "is_enabled": enabled_states.get(buff_type, false)  # 从 settings.cfg 获取状态
            }
            
            # 根据不同的BUFF类型，加载特定的额外数据
            match buff_type:
                BuffType.TIGER, BuffType.RABBIT:
                    buff_data["debuff_value"] = config.get_value(section, "debuff_value", 2)
                
                BuffType.FROG:
                    buff_data["score_rate_min"] = config.get_value(section, "score_rate_min", 0.6)
                    buff_data["score_rate_max"] = config.get_value(section, "score_rate_max", 0.9)
                
                BuffType.TURTLE:
                    buff_data["penalty_value"] = config.get_value(section, "penalty_value", 10)
                
                BuffType.GORILLA:
                    buff_data["base_penalty_n"] = config.get_value(section, "base_penalty_n", 3)
                
                BuffType.CARP:
                    buff_data["base_value_max"] = config.get_value(section, "base_value_max", 2.5)
        
            # 添加到BUFF池
            zoo_buff_pool.append(buff_data)
    
    print("成功加载BUFF配置，共加载 ", zoo_buff_pool.size(), " 个BUFF")

# 加载游戏规则值到内存
func load_game_rules_values():
    var file = FileAccess.open(GAME_RULES_VALUES_PATH, FileAccess.READ)
    if not file:
        print("无法打开游戏规则配置文件: ", GAME_RULES_VALUES_PATH)
        # 使用默认值
        _initialize_default_game_rules()
        return

    var content = file.get_as_text()
    file.close()

    var json = JSON.new()
    var parse_result = json.parse(content)

    if parse_result != OK:
        print("解析游戏规则配置文件失败")
        _initialize_default_game_rules()
        return

    var data = json.data
    if "rules" in data and data.rules is Array:
        # 将规则数组转换为字典，以 id 为键
        game_rules_values.clear()
        for rule in data.rules:
            if "id" in rule:
                game_rules_values[rule.id] = {
                    "base_score": rule.get("base_score", 0),
                    "bonus_score": rule.get("bonus_score", 0)
                }
        print("成功加载游戏规则值，共 %d 个规则" % game_rules_values.size())
    else:
        print("游戏规则配置文件格式错误")
        _initialize_default_game_rules()

# 初始化默认游戏规则值
func _initialize_default_game_rules():
    game_rules_values = {
        "three_of_kind": {"base_score": 60, "bonus_score": 0},
        "straight_flush": {"base_score": 50, "bonus_score": 0},
        "vip": {"base_score": 40, "bonus_score": 0},
        "flush": {"base_score": 30, "bonus_score": 0},
        "straight": {"base_score": 20, "bonus_score": 0},
        "pair": {"base_score": 10, "bonus_score": 0},
        "high_card": {"base_score": 0, "bonus_score": 0}
    }
    print("使用默认游戏规则值")

# 从 settings.cfg 加载 zoo_buff 的 is_enabled 状态
func _load_zoo_buff_enabled_states() -> Dictionary:
    var settings = ConfigFile.new()
    var err = settings.load("user://settings.cfg")

    var enabled_states = {}

    if err == OK:
        # 从 [zoo_buff_enabled] 节读取状态
        var sections = settings.get_sections()
        if "zoo_buff_enabled" in sections:
            var keys = settings.get_section_keys("zoo_buff_enabled")
            for key in keys:
                enabled_states[key] = settings.get_value("zoo_buff_enabled", key, false)
            print("从 settings.cfg 加载了 %d 个 zoo_buff 启用状态" % enabled_states.size())
        else:
            print("settings.cfg 中没有找到 zoo_buff_enabled 节，使用默认状态")
    else:
        print("无法加载 settings.cfg，使用默认 zoo_buff 状态")

    return enabled_states

# 保存 zoo_buff 的 is_enabled 状态到 settings.cfg
func _save_zoo_buff_enabled_states():
    var settings = ConfigFile.new()
    # 先尝试加载现有的 settings.cfg
    settings.load("user://settings.cfg")

    # 保存所有 zoo_buff 的 is_enabled 状态
    for buff in zoo_buff_pool:
        settings.set_value("zoo_buff_enabled", buff.type, buff.is_enabled)

    # 保存到文件
    var err = settings.save("user://settings.cfg")
    SteamInfo.fileUpload("Settings",settings)
    # if err == OK:
    #     print("zoo_buff 启用状态已保存到 settings.cfg")
    # else:
    #     print("保存 zoo_buff 启用状态失败: ", err)

# 随机解锁指定数量的BUFF
func unlock_random_buffs(count: int) -> Array:
    # 获取所有未解锁的BUFF
    var locked_buffs = []
    for i in range(zoo_buff_pool.size()):
        if not zoo_buff_pool[i].is_enabled:
            locked_buffs.append(i)
    
    # 如果没有未解锁的BUFF，返回空数组
    if locked_buffs.size() == 0:
        return []
    
    # 随机选择指定数量的BUFF解锁
    var unlocked_buffs = []
    count = min(count, locked_buffs.size())
    
    for _i in range(count):
        if locked_buffs.size() == 0:
            break
            
        var random_index = randi() % locked_buffs.size()
        var buff_index = locked_buffs[random_index]
        
        # 解锁BUFF
        zoo_buff_pool[buff_index].is_enabled = true
        unlocked_buffs.append(zoo_buff_pool[buff_index])
        
        # 从未解锁列表中移除
        locked_buffs.remove_at(random_index)
    
    # 保存启用状态到 settings.cfg
    _save_zoo_buff_enabled_states()

    return unlocked_buffs

# 重置所有BUFF为未解锁状态
func reset_all_buffs():
    for buff in zoo_buff_pool:
        buff.is_enabled = false
    
    # 保存启用状态到 settings.cfg
    _save_zoo_buff_enabled_states()

# 检查是否所有BUFF都已解锁
func are_all_buffs_unlocked() -> bool:
    for buff in zoo_buff_pool:
        if not buff.is_enabled:
            return false
    return true

# 获取已解锁的BUFF数量
func get_unlocked_buff_count() -> int:
    var count = 0
    for buff in zoo_buff_pool:
        if buff.is_enabled:
            count += 1
    return count

# --- 新增辅助函数 ---
# 获取 BUFF 对应的贴图路径
static func get_buff_texture_path(buff_type: String) -> String:
    return BUFF_TEXTURES.get(buff_type, "res://assert/sign/default.png") # 默认贴图

# 获取 BUFF 对应的视觉颜色 (用于着色器)
static func get_buff_visual_color(buff_color_enum: BuffColor) -> Color:
    return BUFF_COLORS.get(buff_color_enum, Color.WHITE) # 默认白色

# 创建随机BUFF效果
func create_random_buff() -> Dictionary:
    # 获取卡牌系统和当前阶数
    var card_system = get_node("/root/CardSystem")
    var current_round = 1
    if card_system:
        current_round = max(1, card_system.round_counter)

    # 根据阶数计算BUFF值的上限增量
    var round_bonus = int(current_round / 5)  # 每N阶增加一次上限

    # --- 新增：决定是否尝试抽取特殊 BUFF ---
    var try_special_buff = randf() < 0.5 # 50% 几率
    var selected_special_buff = null

    if try_special_buff:
        var enabled_special_buffs = []
        for zoo_buff_def in zoo_buff_pool:
            if zoo_buff_def.is_enabled:  # 只从已解锁的BUFF中选择
                enabled_special_buffs.append(zoo_buff_def)

        if not enabled_special_buffs.is_empty():
            selected_special_buff = enabled_special_buffs[randi() % enabled_special_buffs.size()]
    # --- 特殊 BUFF 抽取逻辑结束 ---

    var buff_type
    var buff_value = 0
    var buff_color = BuffColor.WHITE
    var buff_description = ""
    var rounds_left = 1
    var additional_data = {} # 用于存储特殊 BUFF 可能需要的额外数据

    # --- 如果选中了特殊 BUFF --- 
    if selected_special_buff:
        buff_type = selected_special_buff.type
        buff_color = BuffColor.ORANGE # 特殊 BUFF 使用橙色
        var base_s = selected_special_buff.base_value_s + round_bonus
        var base_n = selected_special_buff.base_rounds_n + int(current_round / 8) # 阶数也稍微增加
        var _name = _get_localized_value(selected_special_buff.name, TranslationServer.get_locale())
        base_n = max(2, base_n) # 至少持续2阶

        # 计算阶数 N (适用于所有特殊 BUFF)
        var min_rounds = max(2, base_n - 1)
        var max_rounds = base_n + 1
        rounds_left = randi() % (max_rounds - min_rounds + 1) + min_rounds
        additional_data["total_rounds"] = rounds_left # 记录总阶数，以便后续逻辑使用

        match buff_type:
            BuffType.TIGER:
                # 老虎牌: N阶后获得S分, 每阶扣最后牌分
                var score_s = randi() % (base_s + 10) + base_s # 随机分数 S
                buff_value = score_s # value 存储老虎牌最终奖励的分数
                buff_description = _name
                additional_data["debuff_value"] = selected_special_buff.debuff_value
                buff_color = BuffColor.ORANGE

            BuffType.RABBIT:
                # 兔子牌: 立刻获得S分, N阶内扣最后牌分
                var score_s = randi() % (base_s + 5) + base_s # 随机分数 S
                buff_value = score_s # value 存储兔子牌立刻获得的分数
                buff_description = _name
                additional_data["debuff_value"] = selected_special_buff.debuff_value
                buff_color = BuffColor.ORANGE

            BuffType.FROG:
                # 青蛙牌: 分数上限70%, N阶内禁用某花色
                if card_system and "CardSuit" in card_system: 
                    var forbidden_suit_enum_value = _get_random_suit(card_system)
                    if forbidden_suit_enum_value != null:
                        var score_s = randf_range(selected_special_buff.score_rate_min,selected_special_buff.score_rate_max)
                        buff_value = round(score_s*10)/10
                        buff_description = _name
                        additional_data["forbidden_suit"] = forbidden_suit_enum_value 
                        buff_color = BuffColor.ORANGE
            
            BuffType.TURTLE:
                var multiplier = selected_special_buff.base_value_s * current_round / 10.0 
                multiplier = max(2, min(multiplier, 8.0)) 
                buff_value = roundf(multiplier * 10) / 10.0
                var penalty = selected_special_buff.penalty_value + int(current_round / 5) * 2
                var enhanced_suit_enum_value = _get_random_suit(card_system)
                if enhanced_suit_enum_value != null:
                    additional_data["enhanced_suit"] = enhanced_suit_enum_value
                buff_description = _name # 描述将在 card_detail_panel 中生成
                buff_color = BuffColor.ORANGE
                additional_data["penalty_value"] = penalty
                additional_data["multiplier"] = buff_value
            
            BuffType.GORILLA:
                # 猩猩牌: 每阶牌面积分超过S则加S分,否则减N分
                var threshold_s = selected_special_buff.base_value_s + int(current_round / 5) # 阈值随阶增加
                
                buff_value = threshold_s # buff_value 存储阈值S
                additional_data["bonus_s"] = threshold_s # 奖励也是S
                additional_data["penalty_n"] = threshold_s # 惩罚也是S
                
                buff_description = _name # 描述将在 card_detail_panel 中生成
                buff_color = BuffColor.ORANGE

            BuffType.SHARK:
                # 鲨鱼牌: 所有BUFF有效阶数+N，青蛙和乌龟牌失效（一次性BUFF）
                var bonus_rounds = selected_special_buff.base_value_s # 增加的阶数
                buff_value = bonus_rounds # 存储增加的阶数
                buff_description = _name # 描述将在 card_detail_panel 中生成
                buff_color = BuffColor.ORANGE

            BuffType.EAGLE:
                # 老鹰牌: 兔子牌失效，老虎/猩猩牌剩余次数乘以N
                var multiplier = selected_special_buff.base_value_s # 阶数倍率N
                buff_value = multiplier # 存储阶数倍率
                buff_description = _name
                buff_color = BuffColor.ORANGE

            BuffType.CARP:
                # 锦鲤牌: 当前积分*N
                # 随着阶增加，倍率减小
                var min_multiplier = selected_special_buff.base_value_s
                var max_multiplier = selected_special_buff.base_value_max
                
                # 计算随阶递减的倍率上限
                max_multiplier = max(min_multiplier, max_multiplier - (current_round / 10.0))
                
                # 生成随机倍率（保留一位小数）
                var multiplier = randf_range(min_multiplier, max_multiplier)
                multiplier = roundf(multiplier * 10) / 10.0 # 四舍五入到一位小数
                
                buff_value = multiplier
                buff_description = _name
                buff_color = BuffColor.ORANGE

    # --- 如果没有选中特殊 BUFF，或者特殊 BUFF 池为空/都禁用，则执行原逻辑 --- 
    else:
        # 使用加权随机选择普通BUFF类型
        var total_weight = 0
        for buff in normal_buff_pool:
            # 如果是骰子数量BUFF且已达到上限，跳过
            if buff.type == BuffType.DICE_COUNT:
                var game_manager = get_node("/root/GameManager")
                if game_manager and game_manager.dice_count >= 6:
                    continue
            total_weight += buff.weight
        
        # 如果总权重为0，返回默认BUFF
        if total_weight == 0:
            return {
                "type": BuffType.ROUND_SCORE_ADD,
                "value": 1,
                "color": BuffColor.WHITE,
                "description": tr("SINGLE_ROUND_PLUS_ONE"),
                "rounds_left": 1,
                "card": null,
                "additional_data": {}
            }
        
        # 随机选择BUFF
        var random_value = randi() % total_weight
        var cumulative_weight = 0
        var selected_buff = null
        
        for buff in normal_buff_pool:
            # 如果是骰子数量BUFF且已达到上限，跳过
            if buff.type == BuffType.DICE_COUNT:
                var game_manager = get_node("/root/GameManager")
                if game_manager and game_manager.dice_count >= 6:
                    continue
            
            cumulative_weight += buff.weight
            if random_value < cumulative_weight:
                selected_buff = buff
                break
        
        if selected_buff == null:
            selected_buff = normal_buff_pool[0]
        
        buff_type = selected_buff.type
        
        # 计算阶数
        if _is_one_time_buff(buff_type):
            rounds_left = 1
        else:
            var min_rounds = selected_buff.min_rounds
            var max_rounds = min(selected_buff.max_rounds + int(current_round / 5), 20)
            rounds_left = randi() % (max_rounds - min_rounds + 1) + min_rounds
            additional_data["total_rounds"] = rounds_left
        
        # 计算BUFF值
        var base_value = selected_buff.base_value
        match buff_type:
            BuffType.ROUND_SCORE_ADD, BuffType.ACTION_POINTS_ADD:
                buff_value = base_value + randi() % (round_bonus + 1)
                buff_color = BuffColor.WHITE
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale()) % buff_value
                
            BuffType.SAME_RANK_ADD, BuffType.SAME_TYPE_ADD:
                buff_value = base_value + randi() % (round_bonus + 1)
                buff_color = BuffColor.GREEN
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale()) % buff_value
                
            BuffType.SAME_RANK_MULTIPLY, BuffType.SAME_TYPE_MULTIPLY:
                buff_value = base_value + randi() % (round_bonus + 1)
                buff_color = BuffColor.BLUE
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale()) % buff_value
                
            BuffType.DICE_COUNT, BuffType.DICE_ROLLS, BuffType.HAND_LIMIT:
                buff_value = base_value
                buff_color = BuffColor.PURPLE
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale()) % buff_value
                rounds_left = 1

            BuffType.HIGH_CARD_ADD,BuffType.PAIR_ADD,BuffType.STRAIGHT_ADD,BuffType.FLUSH_ADD,BuffType.STRAIGHT_FLUSH_ADD,BuffType.THREE_KIND_ADD,BuffType.VIP_ADD:
                buff_value = base_value
                buff_color = BuffColor.PURPLE
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale()) % buff_value

            BuffType.DRAW_COST_REDUCE:
                buff_value = base_value
                buff_color = BuffColor.ORANGE
                buff_description = _get_localized_value(selected_buff.description_template, TranslationServer.get_locale())

    # 创建最终 BUFF 数据
    var buff_data = {
        "type": buff_type,
        "value": buff_value,
        "color": buff_color,
        "description": buff_description,
        "rounds_left": rounds_left,
        "card": null,
        "additional_data": additional_data
    }
    
    return buff_data

# 将BUFF应用到卡牌
func apply_buff_to_card(card_data) -> Dictionary:
    var buff_data = create_random_buff()
    
    # 将BUFF数据添加到卡牌
    var card_with_buff = card_data.duplicate()
    card_with_buff["buff"] = buff_data
    buff_data["card"] = card_with_buff
    
    return card_with_buff

# 激活BUFF效果
func activate_buff(buff_data, is_add_buff = false):
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return
            
    # 确保 BUFF 卡牌（如果是来自卡牌的 BUFF）确实在手牌的前三张位置
    var should_activate = false
    if "card" in buff_data and buff_data.card != null: # 检查 buff_data.card 是否为 null
        for i in range(min(3, card_system.player_hand.size())): # 使用 hand_limit
            if card_system.player_hand[i] == buff_data.card:
                should_activate = true
                break
    else:
        # 如果不是来自卡牌的 BUFF（例如直接添加的），总是激活
        should_activate = true 
        
    # 如果不满足激活条件，则返回
    if not should_activate:
        print("BUFF 卡牌不在有效位置，不激活: ", buff_data.description)
        return

    # 添加到激活的BUFF列表
    if not active_buffs.has(buff_data):
        active_buffs.append(buff_data)
        print("BUFF添加到active_buffs: ", buff_data.description)
    
    # 持续性BUFF (包括特殊 BUFF) 加入 BUFF 区
    # 注意：这里移除了 _is_one_time_buff 检查，因为特殊 BUFF 也需要进 buff_area
    if is_add_buff and not buff_area.has(buff_data) and not _is_one_time_buff(buff_data.type):
        buff_area.append(buff_data)
        print("BUFF添加到buff_data: ", buff_data.description)


# 应用BUFF效果到游戏机制
func apply_buffs_to_game_mechanics():
    # 获取游戏管理器
    var game_manager = get_node("/root/GameManager")
    if not game_manager:
        return
    
    # 应用所有激活的BUFF (合并 active_buffs 和 buff_area)
    var all_active_buffs = []
    for buff in active_buffs:
        if not all_active_buffs.has(buff) and "additional_data" in buff and "forbidden_suit" in buff.additional_data and not game_manager.forbidden_suits.has(buff.additional_data.forbidden_suit):
             all_active_buffs.append(buff)
    for buff in buff_area:
        if not all_active_buffs.has(buff) and "additional_data" in buff and "forbidden_suit" in buff.additional_data and not game_manager.forbidden_suits.has(buff.additional_data.forbidden_suit):
             all_active_buffs.append(buff)
    
    for buff in all_active_buffs: # 遍历合并后的列表
        # 确保BUFF还有剩余阶数
        if buff.rounds_left <= 0:
            continue
            
        var card_system = get_node("/root/CardSystem")
        match buff.type:
            BuffType.ROUND_SCORE_ADD:
                # 阶积分BUFF在分数结算时已经应用，这里不需要额外处理游戏机制
                pass
            
            BuffType.DICE_COUNT:
                # 骰子数量 +N
                if game_manager:
                    # 检查当前骰子数量是否已经达到或超过5
                    if game_manager.dice_count >= 5:
                        # 如果已经达到5个或以上，则不应用此BUFF
                        print("骰子数量已达5个或以上，DICE_COUNT BUFF不生效")
                        continue
                    
                    # 使用set_dice_count方法更新骰子数量
                    var new_count = game_manager.dice_count + buff.value
                    # 确保不超过最大值10
                    if new_count > 10:
                        new_count = 10
                    game_manager.set_dice_count(new_count)
                    # 发出BUFF应用信号
                    emit_signal("buff_applied", buff, game_manager.dice_count - buff.value, game_manager.dice_count)
            
            BuffType.DICE_ROLLS:
                # 掷骰子机会 +N
                if game_manager:
                    game_manager.dice_rolls_chance += buff.value
                    # 确保不会小于1
                    if game_manager.dice_rolls_chance < 1:
                        game_manager.dice_rolls_chance = 1
                       
            
            BuffType.HAND_LIMIT:
                if game_manager:
                    # 获取当前手牌上限并应用BUFF值
                    game_manager.hand_limit += buff.value
                    # 确保手牌上限不小于最小值（至少为3）
                    if game_manager.hand_limit < 3:
                        game_manager.hand_limit = 3
                    # 发出手牌上限变化信号
                    game_manager.emit_signal("hand_limit_changed", game_manager.hand_limit)

            BuffType.ACTION_POINTS_ADD:
                # if game_manager:
                #     game_manager.add_action_points(buff.value)
                pass
            
            BuffType.DRAW_COST_REDUCE:
                # 抽牌消耗改变
                if game_manager:
                    if buff.rounds_left == 1:
                        game_manager.consume_score = false
                    else:
                        # 设置抽牌消耗为0
                        game_manager.consume_score = true
                        # 发出BUFF应用信号
                        emit_signal("buff_applied", buff, 0)  

            # --- 新增: 处理特殊 BUFF 的游戏机制效果 ---
            BuffType.TIGER:
                # 老虎牌主要影响分数，游戏机制上无特殊处理
                pass 
            
            BuffType.RABBIT:
                # 兔子牌主要影响分数，游戏机制上无特殊处理
                pass 
                
            BuffType.FROG:
                # 禁用特定花色 (通过 GameManager 记录禁用的花色)
                if game_manager.has_method("add_forbidden_suit") and "additional_data" in buff and "forbidden_suit" in buff.additional_data:
                    var suit = buff.additional_data.forbidden_suit
                    game_manager.add_forbidden_suit(suit)
                    print("青蛙 BUFF: 添加禁用花色 ", suit)
            
            BuffType.TURTLE:
                # 乌龟牌主要影响分数，游戏机制上无特殊处理
                pass
            
            BuffType.GORILLA:
                # 猩猩牌主要影响分数，游戏机制上无特殊处理
                pass
            # --- 特殊 BUFF 处理结束 ---

# 更新BUFF的阶数限制
func update_buff_rounds():
    var processed_buffs = [] # 用于记录本次调用已处理的BUFF
    var expired_buffs = []   # 用于记录本轮过期的BUFF
    
    # --- 第一步：合并并去重所有需要更新阶数的BUFF --- 
    var buffs_to_update = []
    # 添加 active_buffs 中的非一次性BUFF
    for buff in active_buffs:
        if not _is_one_time_buff(buff.type) and not buffs_to_update.has(buff):
            buffs_to_update.append(buff)
    # 添加 buff_area 中的BUFF (如果还没添加的话)
    for buff in buff_area:
        if not buffs_to_update.has(buff):
            buffs_to_update.append(buff)
            
    # --- 第二步：遍历去重后的列表，更新阶数 --- 
    for buff in buffs_to_update:
        if buff.rounds_left > 0:
            buff.rounds_left -= 1
            emit_signal("buff_rounds_updated", buff)
 
            # 当阶数为0时，标记为过期
            if buff.rounds_left <= 0:
                expired_buffs.append(buff)
                emit_signal("buff_expired", buff)
    
    # --- 第三步：从 buff_area 中移除过期的BUFF --- 
    # 注意：active_buffs 会在阶开始时被清空，无需在此处移除
    for expired in expired_buffs:
        if buff_area.has(expired):
            buff_area.erase(expired)

# 清除所有BUFF
func clear_active_buffs():
    active_buffs.clear()
    buff_area.clear()

# 获取BUFF颜色
func get_buff_color(color_enum) -> Color:
    match color_enum:
        BuffColor.WHITE:
            return Color(1.0, 1.0, 1.0, 1.0)  # 白色
        BuffColor.GREEN:
            return Color(0.0, 1.0, 0.0, 1.0)  # 绿色
        BuffColor.BLUE:
            return Color(0.0, 0.5, 1.0, 1.0)  # 蓝色
        BuffColor.PURPLE:
            return Color(0.8, 0.0, 1.0, 1.0)  # 紫色
        BuffColor.ORANGE:
            return Color(1.0, 0.7, 0.3, 1.0)  # 橙色
        _:
            return Color(1.0, 1.0, 1.0, 1.0)  # 默认白色

# 判断是否是一次性BUFF
func _is_one_time_buff(buff_type) -> bool:
    # 特殊 BUFF 虽然有 N 阶效果，但它们通常不应像普通一次性 BUFF 那样处理
    # （例如，它们需要保留在 buff_area）
    return buff_type == BuffType.DICE_COUNT or \
           buff_type == BuffType.DICE_ROLLS or \
           buff_type == BuffType.HAND_LIMIT or \
           buff_type == BuffType.SHARK or \
           buff_type == BuffType.EAGLE or \
           buff_type == BuffType.CARP or \
           buff_type == BuffType.ACTION_POINTS_ADD or \
           buff_type == BuffType.HIGH_CARD_ADD or \
           buff_type == BuffType.PAIR_ADD or \
           buff_type == BuffType.STRAIGHT_ADD or \
           buff_type == BuffType.FLUSH_ADD or \
           buff_type == BuffType.STRAIGHT_FLUSH_ADD or \
           buff_type == BuffType.THREE_KIND_ADD or \
           buff_type == BuffType.VIP_ADD

# 获取BUFF的阶数显示文本
func get_rounds_text(buff_data) -> String:
    if "rounds_left" in buff_data:
        # 检查是否为特殊 BUFF 或非一次性普通 BUFF
        if not _is_one_time_buff(buff_data.type):
             # 对于持续性 BUFF (包括特殊 BUFF)，显示剩余阶数
            return "[%d]" % buff_data.rounds_left
        else:
            # 一次性BUFF不显示阶数
            return ""
    return ""

# 处理持续性BUFF
func process_persistent_buffs(buff_manager, buffs, current_score, running_score, base_score, hand_container, buff_display, score_value_label, score_coin, limit_label, hand_card_nodes, face_value, buff_area_nodes = null):
    var updated_score = running_score
    var card_system = get_node("/root/CardSystem")
    
    # 显示每个持续性BUFF的效果
    for buff in buffs:
        # 检查BUFF是否能在当前阶生效
        var buff_can_affect = false
        
        # 根据不同的BUFF类型，检查是否有相应的卡牌可以被影响
        match buff.type:
            buff_manager.BuffType.SAME_RANK_ADD, buff_manager.BuffType.SAME_RANK_MULTIPLY:
                # 对于相同点数的BUFF，检查是否有点数相同的卡牌
                if "card" in buff and buff.card != null and card_system:
                    for i in range(3):
                        if card_system.player_hand[i].rank == buff.card.rank:
                            buff_can_affect = true
                            break
            
            buff_manager.BuffType.SAME_TYPE_ADD, buff_manager.BuffType.SAME_TYPE_MULTIPLY:
                # 对于相同花色的BUFF，检查是否有花色相同的卡牌
                if "card" in buff and buff.card != null and card_system:
                    for i in range(3):
                        if card_system.player_hand[i].suit == buff.card.suit:
                            buff_can_affect = true
                            break
            
            buff_manager.BuffType.TURTLE:
                # 对于乌龟牌BUFF，检查是否有对应花色的卡牌
                if "additional_data" in buff and "enhanced_suit" in buff.additional_data and card_system:
                    var enhanced_suit = buff.additional_data.enhanced_suit
                    for i in range(3):
                        if card_system.player_hand[i].suit == enhanced_suit:
                            buff_can_affect = true
                            break
            
            buff_manager.BuffType.FROG, buff_manager.BuffType.TIGER, buff_manager.BuffType.RABBIT, buff_manager.BuffType.GORILLA:
                # 某些动物BUFF总是生效
                buff_can_affect = true
            
            buff_manager.BuffType.ROUND_SCORE_ADD:
                # 阶积分BUFF总是生效
                buff_can_affect = true
        
        # 如果BUFF能在当前阶生效，才显示BUFF标签和效果
        if buff_can_affect:
            # 播放BUFF对应卡牌的震动动画
            var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

            # 创建BUFF标签
            var buff_label = _create_score_label(buff.description, hand_container, 38, _get_buff_color(buff.color), Color(0.3, 0.3, 0.3, 1), Vector2(0, hand_container.size.y))

            # 显示BUFF标签
            var tween = AnimationManager._play_label_animation(buff_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0.1)
            await tween.finished
            buff_label.queue_free()

            # 处理猩猩牌BUFF
            if buff.type == buff_manager.BuffType.GORILLA:
                updated_score = await _process_gorilla_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, face_value, hand_card_nodes, buff_area_nodes)

            # 处理阶积分BUFF
            if buff.type == buff_manager.BuffType.ROUND_SCORE_ADD:
                updated_score = await _process_round_score_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin)
            
            # 处理同点数同牌型BUFF
            if "card" in buff and buff.card != null:
                updated_score = await _process_card_related_buff(buff_manager, buff, current_score, updated_score, base_score, hand_card_nodes, score_value_label, score_coin)
            
            # 处理老虎牌BUFF
            if buff.type == buff_manager.BuffType.TIGER:
                updated_score = await _process_tiger_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes, buff_area_nodes)

            # 处理兔子牌BUFF
            if buff.type == buff_manager.BuffType.RABBIT:
                updated_score = await _process_rabbit_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes, buff_area_nodes)
            
            # 处理青蛙牌BUFF
            if buff.type == buff_manager.BuffType.FROG:
                updated_score = await _process_frog_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes,limit_label)
            
            # 处理乌龟牌BUFF
            if buff.type == buff_manager.BuffType.TURTLE:
                updated_score = await _process_turtle_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes, buff_area_nodes)
        else:
            print("BUFF不生效 (本阶无适用卡牌):", buff.description)
    
    return updated_score

# 处理阶积分BUFF
func _process_round_score_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin):
    var updated_score = running_score
    
    # 更新总积分显示
    var old_score = current_score + updated_score + base_score
    updated_score += buff.value
    var new_score = current_score + updated_score + base_score
    
    # 使用积分动画更新score_label
    var tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
    await tween.finished
    
    return updated_score

# 获取BUFF颜色
func _get_buff_color(buff_color):
    match buff_color:
        BuffColor.WHITE:
            return Color(1, 1, 1, 1) # 白色
        BuffColor.GREEN:
            return Color(0, 1, 0, 1) # 绿色
        BuffColor.BLUE:
            return Color(0, 0.7, 1, 1) # 蓝色
        BuffColor.ORANGE:
            return Color(1, 0.7, 0, 1) # 橙色
        BuffColor.PURPLE:
            return Color(0.7, 0, 1, 1) # 紫色
        _:
            return Color(1, 1, 1, 1) # 默认白色

# 创建积分或文字标签
func _create_score_label(text, parent_node, font_size, font_color, outline_color=null, position=null):
    var label = Label.new()
    label.text = text
    label.add_theme_font_size_override("font_size", font_size)
    label.add_theme_constant_override("outline_size", 2) # 使用描边代替粗体效果
    label.add_theme_color_override("font_color", font_color)
    
    if outline_color:
        label.add_theme_constant_override("outline_size", 3) # 添加描边
        label.add_theme_color_override("font_outline_color", outline_color)
    
    # 设置位置
    if position:
        label.position = position
    else:
        # 默认位置在卡牌上方
        if parent_node:
            label.position = Vector2(parent_node.size.x / 4, parent_node.size.y)
    
    label.modulate.a = 0 # 初始透明
    label.z_index = 60 # 确保标签显示在卡牌上方
    # 水平居中
    label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    parent_node.add_child(label)
    
    return label

# 获取BUFF对应的卡牌节点
func _get_buff_card_node(buff, hand_card_nodes, buff_area_nodes = null):
    # 如果BUFF有关联的卡牌，在手牌节点中查找
    if "card" in buff and buff.card != null:
        var card_system = get_node("/root/CardSystem")
        if card_system and hand_card_nodes:
            for i in range(min(3, card_system.player_hand.size())):
                if i < hand_card_nodes.size() and card_system.player_hand[i] == buff.card:
                    return hand_card_nodes[i]

    # 如果BUFF来自BUFF展示区域，在BUFF展示区域中查找
    # 尝试多种可能的路径来找到BuffDisplay节点
    var buff_display = null

    # 首先尝试通过unique name访问
    var scene_tree = get_tree()
    if scene_tree and scene_tree.current_scene:
        buff_display = scene_tree.current_scene.get_node_or_null("%BuffDisplay")

    # 如果没找到，尝试其他可能的路径
    if not buff_display:
        buff_display = get_node_or_null("/root/Main/BuffDisplay")
    if not buff_display:
        buff_display = get_node_or_null("../BuffDisplay")

    if buff_display and buff_display.has_method("get_buff_card_node_by_buff"):
        var buff_card_node = buff_display.get_buff_card_node_by_buff(buff)
        if buff_card_node:
            return buff_card_node

    # 如果BUFF来自其他BUFF区域节点，在这些节点中查找
    if buff_area_nodes:
        for buff_node in buff_area_nodes:
            if buff_node.has_method("get_buff_data") and buff_node.get_buff_data() == buff:
                return buff_node
            # 或者通过其他方式匹配BUFF节点
            if "buff_data" in buff_node and buff_node.buff_data == buff:
                return buff_node

    return null

# 播放BUFF卡牌震动动画
func _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes = null):
    var card_node = _get_buff_card_node(buff, hand_card_nodes, buff_area_nodes)
    if card_node:
        # 播放卡牌抖动动画，使用与原有卡牌值动画相同的效果
        var shake_tween = AnimationManager.play_card_shake_animation(card_node, 10.0, AnimationManager.get_animation_duration(0.3))
        return shake_tween
    return null

# 处理卡牌相关的BUFF
func _process_card_related_buff(buff_manager, buff, current_score, running_score, base_score, hand_card_nodes, score_value_label, score_coin):
    var updated_score = running_score
    
    # 查找对应的卡牌节点
    for card_node in hand_card_nodes.slice(0, 3):
        # 创建BUFF积分标签
        var buff_score = 0
        var score_signal = ""
        match buff.type:
            buff_manager.BuffType.SAME_RANK_ADD:
                if card_node.card_data.rank == buff.card.rank:
                    buff_score = buff.value
                    score_signal = "+"
            buff_manager.BuffType.SAME_TYPE_ADD:
                if card_node.card_data.suit == buff.card.suit:
                    buff_score = buff.value
                    score_signal = "+"
            buff_manager.BuffType.SAME_RANK_MULTIPLY:
                if card_node.card_data.rank == buff.card.rank:
                    buff_score = buff.value
                    score_signal = "x"
            buff_manager.BuffType.SAME_TYPE_MULTIPLY:
                if card_node.card_data.suit == buff.card.suit:
                    buff_score = buff.value
                    score_signal = "x"
        
        if buff_score > 0:
            # 播放卡牌抖动动画
            var shake_tween = AnimationManager.play_card_shake_animation(card_node, 10.0, AnimationManager.get_animation_duration(0.3))
            await shake_tween.finished
            var buff_score_label = _create_score_label("%s %d" % [score_signal, buff_score], card_node, 40, Color(1, 0.9, 0, 1), Color(0.8, 0.4, 0, 1))
            
            var tween = AnimationManager._play_label_animation(buff_score_label, Vector2(1.8, 1.8), card_node.size.y/6, 0.2)
            
            # 更新总积分显示
            var old_score = current_score + updated_score + base_score
            if score_signal == "+":
                updated_score += buff.value
            elif score_signal == "x":
                updated_score += int(card_node.card_data.value * (buff.value-1))
            
            # 使用积分动画更新score_label
            var new_score = current_score + updated_score + base_score
            tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
            await tween.finished
            
            buff_score_label.queue_free()
    
    return updated_score

# 处理老虎牌BUFF
func _process_tiger_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes = null, buff_area_nodes = null):
    var updated_score = running_score

    if buff.rounds_left > 0:  # 确保BUFF仍然有效
        # 播放BUFF对应卡牌的震动动画
        var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

        # 创建老虎牌的扣分效果标签
        var debuff_value = 0
        if "additional_data" in buff and "debuff_value" in buff.additional_data:
            debuff_value = buff.additional_data.debuff_value

        var tiger_label = BuffManager._create_score_label(tr("TIGER_POUNCE") % debuff_value, hand_container, buff_label_font_size, Color(1.0, 0.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0, hand_container.size.y))
        
        var tween = AnimationManager._play_label_animation(tiger_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0)
        
        # 更新积分 - 扣减分数
        var old_score = current_score + updated_score + base_score
        updated_score -= debuff_value
        var new_score = current_score + updated_score + base_score
        
        # 使用积分动画更新score_label
        tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
        await tween.finished
        
        tiger_label.queue_free()
        
        # 检查是否是最后一阶，为老虎牌奖励做准备
        if buff.rounds_left == 1:  # 下一阶将是0
            # 再次播放卡牌震动动画（奖励效果）
            var reward_shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

            var gain_label = BuffManager._create_score_label(tr("TIGER_BIG_BITE") % buff.value, hand_container, buff_label_font_size, Color(0.0, 1.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0 , hand_container.size.y))
            
            tween = AnimationManager._play_label_animation(gain_label, Vector2(2.0, 2.0), hand_container.size.y/4, 0)
            
            # 更新积分
            old_score = current_score + updated_score + base_score
            updated_score += buff.value
            new_score = current_score + updated_score + base_score
            
            tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
            await tween.finished
            
            gain_label.queue_free()
    
    return updated_score

# 处理兔子牌BUFF
func _process_rabbit_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes = null, buff_area_nodes = null):
    var updated_score = running_score
    
    if buff.rounds_left > 0:  # 确保BUFF仍然有效
        # 检查是否是首阶激活
        var is_activation_round = false
        
        # 通过检查additional_data中的activation_processed字段判断是否已经处理过激活效果
        if "additional_data" not in buff:
            buff.additional_data = {}
        
        if "activation_processed" not in buff.additional_data:
            is_activation_round = true
            buff.additional_data.activation_processed = true
        
        # 在激活时立即获得积分
        if is_activation_round:
            # 播放BUFF对应卡牌的震动动画
            var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

            var gain_label = BuffManager._create_score_label(tr("RABBIT_HOP_JUMP") % buff.value, hand_container, buff_label_font_size,  Color(0.0, 1.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0 , hand_container.size.y))
            
            var tween = AnimationManager._play_label_animation(gain_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0)
            
            # 更新积分 - 增加分数
            var old_score = current_score + updated_score + base_score
            updated_score += buff.value
            var new_score = current_score + updated_score + base_score
            
            # 使用积分动画更新score_label
            tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
            await tween.finished
            
            gain_label.queue_free()
        
        # 每阶，扣减积分
        var debuff_value = 0
        if "additional_data" in buff and "debuff_value" in buff.additional_data:
            debuff_value = buff.additional_data.debuff_value

        # 播放BUFF对应卡牌的震动动画
        var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)
        
        var rabbit_label = BuffManager._create_score_label(tr("RABBIT_CUNNING") % debuff_value, hand_container, buff_label_font_size, Color(1.0, 0.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0 , hand_container.size.y))
        
        var tween = AnimationManager._play_label_animation(rabbit_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0)
        
        # 更新积分 - 扣减分数
        var old_score = current_score + updated_score + base_score
        updated_score -= debuff_value
        var new_score = current_score + updated_score + base_score
        
        # 使用积分动画更新score_label
        tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
        await tween.finished
        
        rabbit_label.queue_free()
    
    return updated_score

# 处理青蛙牌BUFF效果
func _process_frog_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes, limit_label):
    var updated_score = running_score
    var game_manager = get_node("/root/GameManager")
    
    if not game_manager:
        return updated_score
    
    # 获取当前限制积分
    var limit_score = game_manager.limit_score
    
    # 创建动画展示限制积分值乘以buff值
    var multiplier_label = BuffManager._create_score_label(" x %s" % buff.value, limit_label, 26, Color(0.0, 1.0, 0.5, 1.0), Color(0.0, 0.5, 0.2, 1.0), Vector2(limit_label.size.x/2 + 10, limit_label.size.y *2))
    var tween = AnimationManager._play_label_animation(multiplier_label, Vector2(1.8, 1.8), -30, 0)

    # 计算新的限制积分
    var new_limit_score = limit_score * buff.value
    
    # 使用积分动画更新limit_label
    tween = AnimationManager.play_label_change_animation(limit_label, score_coin, limit_score, new_limit_score)
    await tween.finished
    multiplier_label.queue_free()
    
    # 更新游戏管理器的限制积分
    game_manager.limit_score = new_limit_score
    game_manager.limit_score_changed.emit(new_limit_score)
    
    return updated_score

# 处理乌龟牌BUFF效果
func _process_turtle_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, hand_card_nodes, buff_area_nodes = null):
    var updated_score = running_score
    var card_system = get_node("/root/CardSystem")
    
    if not card_system:
        return updated_score
    
    # 计算乌龟牌BUFF加成的分数
    var suit_multiplier = 0
    if "additional_data" in buff and "multiplier" in buff.additional_data:
        suit_multiplier = buff.additional_data.multiplier
    
    # 获取加成的花色
    var enhanced_suit = null
    if "additional_data" in buff and "enhanced_suit" in buff.additional_data:
        enhanced_suit = buff.additional_data.enhanced_suit
    
    # 如果没有指定花色，返回原始分数
    if enhanced_suit == null:
        return updated_score
    
    # 获取花色名称用于显示
    var suit_name = _get_suit_name_by_enum(enhanced_suit, card_system)

    # 遍历前三张手牌，为每张符合花色的卡牌单独显示加成效果
    for i in range(min(3, card_system.player_hand.size())):
        var card = card_system.player_hand[i]
        if card.suit == enhanced_suit:  # 使用指定花色
            # 计算这张卡的加成分数
            var card_bonus = int(card.value * (suit_multiplier - 1))

            # 获取对应的卡牌节点
            var card_node = hand_container

            # 如果提供了hand_card_nodes并且索引有效，使用对应的卡牌节点
            if hand_card_nodes and i < hand_card_nodes.size():
                card_node = hand_card_nodes[i]

                # 播放获得加成效果的卡牌震动动画
                var _card_shake_tween = AnimationManager.play_card_shake_animation(card_node, 10.0, 0.3)
            # 显示加成分数
            var bonus_label = _create_score_label(
                "x%.1f" % suit_multiplier, 
                card_node, 
                38, 
                Color(1.0, 0.7, 0.2, 1), 
                Color(0.3, 0.3, 0.3, 1)
            )
            
            var tween = AnimationManager._play_label_animation(bonus_label, Vector2(1.8, 1.8), -50, 0.2)
            
            # 更新总积分显示
            var old_score = current_score + updated_score + base_score
            updated_score += card_bonus
            var new_score = current_score + updated_score + base_score
            
            # 使用积分动画更新score_label
            tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
            await tween.finished
            
            # 清理标签
            bonus_label.queue_free()
    
    # 检查是否是最后一阶，需要扣减积分
    if buff.rounds_left == 1:
        var penalty = 0
        if "additional_data" in buff and "penalty_value" in buff.additional_data:
            penalty = buff.additional_data.penalty_value
        
        # 播放BUFF对应卡牌的震动动画（扣分时）
        var penalty_shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

        # 创建扣分效果标签
        var penalty_label = _create_score_label(tr("TURTLE_SLOW_MOVEMENT") % penalty, hand_container, buff_label_font_size, Color(1.0, 0.4, 0.4, 1), Color(0.5, 0.1, 0.1, 1), Vector2(0, -50))
        
        var tween = AnimationManager._play_label_animation(penalty_label, Vector2(1.8, 1.8), -50, 0)
        
        # 更新积分 - 扣减分数
        var old_score = current_score + updated_score + base_score
        updated_score -= penalty
        var new_score = current_score + updated_score + base_score
        
        # 使用积分动画更新score_label
        tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_score, new_score)
        await tween.finished
        
        penalty_label.queue_free()
    
    return updated_score

# 获取花色名称
func _get_suit_name_by_enum(suit_enum, card_system):
    if not card_system:
        return tr("SUIT_UNKNOWN")

    # 遍历CardSuit枚举，查找匹配的名称
    for key in card_system.CardSuit.keys():
        if card_system.CardSuit[key] == suit_enum:
            match key:
                "SPADE":
                    return tr("SUIT_SPADE")
                "HEART":
                    return tr("SUIT_HEART")
                "CLUB":
                    return tr("SUIT_CLUB")
                "DIAMOND":
                    return tr("SUIT_DIAMOND")
                _:
                    return key

    return tr("SUIT_UNKNOWN")

# 新增辅助方法 - 随机获取一个花色枚举值
func _get_random_suit(card_system):
    if card_system and "CardSuit" in card_system:
        var suit_enum = card_system.CardSuit # 获取枚举引用
        var suit_values = suit_enum.values() # 获取所有枚举值 [0, 1, 2, 3]
        if not suit_values.is_empty():
            return suit_values[randi() % (suit_values.size() -1)] # 随机选择一个枚举值，JOKER不参与随机
    return null

# 处理猩猩牌BUFF效果
func _process_gorilla_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, face_value, hand_card_nodes = null, buff_area_nodes = null):
    var updated_score = running_score
    
    if not ("additional_data" in buff and "bonus_s" in buff.additional_data and "penalty_n" in buff.additional_data):
        printerr("猩猩牌BUFF缺少additional_data！")
        return updated_score
        
    var threshold_s = buff.value # 阈值S
    var bonus_s = buff.additional_data.bonus_s # 奖励S
    var penalty_n = buff.additional_data.penalty_n # 惩罚N
    
    var current_round_face_value = face_value # 当前阶的牌面值
    
    var score_change = 0
    var label_text = ""
    var label_color = Color(1,1,1,1)

    print("牌面值: ", current_round_face_value, "阈值: ", threshold_s)

    # 播放BUFF对应卡牌的震动动画
    var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

    if current_round_face_value > threshold_s:
        score_change = bonus_s
        label_text = tr("GORILLA_POWER") % score_change
        label_color = Color(0.2, 0.8, 0.2, 1) # 绿色表示增益
    else:
        score_change = -penalty_n
        label_text = tr("GORILLA_ROAR") % score_change # 负数自带减号
        label_color = Color(0.8, 0.2, 0.2, 1) # 红色表示减益

    # 创建效果标签
    var gorilla_label = _create_score_label(
        label_text, 
        hand_container, 
        buff_label_font_size, 
        label_color, 
        Color(0.1, 0.1, 0.1, 1), 
        Vector2(0, hand_container.size.y) # 调整标签位置
    )
    
    var tween = AnimationManager._play_label_animation(gorilla_label, Vector2(1.8, 1.8), hand_container.size.y/6, 0)
    await tween.finished
    # 更新总积分显示
    var old_total_score = current_score + updated_score + base_score
    updated_score += score_change
    var new_total_score = current_score + updated_score + base_score
    
    # 使用积分动画更新score_label
    var score_tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, old_total_score, new_total_score)
    await score_tween.finished
    
    gorilla_label.queue_free()
    
    return updated_score

# 处理鲨鱼牌BUFF
func _process_shark_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, buffs_to_process):
    var updated_score = running_score
    
    var tween = create_tween()
    # 处理所有活跃的BUFF，增加它们的阶数
    for other_buff in buffs_to_process:
        # 跳过自己
        if other_buff == buff:
            continue

        if other_buff.type == BuffType.FROG:
            other_buff.rounds_left = 0
            # 创建失效提示
            var disabled_label = _create_score_label(tr("SHARK_KILL") % other_buff.description, hand_container, 36, Color(1.0, 0.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0, hand_container.size.y))
            tween = AnimationManager._play_label_animation(disabled_label, Vector2(1.5, 1.5), hand_container.size.y / 4, 0)
            await tween.finished
            disabled_label.queue_free()
            emit_signal("buff_expired", other_buff)
            continue
        
        if other_buff.type == BuffType.TURTLE:
            other_buff.rounds_left = 0
            # 创建失效提示
            var disabled_label = _create_score_label(tr("SHARK_KILL") % other_buff.description, hand_container, 36, Color(1.0, 0.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0, hand_container.size.y))
            tween = AnimationManager._play_label_animation(disabled_label, Vector2(1.5, 1.5), hand_container.size.y / 4, 0)
            await tween.finished
            disabled_label.queue_free()
            emit_signal("buff_expired", other_buff)
            continue

        # 增加其他BUFF的阶数
        if other_buff.rounds_left > 0:
            other_buff.rounds_left += buff.value
            # 创建阶增加提示
            var buff_info = _create_score_label(tr("SHARK_FIN_REWARD") % buff.value, hand_container, 36, Color(0.0, 1.0, 0.0, 1), Color(0.0, 0.0, 0.0, 1), Vector2(0, hand_container.size.y))
            tween = AnimationManager._play_label_animation(buff_info, Vector2(1.5, 1.5), hand_container.size.y / 4, 0)
            await tween.finished
            buff_info.queue_free()
            emit_signal("buff_rounds_updated", other_buff)

    # shark_label.queue_free()
    # rounds_label.queue_free()
    return updated_score

# 将 BuffType 转换为 HandType
func change_buff_to_hand(buff_type: String) -> int:
    var card_system = get_node("/root/CardSystem")
    if not card_system:
        return card_system.HandType.HIGH_CARD
        
    match buff_type:
        BuffType.HIGH_CARD_ADD:
            return card_system.HandType.HIGH_CARD
        BuffType.PAIR_ADD:
            return card_system.HandType.PAIR
        BuffType.STRAIGHT_ADD:
            return card_system.HandType.STRAIGHT
        BuffType.FLUSH_ADD:
            return card_system.HandType.FLUSH
        BuffType.STRAIGHT_FLUSH_ADD:
            return card_system.HandType.STRAIGHT_FLUSH
        BuffType.THREE_KIND_ADD:
            return card_system.HandType.THREE_OF_KIND
        BuffType.VIP_ADD:
            return card_system.HandType.SPECIAL
        _:
            return card_system.HandType.HIGH_CARD

# 处理一次性BUFF
func process_one_time_buffs(buff_manager, buffs, current_score, running_score, base_score, hand_container, buff_display, score_value_label, score_coin, action_points_label, hand_card_nodes = null, buff_area_nodes = null):
    var updated_score = running_score
    
    # 显示每个一次性BUFF的效果
    for buff in buffs:
        # 播放BUFF对应卡牌的震动动画
        var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

        # 创建BUFF标签
        var buff_label = _create_score_label(buff.description, hand_container, 38, _get_buff_color(buff.color), Color(0.3, 0.3, 0.3, 1), Vector2(0, hand_container.size.y))

        # 显示BUFF标签
        var tween = AnimationManager._play_label_animation(buff_label, Vector2(1.8, 1.8),  hand_container.size.y/4, 0.1)
        await tween.finished
        buff_label.queue_free()
        
        # 根据BUFF类型处理效果
        match buff.type:
            buff_manager.BuffType.DICE_COUNT, buff_manager.BuffType.DICE_ROLLS, buff_manager.BuffType.HAND_LIMIT:
                # 发送信号以添加永久BUFF图标
                permanent_buff_added.emit({
                    "icon": BUFF_TEXTURES.get(buff.type, "res://assert/sign/default.png"),
                    "description": buff.description
                })
                
            buff_manager.BuffType.ACTION_POINTS_ADD:
                updated_score = await _process_action_points_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin,action_points_label)
            
            buff_manager.BuffType.SHARK:
                # 处理鲨鱼牌BUFF
                var all_active_buffs = []
                for active_buff in buff_manager.active_buffs:
                    if not all_active_buffs.has(active_buff):
                        all_active_buffs.append(active_buff)
                for area_buff in buff_manager.buff_area:
                    if not all_active_buffs.has(area_buff):
                        all_active_buffs.append(area_buff)
                       
                updated_score = await _process_shark_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, null, null, all_active_buffs)
                
            buff_manager.BuffType.EAGLE:
                # 处理老鹰牌BUFF
                # 获取所有当前有效的BUFF
                var all_active_buffs = []
                for active_buff in buff_manager.active_buffs:
                    if not all_active_buffs.has(active_buff):
                        all_active_buffs.append(active_buff)
                for area_buff in buff_manager.buff_area:
                    if not all_active_buffs.has(area_buff):
                        all_active_buffs.append(area_buff)
                        
                updated_score = await _process_eagle_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, null, null, all_active_buffs)
                
            buff_manager.BuffType.CARP:
                # 处理锦鲤牌BUFF
                var total_score = current_score + updated_score + base_score
                updated_score = await _process_carp_buff(buff_manager, buff, current_score, updated_score, base_score, hand_container, score_value_label, score_coin, total_score)
    return updated_score

func hand_type_buff(hand_container, hand_card_nodes = null, buff_area_nodes = null):
    for buff in active_buffs:
        # 检查buff.type是否是牌型加成类型
        if buff.type in [BuffType.HIGH_CARD_ADD, BuffType.PAIR_ADD, BuffType.STRAIGHT_ADD, 
                        BuffType.FLUSH_ADD, BuffType.STRAIGHT_FLUSH_ADD, BuffType.THREE_KIND_ADD, 
                        BuffType.VIP_ADD]:

            # 获取卡牌系统
            var card_system = get_node("/root/CardSystem")
            if card_system:
                # 获取buff对应的牌型
                var hand_type = change_buff_to_hand(buff.type)
                # 获取规则ID
                var rule_id = card_system.get_hand_type_rule_id(hand_type)
                
                # 更新游戏规则配置
                update_user_game_rules(rule_id, buff.value)
                
                # 播放BUFF对应卡牌的震动动画
                var shake_tween = _play_buff_card_shake_animation(buff, hand_card_nodes, buff_area_nodes)

                # 创建牌型BUFF加成标签
                var hand_type_name = card_system.get_localized_hand_type_name_by_type(hand_type) # 使用国际化名称
                var bonus_label = _create_score_label(tr("HAND_TYPE_BONUS") % [hand_type_name, buff.value], hand_container, buff_label_font_size, _get_buff_color(buff.color), Color(0.3, 0.2, 0.1, 1.0), Vector2(0, hand_container.size.y))
                var tween = AnimationManager._play_label_animation(bonus_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0)
                await tween.finished
                bonus_label.queue_free()

# 处理BUFF效果
func process_buffs(buff_manager, current_score, running_score, base_score, hand_container, buff_display, score_value_label, score_coin, limit_label, hand_card_nodes, action_points_label, face_value, buff_area_nodes = null):
    var updated_score = running_score
    
    # 合并所有激活的BUFF
    var all_buffs = []
    # 首先处理需要进BUFF区的持续性BUFF
    var persistent_buff_types = [buff_manager.BuffType.SAME_RANK_ADD, buff_manager.BuffType.SAME_TYPE_ADD, 
                                buff_manager.BuffType.SAME_RANK_MULTIPLY, buff_manager.BuffType.SAME_TYPE_MULTIPLY,
                                buff_manager.BuffType.ROUND_SCORE_ADD,
                                buff_manager.BuffType.TIGER,buff_manager.BuffType.RABBIT,buff_manager.BuffType.FROG,
                                buff_manager.BuffType.TURTLE, buff_manager.BuffType.GORILLA,
                                buff_manager.BuffType.DRAW_COST_REDUCE]
    var one_time_buff_types = [buff_manager.BuffType.DICE_COUNT, buff_manager.BuffType.DICE_ROLLS, buff_manager.BuffType.HAND_LIMIT,
                               buff_manager.BuffType.SHARK, buff_manager.BuffType.EAGLE, buff_manager.BuffType.CARP,
                               buff_manager.BuffType.ACTION_POINTS_ADD]
    for buff in buff_manager.active_buffs:
        if buff["type"] in persistent_buff_types:
            all_buffs.append(buff)
    for buff in buff_manager.buff_area:
        if not all_buffs.has(buff) and buff["type"] in persistent_buff_types:
            all_buffs.append(buff)
    
    # 再处理一次性BUFF（不进入BUFF区）
    var one_time_buffs = []
    for buff in buff_manager.active_buffs:
        if buff["type"] in one_time_buff_types:
            one_time_buffs.append(buff)
    
    # 处理持续性和一次性BUFF
    updated_score = await process_persistent_buffs(buff_manager, all_buffs, current_score, updated_score, base_score, hand_container, buff_display, score_value_label, score_coin, limit_label, hand_card_nodes, face_value, buff_area_nodes)
    updated_score = await process_one_time_buffs(buff_manager, one_time_buffs, current_score, updated_score, base_score, hand_container, buff_display, score_value_label, score_coin, action_points_label, hand_card_nodes, buff_area_nodes)
    
    return updated_score

# 处理老鹰牌BUFF效果
func _process_eagle_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, buffs_to_process):
    var updated_score = running_score
    
    var tween = create_tween()
    # 处理所有活跃的BUFF
    for other_buff in buffs_to_process:
        # 跳过自己
        if other_buff == buff:
            continue
        
        # 使兔子牌立即失效
        if other_buff.type == BuffType.RABBIT:
            other_buff.rounds_left = 0
            # 创建失效提示
            var disabled_label = _create_score_label(tr("EAGLE_SOUL_STRIKE") % other_buff.description, hand_container, 36, Color(1.0, 0.3, 0.3, 1), Color(0.5, 0.1, 0.1, 1), Vector2(0, hand_container.size.y))
            tween = AnimationManager._play_label_animation(disabled_label, Vector2(1.5, 1.5), hand_container.size.y/4, 0)
            await tween.finished
            disabled_label.queue_free()
            emit_signal("buff_expired", other_buff)
            continue
        
        # 老虎牌和猩猩牌阶数乘以N
        if other_buff.type == BuffType.TIGER or other_buff.type == BuffType.GORILLA:
            if other_buff.rounds_left > 0:
                var old_rounds = other_buff.rounds_left
                other_buff.rounds_left = old_rounds * buff.value
                # 创建阶增加提示
                var buff_info = _create_score_label(tr("EAGLE_SPREAD_WINGS") % [other_buff.description, buff.value], hand_container, 36, Color(0.3, 0.9, 0.6, 1), Color(0.1, 0.4, 0.3, 1), Vector2(0, hand_container.size.y))
                tween = AnimationManager._play_label_animation(buff_info, Vector2(1.5, 1.5), hand_container.size.y/4, 0)
                await tween.finished
                buff_info.queue_free()
                emit_signal("buff_rounds_updated", other_buff)
    
    return updated_score

# 处理锦鲤牌BUFF效果
func _process_carp_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin, total_score):
    var updated_score = running_score
    
    # 确保不为null
    if not score_value_label or not score_coin:
        print("锦鲤牌BUFF缺少必要的UI元素")
        return total_score * buff.value - current_score - base_score

    # 创建锦鲤牌的加成分数标签
    var bonus_text = tr("CARP_LEAP_DRAGON_GATE") % buff.value
    var bonus_label = _create_score_label(bonus_text, hand_container, buff_label_font_size, Color(1.0, 0.8, 0.2, 1.0), Color(0.3, 0.2, 0.1, 1.0), Vector2(0, hand_container.size.y))
    var tween = AnimationManager._play_label_animation(bonus_label, Vector2(1.8, 1.8), hand_container.size.y/4, 0)

    # 计算新的总积分
    var new_total = total_score * buff.value
    var bonus = new_total - total_score
    updated_score = running_score + bonus
    
    # 使用积分动画更新score_label
    tween = AnimationManager.play_score_change_animation(score_value_label, score_coin, total_score, new_total)
    await tween.finished
    bonus_label.queue_free()
    
    return updated_score

# 处理行动点数增加BUFF
func _process_action_points_buff(buff_manager, buff, current_score, running_score, base_score, hand_container, score_value_label, score_coin,action_points_label):
    var updated_score = running_score
    
    # 处理行动点数增加BUFF
    var game_manager = get_node("/root/GameManager")
    if game_manager and game_manager.has_method("add_action_points"):
        # 行动点数变化动画
        AnimationManager.play_label_change_animation(action_points_label,null,GameManager.action_points,GameManager.action_points+buff.value)
        game_manager.add_action_points(buff.value)
    return updated_score

# 更新游戏规则配置（仅在内存中修改）
func update_user_game_rules(rule_id: String, bonus_score: int):
    if rule_id in game_rules_values:
        game_rules_values[rule_id].bonus_score += bonus_score
        print("更新规则配置: ", rule_id, " bonus_score += ", bonus_score, " 总计: ", game_rules_values[rule_id].bonus_score)
    else:
        print("未找到规则ID: ", rule_id)

# 重置游戏规则的奖励分数（新游戏时调用）
func reset_game_rules_bonus():
    for rule_id in game_rules_values.keys():
        game_rules_values[rule_id].bonus_score = 0
    print("已重置所有游戏规则的奖励分数")

# 获取BUFF类型名称
func get_buff_type_name(buff_type: String) -> String:
    # 先从普通BUFF池中查找
    for buff in normal_buff_pool:
        if buff.type == buff_type:
            return _get_localized_value(buff.display_name, TranslationServer.get_locale())
    
    # 再从特殊BUFF池中查找
    for buff in zoo_buff_pool:
        if buff.type == buff_type:
            return _get_localized_value(buff.display_name, TranslationServer.get_locale())
    
    # 如果都没找到，返回默认值
    return tr("UNKNOWN_BUFF")

# 获取BUFF解释模板
func get_buff_explanation_template(buff_type: String) -> String:
    # 先从普通BUFF池中查找
    for buff in normal_buff_pool:
        if buff.type == buff_type:
            return _get_localized_value(buff.explanation_template, TranslationServer.get_locale())
    
    # 再从特殊BUFF池中查找
    for buff in zoo_buff_pool:
        if buff.type == buff_type:
            return _get_localized_value(buff.explanation_template, TranslationServer.get_locale())
    
    # 如果都没找到，返回默认值
    return tr("BUFF_UNCLEAR")

# 获取BUFF详细解释
func get_buff_explanation(buff_type, buff_value, buff_manager, rounds_left, additional_data):
    var value_color = _get_value_color_code(buff_value)
    var penalty_color = _get_value_color_code(additional_data.get("penalty_n", 3), true) # 用于猩猩牌的惩罚
    var bonus_color = _get_value_color_code(additional_data.get("bonus_s", 10))     # 用于猩猩牌的奖励

    # 获取解释模板
    var template = get_buff_explanation_template(buff_type)
    
    # 根据不同的BUFF类型，准备不同的参数
    match buff_type:
        BuffType.ROUND_SCORE_ADD, BuffType.SAME_RANK_ADD, BuffType.SAME_TYPE_ADD, \
        BuffType.SAME_RANK_MULTIPLY, BuffType.SAME_TYPE_MULTIPLY, \
        BuffType.DICE_COUNT, BuffType.DICE_ROLLS, BuffType.HAND_LIMIT, \
        BuffType.ACTION_POINTS_ADD, BuffType.HIGH_CARD_ADD, BuffType.PAIR_ADD, \
        BuffType.STRAIGHT_ADD, BuffType.FLUSH_ADD, BuffType.STRAIGHT_FLUSH_ADD, \
        BuffType.THREE_KIND_ADD, BuffType.VIP_ADD:
            return template % [value_color, buff_value]
            
        BuffType.TIGER:
            var debuff_color_tiger = _get_value_color_code(additional_data.get("debuff_value", 2), true)
            var buff_color_tiger = _get_value_color_code(buff_value)
            return template % [
                debuff_color_tiger, additional_data.get("debuff_value", 2),
                _get_rounds_color_code(rounds_left), rounds_left,
                buff_color_tiger, buff_value
            ]
            
        BuffType.RABBIT:
            var debuff_color_rabbit = _get_value_color_code(additional_data.get("debuff_value", 2), true)
            var buff_color_rabbit = _get_value_color_code(buff_value)
            return template % [
                buff_color_rabbit, buff_value,
                debuff_color_rabbit, additional_data.get("debuff_value", 2)
            ]
            
        BuffType.FROG:
            var suit_info_frog = {"name": tr("SUIT_UNKNOWN"), "color": "#FFFFFF"}
            if "forbidden_suit" in additional_data:
                suit_info_frog = _get_suit_info(additional_data["forbidden_suit"])
            var value_color_code_frog = _get_value_color_code(buff_value)
            return template % [
                value_color_code_frog, buff_value,
                suit_info_frog.color, suit_info_frog.name
            ]

        BuffType.TURTLE:
            var multiplier_color_turtle = _get_value_color_code(buff_value)
            var penalty_color_turtle = _get_value_color_code(additional_data.get("penalty_value", 10), true)
            var suit_info_turtle = {"name": tr("SUIT_UNKNOWN"), "color": "#FFFFFF"}
            if "enhanced_suit" in additional_data:
                suit_info_turtle = _get_suit_info(additional_data["enhanced_suit"])
            return template % [
                suit_info_turtle.color, suit_info_turtle.name,
                multiplier_color_turtle, buff_value,
                _get_rounds_color_code(rounds_left), rounds_left,
                penalty_color_turtle, additional_data.get("penalty_value", 10)
            ]
            
        BuffType.GORILLA:
            var threshold_s = buff_value
            var bonus_s_val = additional_data.get("bonus_s", threshold_s)
            var penalty_n_val = additional_data.get("penalty_n", 3)
            return template % [
                value_color, threshold_s,
                bonus_color, bonus_s_val,
                value_color, threshold_s,
                penalty_color, penalty_n_val
            ]
            
        BuffType.SHARK:
            var bonus_rounds = buff_value
            var disabled_buffs = tr("DISABLED_BUFFS_FROG_TURTLE")
            return template % [
                bonus_color, bonus_rounds,
                penalty_color, disabled_buffs
            ]

        BuffType.EAGLE:
            var multiplier = buff_value
            var enhanced_buffs = tr("DISABLED_BUFFS_RABBIT")
            var disabled_buffs = tr("ENHANCED_BUFFS_TIGER_GORILLA")
            return template % [
                bonus_color, disabled_buffs,
                bonus_color, multiplier,
                penalty_color, enhanced_buffs
            ]
            
        BuffType.CARP:
            var multiplier_color = _get_value_color_code(buff_value)
            return template % [multiplier_color, buff_value]
            
        _:
            return template

# 获取值的颜色代码
func _get_value_color_code(value: float, is_negative: bool = false) -> String:
    if is_negative:
        return "#FF0000"  # 红色用于负面效果
    else:
        return "#00FF00"  # 绿色用于正面效果

func _get_rounds_color_code(rounds_left: int) -> String:
    if rounds_left <= 1:
        return "#FF0000"  # 红色用于负面效果
    elif rounds_left <= 3:
        return "#FFAA33"  # 橙色警告
    else:
        return "#FFFF33"  # 黄色

# 获取花色信息
# func _get_suit_info_back(suit_enum) -> Dictionary:
#     var card_system = get_node("/root/CardSystem")
#     if not card_system:
#         return {"name": "未知花色", "color": "#FFFFFF"}
    
#     # 遍历CardSuit枚举，查找匹配的名称和颜色
#     for key in card_system.CardSuit.keys():
#         if card_system.CardSuit[key] == suit_enum:
#             match key:
#                 "SPADE":
#                     return {"name": "黑桃", "color": "#000000"}
#                 "HEART":
#                     return {"name": "红桃", "color": "#FF0000"}
#                 "CLUB":
#                     return {"name": "梅花", "color": "#008000"}
#                 "DIAMOND":
#                     return {"name": "方块", "color": "#FF0000"}
#                 _:
#                     return {"name": key, "color": "#FFFFFF"}
    
#     return {"name": "未知花色", "color": "#FFFFFF"}

func _get_suit_info(suit_enum_value):
    var suit_info = {
        "name": tr("SUIT_UNKNOWN"),
        "color": "#FFFFFF" # 默认白色
    }
    
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if card_system:
        for key in card_system.CardSuit.keys():
            if card_system.CardSuit[key] == suit_enum_value:
                match key:
                    "SPADE":
                        suit_info.name = tr("SUIT_SPADE_WITH_ICON")
                        suit_info.color = "#FF00FF" # 紫色
                    "HEART":
                        suit_info.name = tr("SUIT_HEART_WITH_ICON")
                        suit_info.color = "#FF3333" # 红色
                    "CLUB":
                        suit_info.name = tr("SUIT_CLUB_WITH_ICON")
                        suit_info.color = "#00CC00" # 绿色
                    "DIAMOND":
                        suit_info.name = tr("SUIT_DIAMOND_WITH_ICON")
                        suit_info.color = "#3333FF" # 蓝色
                    _:
                        suit_info.name = key
                break
    
    return suit_info

# 当语言改变时重新加载配置
func _on_language_changed():
    print("buff_manager语言以切换为：", TranslationServer.get_locale())
    load_normal_buff_config()
    load_zoo_buff_config()

extends Control

const BUFF_ICON_SCENE = preload("res://scence/forever_buff_icon.tscn")
const MIN_SEPARATION = 2  # 最小间距
const DEFAULT_SEPARATION = 15  # 默认间距
const MAX_WIDTH = 484  # 最大宽度

@onready var buff_container: HBoxContainer = $BuffContainer

signal buff_tooltip_requested(buff_data: Dictionary, global_pos: Vector2)
signal buff_tooltip_hide_requested

# 存储BUFF的叠加信息
var buff_stacks: Dictionary = {}  # key: buff_type, value: {count: int, icon_node: Node}

func _ready() -> void:
    # 确保容器大小正确
    custom_minimum_size = Vector2(MAX_WIDTH, 30)
    size = custom_minimum_size

func add_buff(buff_data: Dictionary) -> void:
    # 获取BUFF类型作为唯一标识
    var buff_type = buff_data.get("type", "unknown")

    # 检查是否已存在相同类型的BUFF
    if buff_stacks.has(buff_type):
        # 增加叠加次数
        buff_stacks[buff_type].count += 1
        var existing_icon = buff_stacks[buff_type].icon_node

        # 更新叠加次数显示
        _update_stack_count_display(existing_icon, buff_stacks[buff_type].count)

        # 更新BUFF数据以包含叠加次数
        existing_icon.buff_data["stack_count"] = buff_stacks[buff_type].count

        # 动态更新描述，根据叠加次数计算总数值
        var total_value = buff_data.get("value", 0) * buff_stacks[buff_type].count
        var raw_description = existing_icon.buff_data.get("raw_description", "UNKNOWN")


        # 使用数组参数进行格式化，正确处理百分号
        if(buff_type == "demon_whisper"):
            existing_icon.buff_data["description"] = raw_description % [str(total_value), str(total_value * 10)]
        else:
            existing_icon.buff_data["description"] = raw_description % [str(total_value)]

        # 播放叠加动画
        AnimationManager.play_buff_icon_spawn_animation(existing_icon)
    else:
        # 创建新的BUFF图标
        var buff_icon = BUFF_ICON_SCENE.instantiate()
        buff_container.add_child(buff_icon)

        # 添加叠加次数到BUFF数据
        var enhanced_buff_data = buff_data.duplicate()
        enhanced_buff_data["stack_count"] = 1

        # 保存原始描述模板，用于后续叠加时的动态计算
        if not enhanced_buff_data.has("raw_description"):
            enhanced_buff_data["raw_description"] = enhanced_buff_data.get("description", "")

        # 如果描述包含占位符，进行初始替换
        var description = enhanced_buff_data.get("description", "")

        # 确保 description 是字符串类型
        if typeof(description) != TYPE_STRING:
            description = str(description)

        if description.find("%s") != -1:
            var value = enhanced_buff_data.get("value", 0)
            # 使用数组参数进行格式化，正确处理百分号
            enhanced_buff_data["description"] = description % [str(value)]

        buff_icon.setup(enhanced_buff_data)

        # 连接信号
        buff_icon.mouse_entered_buff.connect(_on_buff_mouse_entered)
        buff_icon.mouse_exited_buff.connect(_on_buff_mouse_exited)

        # 记录到叠加信息中
        buff_stacks[buff_type] = {
            "count": 1,
            "icon_node": buff_icon
        }

        # 创建叠加次数显示（初始隐藏，因为只有1层）
        _create_stack_count_display(buff_icon)

        # 播放出现动画
        AnimationManager.play_buff_icon_spawn_animation(buff_icon)

        # 调整间距以适应新的图标
        _adjust_spacing()

func _adjust_spacing() -> void:
    var total_icons = buff_container.get_child_count()
    if total_icons == 0:
        return
        
    var icon_width = 40  # 每个图标的宽度
    var available_width = MAX_WIDTH
    
    # 计算需要的总宽度（使用默认间距）
    var required_width = (total_icons * icon_width) + ((total_icons - 1) * DEFAULT_SEPARATION)
    
    if required_width <= available_width:
        # 如果空间足够，使用默认间距
        buff_container.set("theme_override_constants/separation", DEFAULT_SEPARATION)
    else:
        # 如果空间不够，计算新的间距，允许负值（重叠）
        var new_separation = (available_width - (total_icons * icon_width)) / (total_icons - 1)
        buff_container.set("theme_override_constants/separation", new_separation)

func _on_buff_mouse_entered(buff_data: Dictionary) -> void:
    var mouse_pos = get_viewport().get_mouse_position()

    # 创建增强的BUFF数据，包含叠加次数信息
    var enhanced_buff_data = buff_data.duplicate()
    var stack_count = buff_data.get("stack_count", 1)
    enhanced_buff_data["stack_count"] = stack_count

    buff_tooltip_requested.emit(enhanced_buff_data, mouse_pos)

func _on_buff_mouse_exited() -> void:
    buff_tooltip_hide_requested.emit()

# 创建叠加次数显示（现在使用场景中已有的Ball/ColorRect节点）
func _create_stack_count_display(buff_icon: Control) -> void:
    # 获取Ball/ColorRect节点（应该已经在forever_buff_icon场景中存在）
    var ball_node = buff_icon.get_node_or_null("Ball")
    if not ball_node:
        print("警告: 未找到Ball节点")
        return

    var color_rect = ball_node.get_node_or_null("ColorRect")
    if not color_rect:
        print("警告: 未找到Ball/ColorRect节点")
        return

    # 设置鼠标过滤器，让鼠标事件穿透到下层节点
    color_rect.mouse_filter = Control.MOUSE_FILTER_IGNORE

    # 同样设置Label的鼠标过滤器
    var stack_label = color_rect.get_node_or_null("Label")
    if stack_label:
        stack_label.mouse_filter = Control.MOUSE_FILTER_IGNORE

    # 初始隐藏叠加次数显示
    color_rect.visible = false

# 更新叠加次数显示
func _update_stack_count_display(buff_icon: Control, count: int) -> void:
    # 获取Ball/ColorRect节点
    var ball_node = buff_icon.get_node_or_null("Ball")
    if not ball_node:
        print("警告: 未找到Ball节点")
        return

    var color_rect = ball_node.get_node_or_null("ColorRect")
    if not color_rect:
        print("警告: 未找到Ball/ColorRect节点")
        return

    var stack_label = color_rect.get_node_or_null("Label")
    if not stack_label:
        print("警告: 未找到Ball/ColorRect/Label节点")
        return

    # 更新数字
    stack_label.text = str(count)

    # 显示或隐藏叠加次数（只有大于1时才显示）
    color_rect.visible = count > 1

    # 播放更新动画
    if count > 1:
        var tween = create_tween()
        tween.set_parallel(true)
        # 缩放动画
        color_rect.scale = Vector2(1.3, 1.3)
        tween.tween_property(color_rect, "scale", Vector2.ONE, 0.3)

        # 颜色闪烁动画（Panel节点使用modulate属性）
        var original_modulate = color_rect.modulate
        tween.tween_property(color_rect, "modulate", Color(1.0, 1.0, 0.0, 1.0), 0.15)
        tween.tween_property(color_rect, "modulate", original_modulate, 0.15)

func clear_buffs() -> void:
    for child in buff_container.get_children():
        child.queue_free()
    buff_stacks.clear()  # 清空叠加信息
    _adjust_spacing()

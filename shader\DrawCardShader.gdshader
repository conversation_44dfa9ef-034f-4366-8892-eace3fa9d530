shader_type canvas_item;
render_mode unshaded;

uniform sampler2D	noise_tex			: hint_default_white, repeat_enable;
uniform vec4		root_color			: source_color = vec4(1.0, 0.75, 0.3, 1.0);
uniform vec4		tip_color			: source_color = vec4(1.0, 0.03, 0.001, 1.0);
uniform float		poster_color		: hint_range(0.0, 16.0) = 6.0;

uniform float		fire_alpha			: hint_range(0.0, 1.0) = 1.0;
uniform vec2		fire_speed			= vec2(0.015, 0.5);
uniform float		fire_aperture		: hint_range(0.0, 5.0) = 0.22;

uniform float		vignette_radius		: hint_range(0.0, 1.0) = 0.25; //fade start
uniform float		vignette_falloff	: hint_range(0.0, 0.5) = 0.25; //fade width
uniform float		noise_influence		: hint_range(0.0, 1.0) = 1.00; //noise influence

// 圆角半径 (0.0-0.5)，值为0.5时为圆形
uniform float corner_radius : hint_range(0.0, 0.5) = 0.15;
// 边缘平滑程度
uniform float edge_smoothness : hint_range(0.0, 0.1) = 0.01;

vec2 polar_coordinates(vec2 uv, vec2 center, float zoom, float repeat) {
    vec2 d = uv - center;
    float r     = length(d) * 2.0;
    float theta = atan(d.y, d.x) * (1.0 / (2.0 * 3.1416));
    return mod(vec2(r * zoom, theta * repeat), 1.0);
}

void fragment() {
	vec2  center = vec2(0.5);
	vec2  p = polar_coordinates(UV, center, 1.0, 1.0);

	p.x += TIME * fire_speed.y;
	p.y += sin(TIME) * fire_speed.x;

	float n = texture(noise_tex, p).r;

	float dist = distance(UV, center);
	float edge = clamp(1.0 - dist, 0.0, 1.0);
	float noise_val = edge * (((edge + fire_aperture) * n - fire_aperture) * 75.0);
	noise_val = clamp(noise_val, 0.0, 1.0);

	float effective_radius = vignette_radius + n * noise_influence * vignette_falloff;
	float mask = smoothstep(effective_radius + vignette_falloff, effective_radius, 1.0-dist);

	float alpha = noise_val * fire_alpha * mask;

	vec4 fire_color;
	if (poster_color >= 1.0){
		float quantized = floor(n * poster_color) / poster_color;
		fire_color = mix(tip_color, root_color, quantized);
		alpha = floor(alpha * poster_color) / poster_color;
	}
	else{
		fire_color = mix(tip_color, root_color, n);
	}
	
	COLOR = vec4(fire_color.rgb, alpha);
    
    // 获取UV坐标相对于中心的位置 (0.5, 0.5)
    vec2 center_distance = abs(UV - vec2(0.5));
    
    // 计算到最近卡片角落的距离
    float corner_distance = length(max(center_distance - vec2(0.5 - corner_radius), vec2(0.0)));
    
    // 创建圆角蒙版 - 使用smoothstep创建平滑边缘
    float corner_mask = 1.0 - smoothstep(corner_radius - edge_smoothness, corner_radius + edge_smoothness, corner_distance);
    
    // 获取原始像素颜色
    vec4 color = COLOR;
    
    // 应用圆角蒙版到alpha通道
    color.a *= corner_mask;
    
    // 输出最终颜色
    COLOR = color;
}
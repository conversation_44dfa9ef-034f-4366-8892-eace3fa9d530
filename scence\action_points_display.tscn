[gd_scene load_steps=3 format=3 uid="uid://b8sul5q5fku3m"]

[ext_resource type="Script" uid="uid://517syyxwcswf" path="res://script/action_points_display.gd" id="1_apd"]
[ext_resource type="Texture2D" uid="uid://gy48upbnnj4i" path="res://assert/top/ap.png" id="2_om1vx"]

[node name="ActionPointsDisplay" type="HBoxContainer"]
custom_minimum_size = Vector2(120, 32)
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -60.0
offset_right = 60.0
offset_bottom = 32.0
grow_horizontal = 2
script = ExtResource("1_apd")

[node name="TextureRect" type="TextureRect" parent="."]
custom_minimum_size = Vector2(32, 0)
layout_mode = 2
texture = ExtResource("2_om1vx")
expand_mode = 1
stretch_mode = 5

[node name="ActionPointsTitle" type="Label" parent="."]
visible = false
layout_mode = 2
theme_override_colors/font_color = Color(0.462745, 0.286275, 0.215686, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_constants/shadow_outline_size = 0
theme_override_font_sizes/font_size = 15
text = "ActionPointsTitle"
horizontal_alignment = 1
vertical_alignment = 1
uppercase = true

[node name="ActionPointsLabel" type="Label" parent="."]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 15
text = "99"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ReductionHint" type="Label" parent="."]
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.2, 0.2, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_font_sizes/font_size = 10
text = "(-1)"
horizontal_alignment = 1
vertical_alignment = 1

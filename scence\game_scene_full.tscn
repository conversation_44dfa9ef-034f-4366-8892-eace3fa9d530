[gd_scene load_steps=56 format=3 uid="uid://dijco185i3u3y"]

[ext_resource type="Script" uid="uid://bdckwnsl4x8sj" path="res://script/game_scene_full.gd" id="1_i5t63"]
[ext_resource type="Texture2D" uid="uid://u4783assfjdl" path="res://assert/back/game_back03.svg" id="2_3oi1i"]
[ext_resource type="Texture2D" uid="uid://ch8up80x4otwr" path="res://assert/desk/stand_brand2.png" id="3_w3ro0"]
[ext_resource type="Shader" uid="uid://cy3xmux52lbs5" path="res://shader/fire_shader.gdshader" id="4_3oi1i"]
[ext_resource type="Texture2D" uid="uid://bfipf8ag7jh1x" path="res://assert/back/mask03.png" id="5_3oi1i"]
[ext_resource type="Texture2D" uid="uid://dk6251badlctf" path="res://assert/top/score.png" id="8_w3ro0"]
[ext_resource type="Texture2D" uid="uid://dqm8qdxpbjliq" path="res://assert/top/goal.png" id="9_8xfhs"]
[ext_resource type="Texture2D" uid="uid://c773egnyxi7s4" path="res://assert/top/setting04.png" id="9_nyhfq"]
[ext_resource type="Texture2D" uid="uid://bd2gpfw86d8wi" path="res://assert/back/jian.png" id="10_unjts"]
[ext_resource type="Texture2D" uid="uid://sdw63mgmj4ql" path="res://assert/right/draw_cards.png" id="14_am34o"]
[ext_resource type="Texture2D" uid="uid://pecj6tmfyxj7" path="res://assert/right/close.png" id="17_am34o"]
[ext_resource type="Gradient" uid="uid://cm7etr2owauld" path="res://themes/arrow_gradient.tres" id="18_am34o"]
[ext_resource type="Texture2D" uid="uid://buayqt5gmp10x" path="res://assert/right/arrow.png" id="18_makri"]
[ext_resource type="Texture2D" uid="uid://bdmt6xsae12nm" path="res://assert/items/Scroll.png" id="19_gcft7"]
[ext_resource type="Texture2D" uid="uid://bijtbyig2esp6" path="res://assert/right/add.png" id="20_af2fc"]
[ext_resource type="Texture2D" uid="uid://bkm6ykoarn05j" path="res://assert/right/change.png" id="21_2rdal"]
[ext_resource type="Shader" uid="uid://1exytwf4iq5n" path="res://shader/progress_shader01.gdshader" id="21_aiowq"]
[ext_resource type="Texture2D" uid="uid://brqhbhlbvw6dt" path="res://assert/particles/64x64_Aura_8.png" id="21_gibhg"]
[ext_resource type="FontFile" uid="uid://cdnwx7btcvfpe" path="res://fonts/siyuan.ttf" id="21_y73b8"]
[ext_resource type="Texture2D" uid="uid://cffn558216bfm" path="res://assert/coins/lvl.png" id="22_x7d8u"]
[ext_resource type="Texture2D" uid="uid://bq425gqnty6ym" path="res://assert/coins/round.png" id="23_vn7j2"]
[ext_resource type="PackedScene" uid="uid://b8sul5q5fku3m" path="res://scence/action_points_display.tscn" id="24_5jn0b"]
[ext_resource type="Texture2D" uid="uid://dcuo1q7sr1yk1" path="res://assert/left/button06.png" id="25_w3ro0"]
[ext_resource type="Shader" uid="uid://ivdl3r1frecv" path="res://shader/end_button_shader.gdshader" id="29_djko6"]
[ext_resource type="PackedScene" uid="uid://ku8knq5neeuw" path="res://scence/forever_buff_area.tscn" id="30_sg5rm"]
[ext_resource type="Texture2D" uid="uid://bgafe1diiys8a" path="res://assert/items/feather02.png" id="31_auu3w"]
[ext_resource type="Shader" uid="uid://7u7bhjj6ew35" path="res://shader/featherShader.gdshader" id="32_5t3w8"]
[ext_resource type="Shader" uid="uid://cbcr2axu63uwt" path="res://shader/featherShader2.gdshader" id="33_3a3wy"]
[ext_resource type="Shader" uid="uid://lfhklunjrc6a" path="res://shader/DrawCardShader.gdshader" id="35_dcgvf"]
[ext_resource type="Texture2D" uid="uid://yu1s2ye2y0lj" path="res://assert/cards/cardback02.png" id="36_glycf"]
[ext_resource type="PackedScene" uid="uid://c8q6y7j5n4g8t" path="res://scence/dice_panel.tscn" id="38_h25xd"]
[ext_resource type="PackedScene" uid="uid://bxnv6cucb8e66" path="res://scence/settings_panel.tscn" id="39_lab21"]
[ext_resource type="PackedScene" uid="uid://bhd0lqeb1ab75" path="res://scence/game_over_panel.tscn" id="40_4de3h"]
[ext_resource type="PackedScene" uid="uid://q8gc6kybbkr3" path="res://scence/victory_panel.tscn" id="41_sk0wp"]
[ext_resource type="PackedScene" uid="uid://dpg62rdk3ga12" path="res://scence/tooltip_bubble.tscn" id="42_xcj5d"]
[ext_resource type="PackedScene" uid="uid://de33uixbyqdd6" path="res://scence/rule_panel.tscn" id="43_rwg48"]
[ext_resource type="PackedScene" uid="uid://riqfmggkqpfd" path="res://scence/settings_menu_panel.tscn" id="44_s044x"]
[ext_resource type="Script" uid="uid://c8gk1c6orjeca" path="res://script/buff_display.gd" id="45_ks1cw"]
[ext_resource type="PackedScene" uid="uid://bal47pdc0c2xk" path="res://scence/UPLevelScene.tscn" id="46_n5epk"]
[ext_resource type="PackedScene" uid="uid://cqsdvuoy51up4" path="res://scence/debug_panel.tscn" id="47_bk8w8"]
[ext_resource type="PackedScene" uid="uid://c8443w5xhulcw" path="res://scence/card_rules_panel.tscn" id="48_kdmsa"]
[ext_resource type="PackedScene" uid="uid://c8k2j5x7n6m4d" path="res://scence/tutorial_overlay.tscn" id="49_s049x"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_hfa1t"]
shader = ExtResource("4_3oi1i")
shader_parameter/fire_color = Color(1, 0.329412, 1, 1)
shader_parameter/fire_power_factor = -0.82
shader_parameter/fire_intensity = 0.875
shader_parameter/fire_start = 1.0
shader_parameter/ball_size = 2.0
shader_parameter/roughness = 0.675
shader_parameter/detail = 8
shader_parameter/scale = 4.0
shader_parameter/lacunarity = 1.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_wpqdo"]
shader = ExtResource("21_aiowq")
shader_parameter/u_aspect = 20.0
shader_parameter/progress_color = Color(0.835, 0, 0.739, 1)
shader_parameter/background_color = Color(0.442, 0.002, 0.984, 1)
shader_parameter/outline_color = Color(1, 0.31, 0.208, 1)
shader_parameter/progress = 0.5

[sub_resource type="ShaderMaterial" id="ShaderMaterial_awo82"]
shader = ExtResource("32_5t3w8")
shader_parameter/fire_color = Color(0, 1, 1, 1)
shader_parameter/fire_power_factor = 2.0
shader_parameter/fire_intensity = 1.5
shader_parameter/fire_start = 5.0
shader_parameter/ball_size = 1.7
shader_parameter/roughness = 0.675
shader_parameter/detail = 6
shader_parameter/scale = 3.0
shader_parameter/lacunarity = 1.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_vt2tb"]
shader = ExtResource("33_3a3wy")
shader_parameter/fire_color = Color(0, 1, 1, 1)
shader_parameter/fire_power_factor = 0.5
shader_parameter/fire_intensity = 1.5
shader_parameter/fire_start = 2.5
shader_parameter/ball_size = 2.0
shader_parameter/roughness = 0.675
shader_parameter/detail = 6
shader_parameter/scale = 4.0
shader_parameter/lacunarity = 1.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gibhg"]
shader = ExtResource("32_5t3w8")
shader_parameter/fire_color = Color(0, 1, 1, 1)
shader_parameter/fire_power_factor = 0.05
shader_parameter/fire_intensity = 1.0
shader_parameter/fire_start = 5.0
shader_parameter/ball_size = 2.5
shader_parameter/roughness = 0.58
shader_parameter/detail = 6
shader_parameter/scale = 6.0
shader_parameter/lacunarity = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_8xfhs"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_3oi1i"]
shader = ExtResource("35_dcgvf")
shader_parameter/noise_tex = SubResource("NoiseTexture2D_8xfhs")
shader_parameter/root_color = Color(1, 1, 0, 0.580392)
shader_parameter/tip_color = Color(1, 0.0313726, 0, 0.709804)
shader_parameter/poster_color = 6.0
shader_parameter/fire_alpha = 1.0
shader_parameter/fire_speed = Vector2(0.015, -0.2)
shader_parameter/fire_aperture = 0.0
shader_parameter/vignette_radius = 0.3
shader_parameter/vignette_falloff = 0.4
shader_parameter/noise_influence = 1.0
shader_parameter/corner_radius = 0.05
shader_parameter/edge_smoothness = 0.0

[sub_resource type="Gradient" id="Gradient_magic"]
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)
colors = PackedColorArray(0.2, 0.6, 1, 0, 0.2, 0.6, 1, 0.8, 0.3, 0.7, 1, 0.8, 0.2, 0.6, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_magic"]
gradient = SubResource("Gradient_magic")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_magic"]
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 60.0
spread = 180.0
initial_velocity_min = 40.0
initial_velocity_max = 80.0
angular_velocity_min = -100.0
angular_velocity_max = 100.0
gravity = Vector3(0, 0, 0)
scale_min = 0.1
scale_max = 0.2
color = Color(0.2, 0.6, 1, 0.8)
color_ramp = SubResource("GradientTexture1D_magic")
hue_variation_min = -1.0
hue_variation_max = -0.8
turbulence_noise_speed_random = 0.32
turbulence_influence_min = 0.564
turbulence_influence_max = 0.834
turbulence_initial_displacement_min = -68.3
turbulence_initial_displacement_max = 56.0

[sub_resource type="Curve" id="Curve_am34o"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 3.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="ShaderMaterial" id="ShaderMaterial_xmc0b"]
shader = ExtResource("29_djko6")
shader_parameter/pulse_speed = 1.5
shader_parameter/pulse_intensity = 0.3
shader_parameter/glow_color = Color(1, 0.8, 0.2, 1)
shader_parameter/glow_intensity = 0.0
shader_parameter/time_offset = 0.0

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_0v1kq"]
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 50.0
spread = 180.0
initial_velocity_min = 50.0
initial_velocity_max = 150.0
gravity = Vector3(0, 0, 0)
scale_max = 3.0
color = Color(1, 0.8, 0.2, 1)
hue_variation_min = -0.1
hue_variation_max = 0.1

[node name="GameScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_i5t63")

[node name="BackGround" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TextureRect" type="TextureRect" parent="BackGround"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_3oi1i")
expand_mode = 1
stretch_mode = 5

[node name="Brand" type="Control" parent="BackGround"]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="StandBrand" type="TextureRect" parent="BackGround/Brand"]
layout_mode = 0
offset_left = 915.0
offset_top = 105.0
offset_right = 979.0
offset_bottom = 208.0
texture = ExtResource("3_w3ro0")
expand_mode = 1
stretch_mode = 5

[node name="DiceNumLable" type="RichTextLabel" parent="BackGround/Brand"]
unique_name_in_owner = true
layout_mode = 0
offset_left = 932.0
offset_top = 127.0
offset_right = 972.0
offset_bottom = 151.0
theme_override_colors/default_color = Color(1, 0, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/italics_font_size = 13
bbcode_enabled = true
text = "[i]0[/i]"
scroll_active = false
horizontal_alignment = 1
vertical_alignment = 1

[node name="ColorRect" type="ColorRect" parent="BackGround"]
material = SubResource("ShaderMaterial_hfa1t")
layout_mode = 0
offset_left = 43.0
offset_top = -47.0
offset_right = 193.0
offset_bottom = 153.0
rotation = 1.5708
pivot_offset = Vector2(75, 100)
color = Color(1, 0, 1, 1)

[node name="MaskTextrue" type="TextureRect" parent="BackGround"]
layout_mode = 0
offset_right = 632.0
offset_bottom = 576.0
scale = Vector2(0.345, 0.345)
texture = ExtResource("5_3oi1i")
expand_mode = 1
stretch_mode = 5

[node name="TopBar" type="Control" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchor_top = 0.123
anchor_right = 1.0
anchor_bottom = 0.201
offset_left = 232.0
offset_top = -56.704
offset_right = -240.0
offset_bottom = 5.752
grow_horizontal = 2

[node name="Progress" type="Control" parent="TopBar"]
anchors_preset = 0
offset_left = -192.0
offset_top = 113.0
offset_right = -152.0
offset_bottom = 153.0

[node name="ProgressBarShadow" type="ProgressBar" parent="TopBar/Progress"]
visible = false
modulate = Color(0, 0, 0, 1)
layout_mode = 0
offset_left = 134.0
offset_top = -43.0
offset_right = 966.0
offset_bottom = -19.0
min_value = 1.0
value = 1.0
show_percentage = false

[node name="ProgressBar" type="ProgressBar" parent="TopBar/Progress"]
unique_name_in_owner = true
material = SubResource("ShaderMaterial_wpqdo")
layout_mode = 0
offset_left = 201.0
offset_top = -48.0
offset_right = 881.0
offset_bottom = -24.0
min_value = 1.0
value = 1.0
show_percentage = false

[node name="RoundInfo" type="VBoxContainer" parent="TopBar"]
layout_mode = 1
offset_left = 56.0
offset_top = 5.0
offset_right = 173.0
offset_bottom = 49.0
size_flags_horizontal = 3
theme_override_constants/separation = 0

[node name="Level" type="HBoxContainer" parent="TopBar/RoundInfo"]
layout_mode = 2
alignment = 1

[node name="level" type="TextureRect" parent="TopBar/RoundInfo/Level"]
visible = false
custom_minimum_size = Vector2(25, 25)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("22_x7d8u")
expand_mode = 3
stretch_mode = 5

[node name="LevelTitle" type="Label" parent="TopBar/RoundInfo/Level"]
custom_minimum_size = Vector2(40, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0.462934, 0.285886, 0.214292, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_constants/shadow_outline_size = 0
theme_override_font_sizes/font_size = 10
text = "LevelTitle"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2
uppercase = true

[node name="LevelLabel" type="Label" parent="TopBar/RoundInfo/Level"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 14
text = "1"
horizontal_alignment = 2
vertical_alignment = 1
autowrap_mode = 2

[node name="Round" type="HBoxContainer" parent="TopBar/RoundInfo"]
layout_mode = 2
alignment = 1

[node name="round" type="TextureRect" parent="TopBar/RoundInfo/Round"]
visible = false
custom_minimum_size = Vector2(25, 25)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("23_vn7j2")
expand_mode = 3
stretch_mode = 5

[node name="RoundTitle" type="Label" parent="TopBar/RoundInfo/Round"]
custom_minimum_size = Vector2(40, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0.462745, 0.286275, 0.215686, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_constants/shadow_outline_size = 0
theme_override_font_sizes/font_size = 10
text = "RoundTitle"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2
uppercase = true

[node name="RoundLabel" type="Label" parent="TopBar/RoundInfo/Round"]
custom_minimum_size = Vector2(10, 0)
layout_mode = 2
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 14
text = "1"
horizontal_alignment = 2
vertical_alignment = 1
autowrap_mode = 2

[node name="ActionPointsDisplay" parent="TopBar" instance=ExtResource("24_5jn0b")]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 0)
layout_mode = 1
anchors_preset = -1
anchor_left = 0.0
anchor_right = 0.0
offset_left = 224.0
offset_top = 11.0
offset_right = 385.0
offset_bottom = 41.0
grow_horizontal = 1
size_flags_horizontal = 3
theme_override_constants/separation = 2

[node name="ScoreInfo" type="HBoxContainer" parent="TopBar"]
layout_mode = 2
offset_left = 384.0
offset_top = 11.0
offset_right = 496.0
offset_bottom = 41.0
size_flags_horizontal = 3

[node name="coins" type="TextureRect" parent="TopBar/ScoreInfo"]
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("8_w3ro0")
expand_mode = 1
stretch_mode = 4

[node name="ScoreTitle" type="Label" parent="TopBar/ScoreInfo"]
visible = false
layout_mode = 2
theme_override_colors/font_color = Color(0.462745, 0.286275, 0.215686, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_constants/shadow_outline_size = 0
theme_override_font_sizes/font_size = 15
text = "ScoreTitle"
horizontal_alignment = 1
vertical_alignment = 1
uppercase = true

[node name="ScoreLabel" type="Label" parent="TopBar/ScoreInfo"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 20
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ReductionHint" type="Label" parent="TopBar/ScoreInfo"]
unique_name_in_owner = true
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.2, 0.2, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_font_sizes/font_size = 10
text = "(-1)"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LimitInfo" type="HBoxContainer" parent="TopBar"]
layout_mode = 1
anchors_preset = -1
offset_left = 536.0
offset_top = 11.0
offset_right = 644.0
offset_bottom = 41.0
size_flags_horizontal = 3

[node name="cash" type="TextureRect" parent="TopBar/LimitInfo"]
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("9_8xfhs")
expand_mode = 3
stretch_mode = 4

[node name="LimitTitle" type="Label" parent="TopBar/LimitInfo"]
visible = false
layout_mode = 2
theme_override_colors/font_color = Color(0.462745, 0.286275, 0.215686, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 0
theme_override_constants/shadow_outline_size = 0
theme_override_font_sizes/font_size = 15
text = "LimitTitle"
horizontal_alignment = 1
vertical_alignment = 1
uppercase = true

[node name="LimitLabel" type="Label" parent="TopBar/LimitInfo"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
theme_override_font_sizes/font_size = 15
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LeftButtons" type="Control" parent="TopBar"]
layout_mode = 1
anchor_left = 0.014
anchor_top = -0.16
anchor_right = 0.083
anchor_bottom = 1.28
offset_left = 830.48
offset_top = 19.08
offset_right = 843.56
offset_bottom = -83.64
size_flags_horizontal = 3
mouse_force_pass_scroll_events = false

[node name="GearButton" type="TextureButton" parent="TopBar/LeftButtons"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -2.0
offset_bottom = -2.0
grow_horizontal = 2
grow_vertical = 2
texture_normal = ExtResource("9_nyhfq")
ignore_texture_size = true
stretch_mode = 5

[node name="RuleButton" type="Button" parent="TopBar/LeftButtons"]
visible = false
layout_mode = 2
offset_left = 90.0
offset_right = 162.0
offset_bottom = 80.0
text = "牌型规则"

[node name="SettingsButton" type="Button" parent="TopBar/LeftButtons"]
visible = false
layout_mode = 2
offset_left = 172.0
offset_right = 212.0
offset_bottom = 80.0
text = "设置"

[node name="ExitButton" type="Button" parent="TopBar/LeftButtons"]
visible = false
layout_mode = 2
offset_left = 222.0
offset_right = 262.0
offset_bottom = 80.0
text = "退出"

[node name="PlayerControl" type="Control" parent="."]
anchors_preset = 0
offset_left = 290.0
offset_top = 186.0
offset_right = 876.0
offset_bottom = 484.0

[node name="TextureRect" type="TextureRect" parent="PlayerControl"]
visible = false
layout_mode = 0
offset_left = -26.0
offset_top = 94.0
offset_right = 596.0
offset_bottom = 200.0
texture = ExtResource("10_unjts")
expand_mode = 1
stretch_mode = 5

[node name="PoolArea" type="VBoxContainer" parent="PlayerControl"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.167
anchor_top = 0.25
anchor_right = 0.167
anchor_bottom = 0.25
offset_left = 12.138
offset_top = -68.5
offset_right = 398.138
offset_bottom = 58.5
grow_horizontal = 2
alignment = 1

[node name="PoolLabel" type="Label" parent="PlayerControl/PoolArea"]
visible = false
layout_mode = 2
text = " "
horizontal_alignment = 1

[node name="PoolContainer" type="Control" parent="PlayerControl/PoolArea"]
layout_mode = 2
size_flags_vertical = 3

[node name="PlayerArea" type="VBoxContainer" parent="PlayerControl"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = -1
anchor_left = 0.167
anchor_top = 0.605
anchor_right = 0.722
anchor_bottom = 0.889
offset_left = -40.862
offset_top = -5.29001
offset_right = 100.908
offset_bottom = 29.078
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="ScoreDisplay" type="Label" parent="PlayerControl/PlayerArea"]
visible = false
custom_minimum_size = Vector2(0, 100)
layout_mode = 2
size_flags_horizontal = 4
horizontal_alignment = 1
vertical_alignment = 1

[node name="HandContainer" type="Control" parent="PlayerControl/PlayerArea"]
layout_mode = 2
size_flags_vertical = 3

[node name="ForeverBuffArea" parent="." instance=ExtResource("30_sg5rm")]
offset_left = 326.0
offset_top = 578.0
offset_right = 810.0
offset_bottom = 628.0

[node name="RightSide" type="Control" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 11
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -287.0
offset_top = 190.0
offset_right = -68.0
offset_bottom = -94.0
grow_horizontal = 0
grow_vertical = 2
scale = Vector2(0.8, 0.8)

[node name="DeckCountLabel" type="Label" parent="RightSide"]
visible = false
layout_mode = 2
offset_left = 48.75
offset_top = -40.0
offset_right = 126.75
offset_bottom = -5.0
theme_override_font_sizes/font_size = 25
text = "牌数: 0"
horizontal_alignment = 1

[node name="ChangeChip" type="Control" parent="RightSide"]
layout_mode = 2
anchors_preset = 0
offset_left = 13.75
offset_top = -28.75
offset_right = 73.75
offset_bottom = 31.25
pivot_offset = Vector2(0, 120)

[node name="Shadow" type="TextureRect" parent="RightSide/ChangeChip"]
modulate = Color(0, 0, 0, 0.305882)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -3.75
offset_top = 15.0
offset_right = -3.75
offset_bottom = 15.0
grow_horizontal = 2
grow_vertical = 2
rotation = 0.261799
scale = Vector2(1.05, 1.05)
pivot_offset = Vector2(0, 120)
texture = ExtResource("31_auu3w")
expand_mode = 1
stretch_mode = 5

[node name="Shader" type="ColorRect" parent="RightSide/ChangeChip"]
visible = false
material = SubResource("ShaderMaterial_awo82")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 45.0
offset_top = -8.75
offset_right = 25.0
offset_bottom = -28.75
grow_horizontal = 2
grow_vertical = 2
rotation = 1.5708

[node name="Shader2" type="ColorRect" parent="RightSide/ChangeChip"]
visible = false
material = SubResource("ShaderMaterial_vt2tb")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 60.0
offset_top = -25.0
offset_right = 40.0
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 2
rotation = 1.5708

[node name="TextureRect" type="TextureRect" parent="RightSide/ChangeChip"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("31_auu3w")
expand_mode = 1
stretch_mode = 5

[node name="DeckDiscardArea" type="Control" parent="RightSide"]
layout_mode = 2
anchors_preset = 0
offset_left = 38.75
offset_top = 75.0
offset_right = 158.75
offset_bottom = 195.0
pivot_offset = Vector2(60, 60)
size_flags_vertical = 3

[node name="DeckDiscardShadow" type="TextureRect" parent="RightSide/DeckDiscardArea"]
modulate = Color(0, 0, 0, 0.427451)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 6.25
offset_top = 20.0
offset_right = 6.25
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(60, 60)
texture = ExtResource("14_am34o")
expand_mode = 1
stretch_mode = 5

[node name="ShaderCard" type="ColorRect" parent="RightSide/DeckDiscardArea"]
visible = false
material = SubResource("ShaderMaterial_gibhg")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 137.5
offset_top = -35.0
offset_right = 167.5
offset_bottom = -35.0
grow_horizontal = 2
grow_vertical = 2
rotation = 1.5708
mouse_filter = 2

[node name="DeckDiscardImage" type="TextureRect" parent="RightSide/DeckDiscardArea"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(60, 60)
texture = ExtResource("14_am34o")
expand_mode = 1
stretch_mode = 5

[node name="Shader" type="TextureRect" parent="RightSide/DeckDiscardArea"]
visible = false
material = SubResource("ShaderMaterial_3oi1i")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 42.0
offset_top = 30.0
offset_right = -31.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2
rotation = -0.20944
pivot_offset = Vector2(105, 125)
texture = ExtResource("36_glycf")
expand_mode = 1

[node name="MagicParticles" type="GPUParticles2D" parent="RightSide/DeckDiscardArea"]
unique_name_in_owner = true
z_index = 1
position = Vector2(60, 47.5)
emitting = false
amount = 30
texture = ExtResource("21_gibhg")
preprocess = 1.0
randomness = 0.5
process_material = SubResource("ParticleProcessMaterial_magic")

[node name="DiscardArea" type="Control" parent="RightSide"]
layout_mode = 2
anchors_preset = 0
offset_left = 85.0
offset_top = 247.5
offset_right = 185.0
offset_bottom = 347.5
pivot_offset = Vector2(50, 50)
size_flags_vertical = 3

[node name="DiscardShadow" type="TextureRect" parent="RightSide/DiscardArea"]
modulate = Color(0, 0, 0, 0.4)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 18.75
offset_right = 5.0
offset_bottom = 18.75
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(50, 50)
size_flags_vertical = 3
texture = ExtResource("17_am34o")
expand_mode = 1
stretch_mode = 5

[node name="DiscardImage" type="TextureRect" parent="RightSide/DiscardArea"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(50, 50)
size_flags_vertical = 3
texture = ExtResource("17_am34o")
expand_mode = 1
stretch_mode = 5

[node name="Line2D" type="Line2D" parent="RightSide/DiscardArea"]
visible = false
points = PackedVector2Array(-190, 30, -340, 20, -430, -50)
width = 50.0
width_curve = SubResource("Curve_am34o")
gradient = ExtResource("18_am34o")
texture = ExtResource("18_makri")
texture_mode = 1
joint_mode = 2

[node name="TypeShow" type="Control" parent="RightSide"]
anchors_preset = 0
offset_left = 88.75
offset_top = 28.75
offset_right = 118.75
offset_bottom = 58.75

[node name="Add" type="Sprite2D" parent="RightSide/TypeShow"]
unique_name_in_owner = true
position = Vector2(13.75, 15)
scale = Vector2(0.08, 0.08)
skew = -0.20944
texture = ExtResource("20_af2fc")

[node name="Change" type="Sprite2D" parent="RightSide/TypeShow"]
unique_name_in_owner = true
visible = false
position = Vector2(13.75, 15)
scale = Vector2(0.05, 0.05)
skew = -0.329867
texture = ExtResource("21_2rdal")

[node name="BuffDisplay" type="Control" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 226.0
offset_top = -164.0
offset_right = 296.0
offset_bottom = 66.0
grow_vertical = 2
pivot_offset = Vector2(76, 0)
mouse_filter = 2
script = ExtResource("45_ks1cw")

[node name="BottomRight" type="Control" parent="."]
layout_mode = 2
anchors_preset = 0
offset_left = 112.0
offset_top = 368.0
offset_right = 312.0
offset_bottom = 568.0
scale = Vector2(0.7, 0.7)
size_flags_vertical = 3
size_flags_stretch_ratio = 0.5

[node name="NextButton" type="Control" parent="BottomRight"]
layout_mode = 2
anchors_preset = 0
offset_left = 1.4286
offset_top = 45.7142
offset_right = 201.429
offset_bottom = 155.714
size_flags_vertical = 3

[node name="Buttonshadow" type="TextureRect" parent="BottomRight/NextButton"]
modulate = Color(0, 0, 0, 0.729412)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -1.42856
offset_top = 7.14264
offset_right = -1.42856
offset_bottom = 7.14265
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(100, 55)
texture = ExtResource("25_w3ro0")
expand_mode = 1
stretch_mode = 5

[node name="EndRoundButton" type="TextureButton" parent="BottomRight/NextButton"]
unique_name_in_owner = true
material = SubResource("ShaderMaterial_xmc0b")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 1.52588e-05
offset_right = 1.52588e-05
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(100, 55)
size_flags_vertical = 3
texture_normal = ExtResource("25_w3ro0")
ignore_texture_size = true
stretch_mode = 5

[node name="FireworkParticles" type="GPUParticles2D" parent="BottomRight/NextButton/EndRoundButton"]
unique_name_in_owner = true
position = Vector2(98.7143, 49.7142)
emitting = false
amount = 30
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_0v1kq")

[node name="NextLabel" type="Label" parent="BottomRight/NextButton/EndRoundButton"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("21_y73b8")
theme_override_font_sizes/font_size = 30
text = "NextLabel"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Scroll" type="Control" parent="BottomRight"]
layout_mode = 2
anchors_preset = 0
offset_left = 278.571
offset_top = -287.143
offset_right = 378.571
offset_bottom = -187.143

[node name="Shadow" type="TextureRect" parent="BottomRight/Scroll"]
modulate = Color(0, 0, 0, 0.290196)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 11.0
offset_top = 10.0
offset_right = 11.0
offset_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("19_gcft7")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="BottomRight/Scroll"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("19_gcft7")
expand_mode = 1
stretch_mode = 5

[node name="ScrollButton" type="Button" parent="BottomRight/Scroll"]
unique_name_in_owner = true
modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="DicePanel" parent="." instance=ExtResource("38_h25xd")]
visible = false
layout_mode = 1

[node name="SettingsPanel" parent="." instance=ExtResource("39_lab21")]
visible = false
layout_mode = 1

[node name="GameOverPanel" parent="." instance=ExtResource("40_4de3h")]
visible = false
layout_mode = 1
pivot_offset = Vector2(200, 300)

[node name="VictoryPanel" parent="." instance=ExtResource("41_sk0wp")]
visible = false
z_index = 100
layout_mode = 1

[node name="TooltipBubble" parent="." instance=ExtResource("42_xcj5d")]
layout_mode = 1
offset_left = -250.0
offset_right = 250.0

[node name="RulePanel" parent="." instance=ExtResource("43_rwg48")]
visible = false
custom_minimum_size = Vector2(700, 400)
layout_mode = 1
offset_left = 0.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0

[node name="SettingsMenuPanel" parent="." instance=ExtResource("44_s044x")]
visible = false
layout_mode = 1

[node name="UpLevelScene" parent="." instance=ExtResource("46_n5epk")]
visible = false

[node name="DebugPanel" parent="." instance=ExtResource("47_bk8w8")]
visible = false
z_index = 500
layout_mode = 1

[node name="CardRulesPanel" parent="." instance=ExtResource("48_kdmsa")]
visible = false
z_index = 80
layout_mode = 1

[node name="tutorialOverlay" parent="." instance=ExtResource("49_s049x")]
visible = false

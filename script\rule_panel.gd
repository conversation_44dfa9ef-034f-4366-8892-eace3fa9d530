extends Panel

# 规则面板 - 显示游戏规则信息

# 节点引用
@onready var rule_content = $VBoxContainer/ScrollContainer/RuleContent
@onready var title_label = $VBoxContainer/TitleLabel

# 规则数据
var rules_data: Dictionary = {}
var rules_values: Dictionary = {}

# 配置文件路径
const RULES_PATH = "res://data/game_rules.json"

# 颜色常量
const COLOR_HEADER = "#FFD700" # 金色
const COLOR_NAME = "#7FFF00"   # 黄绿色
const COLOR_SCORE = "#00FFFF"  # 青色
const COLOR_BONUS_POSITIVE = "#FF69B4" # 粉红色
const COLOR_BONUS_ZERO = "#808080"     # 灰色
const COLOR_DESC = "#E6E6FA"   # 淡紫色

# 初始化
func _ready():
    # 初始隐藏面板
    visible = false
    
    # 设置输入处理
    set_process_input(true)
    
    # 设置中心点，用于缩放动画
    pivot_offset = size / 2
    
    # 设置标题样式
    _setup_title_style()
    
    # 设置面板样式
    _setup_panel_style()
    
    # 确保用户规则值配置文件存在
    _ensure_user_rules_values_exists()
    
    # 加载规则数据
    _load_rules_data()

    # 连接语言变化信号
    #TranslationServer.connect("locale_changed", _on_locale_changed)
    
    # 连接游戏规则重置信号
    GameManager.reset_game_rules.connect(_on_reset_game_rules)

# 从 BuffManager 获取规则值数据（不再需要文件操作）
func _ensure_user_rules_values_exists():
    # 这个函数现在不需要做任何事情，因为规则值直接从 BuffManager 获取
    pass

# 加载规则数据
func _load_rules_data():
    # 加载国际化规则数据
    var file = FileAccess.open(RULES_PATH, FileAccess.READ)
    if file:
        var json = JSON.new()
        var parse_result = json.parse(file.get_as_text())
        if parse_result == OK:
            rules_data = json.get_data()
        file.close()
    
    # 从 BuffManager 获取规则值数据
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager and buff_manager.game_rules_values:
        # 将 BuffManager 的格式转换为面板需要的格式
        rules_values = {"rules": []}
        for rule_id in buff_manager.game_rules_values.keys():
            var rule_data = buff_manager.game_rules_values[rule_id]
            rules_values.rules.append({
                "id": rule_id,
                "base_score": rule_data.base_score,
                "bonus_score": rule_data.bonus_score
            })
        print("从 BuffManager 加载了 %d 个规则值" % rules_values.rules.size())
    else:
        print("无法从 BuffManager 获取规则值数据")
        rules_values = {"rules": []}
    
    _update_rules_display()

# 获取当前语言的规则数据
func _get_current_language_rules() -> Dictionary:
    var current_locale = TranslationServer.get_locale()
    
    if rules_data.has(current_locale):
        return rules_data[current_locale]
    elif rules_data.has("en"):  # 如果找不到当前语言，回退到英语
        return rules_data["en"]
    else:
        push_error("No valid language data found in rules")
        return {}

# 更新规则显示
func _update_rules_display():
    var current_rules = _get_current_language_rules()
    if current_rules.is_empty():
        return
        
    var bbcode = ""
    if current_rules.has("game_info"):
        bbcode += "[center][i]" + current_rules.game_info + "[/i][/center]"
    
    # 添加附加信息
    if current_rules.has("additional_info"):
        bbcode += "\n\n[center]" + current_rules.additional_info + "[/center]"
    
    if current_rules.has("rules_title"):
        bbcode += "\n\n[center][color=%s][b]" % COLOR_HEADER + current_rules.rules_title + "[/b][/color][/center]" 
    
    if current_rules.has("rules") and rules_values.has("rules"):
        bbcode += "\n[table=4]\n"
        
        # 添加表头 - 从当前语言的规则数据中获取
        if current_rules.has("headers"):
            var headers = current_rules.headers
            bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.type]
            bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.score]
            bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]" % [COLOR_HEADER, headers.bonus]
            bbcode += "[cell border=2][center][color=%s][b]%s[/b][/color][/center][/cell]\n" % [COLOR_HEADER, headers.desc]
        
        # 添加规则数据，使用用户规则值数据中的分数
        for i in range(current_rules.rules.size()):
            var rule = current_rules.rules[i]
            var value_rule = rules_values.rules[i]
            var total_score = value_rule.base_score + value_rule.bonus_score
            
            # 牌型名称列
            bbcode += "[cell border=2][center][color=%s]%s[/color][/center][/cell]" % [COLOR_NAME, rule.name]
            
            # 分数列
            bbcode += "[cell border=2][center][color=%s][b]%d[/b][/color][/center][/cell]" % [COLOR_SCORE, total_score]
            
            # 加成列
            var bonus_str = "+" + str(int(value_rule.bonus_score))
            var bonus_color = COLOR_BONUS_POSITIVE
            if value_rule.bonus_score == 0:
                bonus_str = str(0)
                bonus_color = COLOR_BONUS_ZERO
            bbcode += "[cell border=2][center][color=%s]%s[/color][/center][/cell]" % [bonus_color, bonus_str]
            
            # 说明列
            bbcode += "[cell border=2][color=%s]%s[/color][/cell]\n" % [COLOR_DESC, rule.description]
        
        bbcode += "[/table]\n\n"
    
    if current_rules.has("game_tips"):
        bbcode += "[center][i]" + current_rules.game_tips + "[/i][/center]"
    
    rule_content.text = bbcode

# 重置用户规则值
func reset_rules_values():
    # 通过 BuffManager 重置规则值
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager:
        buff_manager.reset_game_rules_bonus()
        print("已重置用户规则值配置")

        # 重新加载规则数据
        _load_rules_data()
    else:
        print("无法找到 BuffManager，重置失败")

# 当语言改变时更新规则显示
func _on_locale_changed():
    _update_rules_display()

# 设置面板样式
func _setup_panel_style():
    # 创建面板样式
    var panel_style = StyleBoxFlat.new()
    panel_style.bg_color = Color(0.1, 0.1, 0.2, 0.95)
    panel_style.corner_radius_top_left = 25
    panel_style.corner_radius_top_right = 25
    panel_style.corner_radius_bottom_left = 25
    panel_style.corner_radius_bottom_right = 25
    panel_style.border_width_left = 4
    panel_style.border_width_top = 4
    panel_style.border_width_right = 4
    panel_style.border_width_bottom = 4
    panel_style.border_color = Color(0.3, 0.3, 0.7, 1.0)
    panel_style.shadow_color = Color(0, 0, 0, 0.5)
    panel_style.shadow_size = 15
    panel_style.shadow_offset = Vector2(2, 2)
    
    # 应用样式
    add_theme_stylebox_override("panel", panel_style)
    
    # 设置背景为透明，因为面板已经有自己的样式
    $Background.color = Color(0.1, 0.1, 0.3, 0.0)

# 设置标题样式
func _setup_title_style():
    if title_label:
        title_label.add_theme_font_size_override("font_size", 36)
        title_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2, 1.0))  # 金色
        title_label.add_theme_constant_override("outline_size", 6)
        title_label.add_theme_color_override("font_outline_color", Color(0.7, 0.0, 0.0, 1.0))
        title_label.add_theme_constant_override("shadow_size", 4)
        title_label.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.5))

# 显示规则面板
func show_panel():
    # 确保样式正确
    _setup_panel_style()
    
    # 设置初始状态
    modulate.a = 0.0
    scale = Vector2(0.8, 0.8)
    mouse_filter = Control.MOUSE_FILTER_STOP  # 确保可以接收输入
    
    # 显示面板
    visible = true
    
    # 播放显示动画
    _play_show_animation()

    _load_rules_data()

# 隐藏规则面板
func hide_panel():
    # 播放隐藏动画
    _play_hide_animation()

# 播放显示动画
func _play_show_animation():
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 播放标题动画
    _play_title_animation()

# 播放隐藏动画
func _play_hide_animation():
    # 立即将处理输入设为false，防止多次点击
    self.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_BACK)
    tween.tween_property(self, "modulate:a", 0.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(0.8, 0.8), 0.3)
    
    # 等待动画完成后隐藏面板
    await tween.finished
    visible = false
    
    # 恢复输入处理
    self.mouse_filter = Control.MOUSE_FILTER_STOP

# 播放标题动画
func _play_title_animation():
    if not title_label:
        return
    
    # 设置旋转中心点
    title_label.pivot_offset = title_label.size / 2
    
    # 设置初始状态
    title_label.scale = Vector2(0.1, 0.1)
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(title_label, "scale", Vector2(1.2, 1.2), 0.5)
    tween.tween_property(title_label, "scale", Vector2(1.0, 1.0), 0.3)

# 输入处理 - 点击任意位置关闭面板
func _input(event):
    # 如果面板可见且点击了鼠标左键
    if visible and event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
        # 获取点击位置
        var click_pos = event.position
        
        # 检查点击是否在面板区域外
        if not get_global_rect().has_point(click_pos):
            hide_panel()
            # 消耗事件，防止点击穿透
            get_viewport().set_input_as_handled()

# 处理游戏规则重置信号
func _on_reset_game_rules():
    reset_rules_values()

extends GPUParticles2D

@export var active = true
@export var min_interval = 1.5  # Minimum time between sparks in seconds
@export var max_interval = 3.5  # Maximum time between sparks in seconds

@onready var timer = $Timer

func _ready():
    # Connect timer signal
    timer.connect("timeout", _on_timer_timeout)
    
    # Set initial random wait time
    timer.wait_time = randf_range(min_interval, max_interval)
    timer.start()

func _on_timer_timeout():
    if active:
        # Emit the particles
        restart()
        emitting = true
        
    # Set a new random interval
    timer.wait_time = randf_range(min_interval, max_interval)
    timer.start()

# This function can be called from outside to trigger a collision effect immediately
func trigger_collision():
    if not emitting:
        restart()
        emitting = true
        
# For manually stopping/starting the periodic effect
func set_active(is_active):
    active = is_active
    if not active:
        timer.stop()
    elif not timer.is_active():
        timer.start() 

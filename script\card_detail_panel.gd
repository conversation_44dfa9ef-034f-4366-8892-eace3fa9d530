extends Control

# 卡牌详情面板 - 显示单张卡牌的详细信息

# 信号
signal detail_panel_closed()
signal close_requested

# 节点引用
@onready var card_container = $Panel/VBoxContainer/Container/CardContainer
@onready var card_display = $Panel/VBoxContainer/Container/CardContainer/CardDisplay
@onready var title_label = %BuffNameLabel
@onready var description_container = %DescriptionMargin
@onready var description_label = %DescriptionLabel
@onready var buff_list = %BuffList
@onready var return_button = $Panel/VBoxContainer/ReturnButton
@onready var background_overlay = $BackgroundDim
@onready var card_score = %CardScore
@onready var h_separator = %HSeparator
@onready var buff_title = %BuffDetailTitle

# 3D效果变量
var is_mouse_inside = false
var mouse_position = Vector2.ZERO
var max_rotation_angle = 10.0  # 最大旋转角度
var rotation_smoothing = 0.1   # 旋转平滑度
var target_rotation = Vector3.ZERO
var current_rotation = Vector3.ZERO
var card_center = Vector2.ZERO

# 光照效果变量
var light_offset = Vector2.ZERO
var max_light_offset = Vector2(30, 30)
var glow_material = null

# 当前显示的卡牌数据
var current_card_data = null
var card_data: Dictionary = {}
# 注意：如果buff_item.tscn不存在，需要先创建它，或者暂时注释掉这一行
# var buff_scene = preload("res://scene/card_detail/buff_item.tscn")

# 卡牌着色器相关变量
var unified_material = null
var buff_texture = null

# 自适应布局相关变量
var base_card_width = 350  # 卡牌基础宽度 
var base_card_height = 470  # 卡牌基础高度 
var base_panel_width = 800  # 面板基础宽度 
var base_panel_height = 400  # 面板基础高度 
var base_font_size = 24  # 基础字体大小

# 初始化
func _ready():
    # 确保节点引用正确
    title_label = %BuffNameLabel
    if !is_instance_valid(title_label):
        print("错误: 找不到标题节点")
        
    # 设置面板和背景的锚点
    $Panel.set_anchors_preset(Control.PRESET_CENTER)  # 设置锚点为中心
    if background_overlay:
        background_overlay.set_anchors_preset(Control.PRESET_FULL_RECT)  # 背景铺满整个区域
    
    # 初始隐藏面板
    visible = false
    modulate.a = 0
    
    # 设置鼠标过滤模式
    _set_mouse_filter_recursive($Panel)
    
    # 特别设置卡牌容器的鼠标过滤模式，使其能接收鼠标移动事件
    if card_container:
        card_container.mouse_filter = Control.MOUSE_FILTER_PASS
        if card_display:
            card_display.mouse_filter = Control.MOUSE_FILTER_PASS
    
    # 连接信号
    card_container.mouse_entered.connect(_on_card_container_mouse_entered)
    card_container.mouse_exited.connect(_on_card_container_mouse_exited)
    card_container.gui_input.connect(_on_card_container_input)
    return_button.pressed.connect(_on_return_button_pressed)
    
    # 创建光照效果材质
    _setup_glow_material()
    
    # 设置返回按钮样式
    _setup_return_button()
    
    # 确保面板在初始化时就处于最顶层
    _ensure_top_layer()
    
    # 应用自适应布局
    #_apply_adaptive_layout()

# 确保面板显示在最顶层
func _ensure_top_layer():
    # 获取父节点
    var parent = get_parent()
    if parent:
        # 将自身移动到父节点的最后位置，确保绘制在最上层
        parent.move_child(self, parent.get_child_count() - 1)
        
        # 直接设置z_index属性，确保在同级节点中显示在最上层
        z_index = 60
        
        # 如果父节点或者场景中有CanvasLayer，可以考虑将此控件添加为其子节点

# 设置按钮样式
func _setup_return_button():
    # 确保按钮居中显示在底部
    return_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
    return_button.size_flags_vertical = Control.SIZE_SHRINK_END
    
    # 使用圆形样式盒
    var style_normal = StyleBoxFlat.new()
    style_normal.bg_color = Color(0.2, 0.6, 0.8, 0.8)
    style_normal.corner_radius_top_left = 15
    style_normal.corner_radius_top_right = 15
    style_normal.corner_radius_bottom_left = 15
    style_normal.corner_radius_bottom_right = 15
    style_normal.content_margin_bottom = 10
    style_normal.content_margin_top = 10
    style_normal.content_margin_left = 20
    style_normal.content_margin_right = 20
    
    var style_hover = StyleBoxFlat.new()
    style_hover.bg_color = Color(0.3, 0.7, 0.9, 0.9)
    style_hover.corner_radius_top_left = 15
    style_hover.corner_radius_top_right = 15
    style_hover.corner_radius_bottom_left = 15
    style_hover.corner_radius_bottom_right = 15
    style_hover.content_margin_bottom = 10
    style_hover.content_margin_top = 10
    style_hover.content_margin_left = 20
    style_hover.content_margin_right = 20
    
    var style_pressed = StyleBoxFlat.new()
    style_pressed.bg_color = Color(0.1, 0.5, 0.7, 1.0)
    style_pressed.corner_radius_top_left = 15
    style_pressed.corner_radius_top_right = 15
    style_pressed.corner_radius_bottom_left = 15
    style_pressed.corner_radius_bottom_right = 15
    style_pressed.content_margin_bottom = 10
    style_pressed.content_margin_top = 10
    style_pressed.content_margin_left = 20
    style_pressed.content_margin_right = 20
    
    # 应用样式
    return_button.add_theme_stylebox_override("normal", style_normal)
    return_button.add_theme_stylebox_override("hover", style_hover)
    return_button.add_theme_stylebox_override("pressed", style_pressed)
    
    # 设置文本
    return_button.text = "返回"
    return_button.add_theme_font_size_override("font_size", 20)
    return_button.add_theme_color_override("font_color", Color(1, 1, 1, 1))
    return_button.add_theme_color_override("font_hover_color", Color(1, 1, 1, 1))
    return_button.add_theme_color_override("font_pressed_color", Color(1, 1, 1, 1))
    
    # 设置鼠标进入/退出动画
    return_button.mouse_entered.connect(_on_return_button_mouse_entered)
    return_button.mouse_exited.connect(_on_return_button_mouse_exited)

# 按钮鼠标进入事件
func _on_return_button_mouse_entered():
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(return_button, "scale", Vector2(1.1, 1.1), 0.3)

# 按钮鼠标退出事件
func _on_return_button_mouse_exited():
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(return_button, "scale", Vector2(1.0, 1.0), 0.3)

# 设置卡牌描述
func set_card_description(card_data):
    # 清空描述容器
    for child in description_container.get_children():
        if child != description_label:
            child.queue_free()
    
    # 清空BUFF列表
    for child in buff_list.get_children():
            child.queue_free()
    
    # 重置容器大小
    buff_list.custom_minimum_size = Vector2.ZERO
    buff_list.size = Vector2.ZERO
    
    card_score.text = str(card_data.value)
    
    # 如果卡牌有BUFF，显示BUFF描述
    if card_data and "buff" in card_data:
        description_container.visible = true
        title_label.visible = true
        h_separator.visible = true
        buff_title.visible = true
        
        var buff_data = card_data.buff
        var buff_type = buff_data.type
        var buff_value = buff_data.value
        var rounds_left = buff_data.rounds_left
        var additional_data = buff_data.additional_data
        var buff_description = buff_data.description
        
        # 设置描述标签显示效果名称，启用BBCode
        description_label.bbcode_enabled = true
        description_label.text = buff_description.replace("{value}", str(buff_value))
        
        # 获取BUFF管理器 
        var buff_manager = get_node("/root/BuffManager")
        if not buff_manager:
            description_label.text = "无法获取BUFF信息"
            return
        
        # 获取BUFF类型名称
        var type_name = buff_manager.get_buff_type_name(buff_type)
        # 获取BUFF颜色
        var buff_color = buff_manager.get_buff_color(buff_data.color)
        
        # 添加效果类型标题
        var effect_type_label = RichTextLabel.new()
        effect_type_label.bbcode_enabled = true
        effect_type_label.fit_content = true
        effect_type_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
        effect_type_label.custom_minimum_size.y = 30  # 设置最小高度
        
        # 转换buff_color为十六进制颜色代码
        var color_code = "#%02X%02X%02X" % [
            int(buff_color.r * 255), 
            int(buff_color.g * 255), 
            int(buff_color.b * 255)
        ]
        
        effect_type_label.text = "[wave amp=20 freq=5][outline_size=4][outline_color=#000000][color=%s]【%s】[/color][/outline_color][/outline_size][/wave]" % [color_code, type_name]
        effect_type_label.add_theme_font_size_override("font_size", 18)
        
        # 将效果类型标题添加到BuffsContainer
        # buff_list.add_child(effect_type_label)
        
        # 添加效果详细说明
        var effect_explanation = RichTextLabel.new()
        effect_explanation.bbcode_enabled = true
        effect_explanation.fit_content = true
        effect_explanation.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
        effect_explanation.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
        effect_explanation.custom_minimum_size.y = 60  # 设置最小高度
        effect_explanation.text = buff_manager.get_buff_explanation(buff_type, buff_value, buff_manager, rounds_left, additional_data)
        effect_explanation.add_theme_font_size_override("font_size", 16)
        effect_explanation.add_theme_color_override("default_color", Color(0.314, 0.314, 0.314))
        
        # 将详细说明添加到BuffsContainer
        buff_list.add_child(effect_explanation)
        
        var rounds_label = RichTextLabel.new()
        rounds_label.bbcode_enabled = true
        rounds_label.fit_content = true
        rounds_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
        rounds_label.custom_minimum_size.y = 30  # 设置最小高度
        rounds_label.add_theme_font_size_override("font_size", 16)
        rounds_label.add_theme_color_override("default_color", Color(0.314, 0.314, 0.314))
        # 添加剩余阶数信息（如果不是一次性BUFF）
        if not buff_manager._is_one_time_buff(buff_type) and "rounds_left" in buff_data:
            rounds_label.text = "%s: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size]" % [tr("STAGE_LEFT"), BuffManager._get_rounds_color_code(buff_data.rounds_left), buff_data.rounds_left]
            
            # 添加到BuffsContainer
            buff_list.add_child(rounds_label)
        else:
            rounds_label.text = "[outline_size=4][outline_color=#000000][color=orange]%s[/color][/outline_color][/outline_size]" % tr("ONE_TIME_BUFF")
            # 添加到BuffsContainer
            buff_list.add_child(rounds_label)
    else:
        # 如果没有BUFF，隐藏描述区域
        description_container.visible = false
        title_label.visible = false
        h_separator.visible = false
        buff_title.visible = false
        
    # 强制更新布局
    buff_list.queue_sort()
    buff_list.reset_size()


# 新增辅助方法 - 根据花色枚举值获取花色名称和颜色
func _get_suit_info(suit_enum_value):
    var suit_info = {
        "name": "未知花色",
        "color": "#FFFFFF" # 默认白色
    }
    
    # 获取卡牌系统
    var card_system = get_node("/root/CardSystem")
    if card_system:
        for key in card_system.CardSuit.keys():
            if card_system.CardSuit[key] == suit_enum_value:
                match key:
                    "SPADE":
                        suit_info.name = "黑桃♠"
                        suit_info.color = "#000000" # 黑色
                    "HEART":
                        suit_info.name = "红桃♥"
                        suit_info.color = "#FF3333" # 红色
                    "CLUB":
                        suit_info.name = "梅花♣"
                        suit_info.color = "#00CC00" # 绿色
                    "DIAMOND":
                        suit_info.name = "方块♦"
                        suit_info.color = "#3333FF" # 蓝色
                    _:
                        suit_info.name = key
                break
    
    return suit_info

# 根据数值大小获取对应的颜色代码
func _get_value_color_code(value, is_negative = false):
    # 颜色基础值
    var r = 1.0
    var g = 1.0
    var b = 0.0
    
    if is_negative:
        # 负面效果：红色系列
        r = 1.0
        g = max(0.3, 1.0 - (value / 10.0))
        b = max(0.0, 0.5 - (value / 20.0))
    else:
        # 正面效果：绿色到金色渐变
        r = min(1.0, 0.5 + (value / 10.0))
        g = min(1.0, 0.8 + (value / 20.0))
        b = max(0.0, 0.5 - (value / 10.0))
    
    # 将RGB值转换为十六进制颜色代码
    return "#%02X%02X%02X" % [int(r * 255), int(g * 255), int(b * 255)]

# 设置光照效果材质
func _setup_glow_material():
    # 创建统一卡牌效果着色器材质
    unified_material = ShaderMaterial.new()
    unified_material.shader = load("res://shader/card_unified_effect.gdshader")
    
    # 设置初始参数
    # 开启浮动效果
    unified_material.set_shader_parameter("enable_float_effect", true)
    unified_material.set_shader_parameter("float_amplitude", 2.0)  # 适当的浮动幅度
    unified_material.set_shader_parameter("float_frequency", 0.8)  # 浮动频率
    
    # 开启BUFF叠加效果
    unified_material.set_shader_parameter("enable_buff_overlay", true)
    unified_material.set_shader_parameter("buff_color", Color(1.0, 1.0, 1.0, 0.3)) # 默认颜色
    unified_material.set_shader_parameter("buff_tile_scale", 1.0)
    unified_material.set_shader_parameter("buff_noise_influence", 0.1)
    
    # 开启3D效果
    unified_material.set_shader_parameter("enable_3d_effect", true)
    unified_material.set_shader_parameter("card_width", base_card_width)
    unified_material.set_shader_parameter("card_height", base_card_height)
    unified_material.set_shader_parameter("mouse_position", Vector2(0, 0))
    
    # buff纹理会在显示卡牌时设置
    
    # 应用材质到卡牌显示节点
    if card_display:
        card_display.material = unified_material

# 计算自适应布局参数
func _calculate_adaptive_layout():
    # 获取当前视口大小
    var viewport_size = get_viewport_rect().size
    
    # 计算基于视口大小的缩放因子
    var base_width = 1152.0  # 基准宽度
    var base_height = 648.0  # 基准高度
    
    var scale_factor_width = viewport_size.x / base_width
    var scale_factor_height = viewport_size.y / base_height
    
    # 使用较小的缩放因子，确保内容在任何方向都不会超出屏幕
    var scale_factor = min(scale_factor_width, scale_factor_height)
    
    # 计算缩放后的尺寸
    var card_width = base_card_width * scale_factor
    var card_height = base_card_height * scale_factor
    var panel_width = base_panel_width * scale_factor
    var panel_height = base_panel_height * scale_factor
    var font_size = int(base_font_size * scale_factor)
    
    # 返回计算结果
    return {
        "scale_factor": scale_factor,
        "card_width": card_width,
        "card_height": card_height,
        "panel_width": panel_width,
        "panel_height": panel_height,
        "font_size": font_size
    }

# 应用自适应布局
func _apply_adaptive_layout():
    var layout = _calculate_adaptive_layout()
    
    # 调整面板大小
    $Panel.custom_minimum_size = Vector2(layout.panel_width, layout.panel_height)
    $Panel.size = Vector2(layout.panel_width, layout.panel_height)
    
    # 确保面板居中显示
    # 获取视口大小
    var viewport_size = get_viewport_rect().size
    
    # 设置面板位置为视口中心
    $Panel.position = (viewport_size - $Panel.size) / 2
    
    # 调整背景遮罩大小以适应整个视口
    if background_overlay:
        background_overlay.size = viewport_size
        background_overlay.position = Vector2.ZERO
    
    # 调整卡牌容器大小
    if card_container:
        card_container.custom_minimum_size = Vector2(layout.card_width, layout.card_height)
    
    # 调整卡牌显示大小
    if card_display:
        card_display.custom_minimum_size = Vector2(layout.card_width, layout.card_height)
    
    # 调整字体大小
    if title_label:
        title_label.add_theme_font_size_override("font_size", layout.font_size * 1.2)
    
    if description_label:
        description_label.add_theme_font_size_override("font_size", layout.font_size)
    
    # 调整返回按钮大小和字体
    if return_button:
        return_button.custom_minimum_size = Vector2(layout.panel_width * 0.3, layout.font_size * 2.5)
        return_button.add_theme_font_size_override("font_size", layout.font_size)
    
    # 调整BUFF列表的字体大小
    if buff_list:
        for child in buff_list.get_children():
            if child is RichTextLabel:
                child.add_theme_font_size_override("font_size", layout.font_size * 0.9)

# 显示卡牌详情面板
func show_card_detail(card_data):
    # 保存当前卡牌数据
    current_card_data = card_data
    
    # 设置卡牌纹理
    if card_data and "path" in card_data:
        var texture_path = card_data.path
        if ResourceLoader.exists(texture_path):
            card_display.texture = load(texture_path)
    
    # 设置卡牌标题
    if card_data and "name" in card_data:
        title_label.text = card_data.name
        
    # 设置卡牌描述
    if card_data and "description" in card_data:
        description_label.text = card_data.description
    
    # 重置所有容器的大小
    buff_list.custom_minimum_size = Vector2.ZERO
    buff_list.size = Vector2.ZERO
    description_container.custom_minimum_size = Vector2.ZERO
    description_container.size = Vector2.ZERO
    
    # 设置卡牌BUFF
    set_card_description(card_data)
    
    # 更新shader的BUFF信息
    update_buff_material(card_data)
    
    # 确保面板可见并重新计算位置
    visible = true
    #_apply_adaptive_layout()
    
    # 重置3D效果参数
    if unified_material:
        unified_material.set_shader_parameter("enable_3d_effect", true)
        unified_material.set_shader_parameter("mouse_position", Vector2(0, 0))
    
    # 重置3D效果参数
    target_rotation = Vector3.ZERO
    current_rotation = Vector3.ZERO
    card_center = card_display.global_position + card_display.size / 2
    
    # 显示动画
    _play_show_animation()

# 播放显示动画
func _play_show_animation():
    # 重置面板状态
    modulate.a = 0
    scale = Vector2(0.9, 0.9)
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)
    
    # 淡入并缩放
    tween.tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(1.0, 1.0), 0.5)
    
    # 背景模糊动画
    background_overlay.modulate.a = 0
    tween.parallel().tween_property(background_overlay, "modulate:a", 1.0, 0.3)
    
    # 卡牌出现动画
    card_display.scale = Vector2(0.5, 0.5)
    card_display.modulate.a = 0
    tween.parallel().tween_property(card_display, "scale", Vector2(1.0, 1.0), 0.5).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_ELASTIC)
    tween.parallel().tween_property(card_display, "modulate:a", 1.0, 0.3)
    
    # 描述区域出现动画
    if description_container.visible:
        description_container.modulate.a = 0
        #description_container.position.x = 50
        tween.parallel().tween_property(description_container, "modulate:a", 1.0, 0.5)
        #tween.parallel().tween_property(description_container, "position:x", 0, 0.5)
    
    # 按钮出现动画
    return_button.modulate.a = 0
    return_button.scale = Vector2(0.5, 0.5)
    var button_tween = create_tween()
    button_tween.set_ease(Tween.EASE_OUT)
    button_tween.set_trans(Tween.TRANS_ELASTIC)
    button_tween.tween_property(return_button, "modulate:a", 1.0, 0.5).set_delay(0.2)
    button_tween.parallel().tween_property(return_button, "scale", Vector2(1.0, 1.0), 0.5).set_delay(0.2)

# 隐藏卡牌详情面板
func hide_card_detail():
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_CUBIC)
    
    # 淡出并缩放
    tween.tween_property(self, "modulate:a", 0.0, 0.3)
    tween.parallel().tween_property(self, "scale", Vector2(0.9, 0.9), 0.3)
    
    # 背景模糊动画
    tween.parallel().tween_property(background_overlay, "modulate:a", 0.0, 0.2)
    
    # 等待动画完成后隐藏面板
    await tween.finished
    
    visible = false
    
    # 发出关闭信号
    emit_signal("detail_panel_closed")

# 处理每帧更新
func _process(delta):
    if visible:
        # 更新卡牌特效
        _update_card_effect(delta)

        if is_mouse_inside:
            # 3D效果不再需要，使用统一着色器代替
            pass

# 更新卡牌效果
func _update_card_effect(delta):
    # 更新unified材质的参数
    if unified_material and card_display and card_display.visible:
        # 应用布局后的正确卡牌尺寸
        var layout = _calculate_adaptive_layout()
        unified_material.set_shader_parameter("card_width", layout.card_width)
        unified_material.set_shader_parameter("card_height", layout.card_height)
        
        # 如果需要根据当前卡牌数据更新特效可以在这里添加逻辑
        
        # 如果有BUFF，可以更新BUFF颜色
        if current_card_data and "buff" in current_card_data:
            var buff_data = current_card_data.buff
            
            # 获取BUFF管理器
            var buff_manager = get_node("/root/BuffManager")
            if buff_manager:
                # 获取BUFF颜色并设置到shader
                var buff_color = buff_manager.get_buff_color(buff_data.color)
                # 设置适当的透明度
                buff_color.a = 0.3
                unified_material.set_shader_parameter("buff_color", buff_color)
                unified_material.set_shader_parameter("glitter_color", buff_color)
                

# 鼠标进入卡牌容器
func _on_card_container_mouse_entered():
    is_mouse_inside = true
    
    # 启用悬停效果
    if unified_material:
        unified_material.set_shader_parameter("enable_hover_effect", false)
        unified_material.set_shader_parameter("is_hovered", false)
        # 确保3D效果开启
        unified_material.set_shader_parameter("enable_3d_effect", true)
    
    # 添加进入动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    
    # 使用着色器中的缩放参数渐变
    if unified_material:
        tween.tween_method(func(value): unified_material.set_shader_parameter("current_scale", value), 
                           1.0, 1.1, 0.3)

# 鼠标离开卡牌容器
func _on_card_container_mouse_exited():
    is_mouse_inside = false
    
    # 禁用悬停效果
    if unified_material:
        unified_material.set_shader_parameter("is_hovered", false)
        # 重置鼠标位置到中心
        unified_material.set_shader_parameter("mouse_position", Vector2(0, 0))
        
    # 动画恢复原始大小
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_method(func(value): unified_material.set_shader_parameter("current_scale", value), 
                        1.1, 1.0, 0.3)
    
    # 完成后关闭悬停效果，但保持3D效果
    tween.tween_callback(func(): 
        if unified_material:
            unified_material.set_shader_parameter("enable_hover_effect", false)
            unified_material.set_shader_parameter("enable_3d_effect", true))

# 处理卡牌容器输入
func _on_card_container_input(event):
    # 更新鼠标位置
    if event is InputEventMouseMotion and is_mouse_inside:
        mouse_position = event.global_position
        
        # 计算相对于卡牌中心的偏移
        var card_rect = card_display.get_global_rect()
        var relative_x = mouse_position.x - card_rect.position.x
        var relative_y = mouse_position.y - card_rect.position.y
        
        # 相对于卡牌中心的坐标，从左上角转为相对中心点
        var center_x = relative_x - (card_rect.size.x / 2)
        var center_y = relative_y - (card_rect.size.y / 2)
        
        # 将相对中心的坐标映射到shader所需的范围
        if unified_material:
            unified_material.set_shader_parameter("mouse_position", Vector2(center_x, center_y))
    # 不在这里处理点击事件，让它传递到_input函数

# 重写输入事件处理
func _input(event):
    if visible and event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
        hide_card_detail()
        get_viewport().set_input_as_handled()
    # 保持ESC关闭功能
    elif visible and event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
        hide_card_detail()
        get_viewport().set_input_as_handled()

# 返回按钮点击处理
func _on_return_button_pressed():
    hide_card_detail()

func setup_card(data: Dictionary):
    card_data = data
    
    # 设置卡牌基本信息
    if data.has("texture"):
        card_display.texture = data.texture
    
    if data.has("name"):
        title_label.text = data.name
    
    if data.has("description"):
        description_label.text = data.description
    
    # 暂时注释掉，等buff_item.tscn创建后再取消注释
    # # 清空现有的BUFF列表
    # for child in buff_list.get_children():
    #     child.queue_free()
    # 
    # # 添加BUFF效果
    # if data.has("buffs") and data.buffs is Array:
    #     for buff_data in data.buffs:
    #         add_buff_item(buff_data)

# 暂时注释掉，等buff_item.tscn创建后再取消注释
# func add_buff_item(buff_data: Dictionary):
#     var buff_item = buff_scene.instantiate()
#     buff_list.add_child(buff_item)
#     
#     if buff_data.has("icon"):
#         buff_item.set_icon(buff_data.icon)
#     
#     if buff_data.has("name"):
#         buff_item.set_title(buff_data.name)
#     
#     if buff_data.has("description"):
#         buff_item.set_description(buff_data.description)

# 更新shader的BUFF信息
func update_buff_material(card_data):
    if unified_material and card_data and "buff" in card_data:
        var buff_data = card_data.buff
        
        # 获取BUFF管理器
        var buff_manager = get_node("/root/BuffManager")
        if buff_manager:
            # 获取BUFF颜色
            var buff_color = buff_manager.get_buff_color(buff_data.color)
            buff_color.a = 0.3  # 设置适当的透明度
            
            # 更新shader的buff颜色
            unified_material.set_shader_parameter("buff_color", buff_color)
            unified_material.set_shader_parameter("glitter_color", buff_color)
            
            # 获取并设置BUFF纹理
            var buff_texture_path = buff_manager.get_buff_texture_path(buff_data.type)
            if ResourceLoader.exists(buff_texture_path):
                var buff_tex = load(buff_texture_path)
                var color = BuffManager.get_buff_visual_color(buff_data.color)

                if buff_tex:
                    unified_material.set_shader_parameter("buff_texture", buff_tex)
                    unified_material.set_shader_parameter("buff_color", color)
                    unified_material.set_shader_parameter("buff_tile_scale", 1.0) # 设置平铺密度
                    unified_material.set_shader_parameter("buff_noise_influence", 0.1)
                    unified_material.set_shader_parameter("enable_buff_overlay", true) # 启用效果
            
           
    else:
        # 如果没有BUFF，使用默认设置
        if unified_material:
            unified_material.set_shader_parameter("enable_buff_overlay", false) # 启用效果
            unified_material.set_shader_parameter("buff_color", Color(1.0, 1.0, 1.0, 0.3))

# 处理窗口大小改变事件
#func _notification(what):
    #if what == NOTIFICATION_WM_SIZE_CHANGED:
        # 窗口大小改变时重新应用自适应布局
        #_apply_adaptive_layout()

# 递归设置所有子节点的鼠标过滤模式
func _set_mouse_filter_recursive(node: Node) -> void:
    if node is Control:
        # 所有控件都设置为忽略鼠标事件，让事件传递到根节点
        # 但跳过卡牌容器和卡牌显示节点，它们需要处理鼠标移动
        if node != card_container and node != card_display:
            node.mouse_filter = Control.MOUSE_FILTER_IGNORE
    
    # 递归处理所有子节点
    for child in node.get_children():
        _set_mouse_filter_recursive(child)

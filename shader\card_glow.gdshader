shader_type canvas_item;

uniform float _tilt_Scale = 0.2;
uniform bool _isSpecularLight = false;
uniform float _speularLightIntensity = 0.3;
uniform float _speularLightPower = 5.0;
uniform vec2 _mousePos;

varying vec2 vTexCoord;
varying float vFragPerspective;
varying vec2 vMouseoffset;

void vertex() {
    vec2 originalVertex = VERTEX.xy ;

    vTexCoord = VERTEX.xy * TEXTURE_PIXEL_SIZE;

    vec2 centeredCoord = vTexCoord - vec2(0.5, 0.5);

    vec2 mouse_centered = _mousePos * 0.8;

    vMouseoffset = mouse_centered / 2.0;

    float cosX = cos(mouse_centered.y * _tilt_Scale);
    float sinX = sin(mouse_centered.y * _tilt_Scale);
    mat3 rotationX;
    rotationX[0] = vec3(1.0, 0.0, 0.0);
    rotationX[1] = vec3(0.0, cosX, -sinX);
    rotationX[2] = vec3(0.0, sinX, cosX);

    float cosY = cos(-mouse_centered.x * _tilt_Scale);
    float sinY = sin(-mouse_centered.x * _tilt_Scale);
    mat3 rotationY;
    rotationY[0] = vec3(cosY, 0.0, sinY);
    rotationY[1] = vec3(0.0, 1.0, 0.0);
    rotationY[2] = vec3(-sinY, 0.0, cosY);

    mat3 rotationZ = mat3(
        vec3(1.0, 0.0, 0.0),
        vec3(0.0, 1.0, 0.0),
        vec3(0.0, 0.0, 1.0)
    );

    mat3 rotation = rotationZ * rotationY * rotationX;

    vec3 transformedCoord = rotation * vec3(centeredCoord, 0.0);

    float perspective = 1.0 / (1.0 - transformedCoord.z * 0.2);
    transformedCoord.xy *= perspective;
    vFragPerspective = perspective;

    vec2 screenOffset = transformedCoord.xy;

    VERTEX.xy = originalVertex + screenOffset * TEXTURE_PIXEL_SIZE.xy * 1024.0;
}

void fragment() {
    vec2 perspectiveCorrectedUV = vTexCoord / vFragPerspective;

    vec2 finalTexCoord = clamp(perspectiveCorrectedUV, vec2(0.0), vec2(1.0));

    vec4 texColor = texture(TEXTURE, UV);

    float specularvalue = pow(clamp(1.0 - length(UV - 0.5 + vMouseoffset * 0.5), 0.0, 1.0), _speularLightPower) * _speularLightIntensity;
    vec3 specularCol = vec3(specularvalue);

    if(_isSpecularLight)
    	COLOR = texColor + vec4(specularCol, 0.0);
    else
        COLOR = texColor;
}
shader_type canvas_item;

// 金色闪动效果参数
uniform float flash_speed : hint_range(0.1, 10.0) = 3.5;  // 闪烁速度
uniform float min_intensity : hint_range(0.0, 1.0) = 0.4;  // 最小闪烁强度
uniform float max_intensity : hint_range(0.5, 2.0) = 1.2;  // 最大闪烁强度
uniform bool is_active = false;                          // 是否激活效果

// 金色参数
uniform vec4 gold_color : source_color = vec4(1.0, 0.84, 0.0, 0.8);  // 金色 - 增加不透明度
uniform float edge_width : hint_range(0.0, 0.5) = 0.2;  // 边缘宽度 - 增加宽度

void fragment() {
    // 获取原始颜色
    vec4 original_color = texture(TEXTURE, UV);
    
    if (is_active) {
        // 计算闪烁强度
        float intensity = mix(min_intensity, max_intensity, (sin(TIME * flash_speed) + 1.0) / 2.0);
        
        // 计算到边缘的距离
        vec2 center_dist = abs(UV - vec2(0.5, 0.5)) * 2.0;
        float dist = max(center_dist.x, center_dist.y);
        
        // 创建边缘效果 - 只在卡片边缘显示金色框
        float edge_effect = smoothstep(1.0 - edge_width, 1.0, dist) * intensity * 2.0;
        
        // 创建整体轻微闪光效果 - 减小整体效果，突出边缘
        float overall_effect = intensity * 0.05;
        
        // 混合金色效果 - 优先应用边缘效果，使边缘更加明亮
        vec3 gold_effect = mix(original_color.rgb, gold_color.rgb * 1.2, edge_effect);
        gold_effect = mix(gold_effect, original_color.rgb, overall_effect);
        
        // 输出最终颜色
        COLOR = vec4(gold_effect, original_color.a);
    } else {
        // 不激活时保持原始颜色
        COLOR = original_color;
    }
}
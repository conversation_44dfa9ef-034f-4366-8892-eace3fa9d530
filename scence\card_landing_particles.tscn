[gd_scene load_steps=7 format=3 uid="uid://d343o6tam4ktp"]

[ext_resource type="Script" uid="uid://7743dw8m3wfw" path="res://script/card_landing_particles.gd" id="1_landing"]

[sub_resource type="Gradient" id="Gradient_lfm2e"]
colors = PackedColorArray(1, 0.9, 0.5, 1, 0.8, 0.4, 0.1, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_yjkxs"]
gradient = SubResource("Gradient_lfm2e")

[sub_resource type="Curve" id="Curve_j8k2p"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_ux7vp"]
curve = SubResource("Curve_j8k2p")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_k3r8d"]
particle_flag_disable_z = true
emission_shape_offset = Vector3(60, 100, 0)
emission_shape_scale = Vector3(1, 1, 0)
emission_shape = 6
emission_ring_axis = Vector3(0, 0, 0)
emission_ring_height = 0.0
emission_ring_radius = 60.0
emission_ring_inner_radius = 80.0
emission_ring_cone_angle = 45.0
angle_min = 1.07288e-05
angle_max = 1.07288e-05
direction = Vector3(0, 0, 0)
spread = 180.0
initial_velocity_max = 180.0
gravity = Vector3(0, 0, 0)
scale_min = 5.0
scale_max = 5.0
scale_curve = SubResource("CurveTexture_ux7vp")
color_ramp = SubResource("GradientTexture1D_yjkxs")
turbulence_enabled = true
turbulence_noise_strength = 5.0
turbulence_noise_scale = 2.0

[node name="CardLandingParticles" type="GPUParticles2D"]
z_index = 1
emitting = false
amount = 60
lifetime = 1.2
one_shot = true
explosiveness = 0.8
randomness = 0.4
process_material = SubResource("ParticleProcessMaterial_k3r8d")
script = ExtResource("1_landing")

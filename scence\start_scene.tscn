[gd_scene load_steps=6 format=3 uid="uid://cimgv8aklwaks"]

[ext_resource type="Script" uid="uid://b24cxt1ppfago" path="res://script/start_scene.gd" id="1_8o0v2"]
[ext_resource type="Texture2D" uid="uid://c8v0oifkf6wid" path="res://assert/start/start.png" id="1_u0te5"]

[sub_resource type="Animation" id="Animation_k86gh"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0, 0, 0, 1)]
}

[sub_resource type="Animation" id="Animation_8o0v2"]
resource_name = "start"
length = 3.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:color")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(0, 0, 0, 1), Color(0, 0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_xuv7h"]
_data = {
&"RESET": SubResource("Animation_k86gh"),
&"start": SubResource("Animation_8o0v2")
}

[node name="StartScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_8o0v2")

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(576, 324)
texture = ExtResource("1_u0te5")
expand_mode = 1
stretch_mode = 5

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 1)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_xuv7h")
}
autoplay = "start"

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_animation_player_animation_finished"]

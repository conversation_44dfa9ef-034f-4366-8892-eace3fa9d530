[gd_scene load_steps=5 format=3 uid="uid://bgfibehq85ssx"]

[ext_resource type="Script" uid="uid://bgbtvoqkdmwsc" path="res://script/deck_button_particles.gd" id="1_particles"]

[sub_resource type="Curve" id="Curve_lfj2p"]
_data = [Vector2(0, 0), 0.0, 2.0, 0, 0, Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_ixvnq"]
curve = SubResource("Curve_lfj2p")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_vu8yd"]
particle_flag_disable_z = true
direction = Vector3(-1, 0, 0)
spread = 30.0
initial_velocity_min = 100.0
initial_velocity_max = 200.0
angular_velocity_min = -100.0
angular_velocity_max = 100.0
gravity = Vector3(0, 0, 0)
scale_min = 0.3
scale_max = 0.6
scale_curve = SubResource("CurveTexture_ixvnq")
color = Color(0, 0.8, 0.4, 1)

[node name="DeckButtonParticles" type="GPUParticles2D"]
amount = 12
lifetime = 0.8
explosiveness = 0.9
process_material = SubResource("ParticleProcessMaterial_vu8yd")
script = ExtResource("1_particles")

[node name="Timer" type="Timer" parent="."]
wait_time = 0.8
one_shot = true

Stack trace:
Frame         Function      Args
0007FFFF3600  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF2500) msys-2.0.dll+0x2118E
0007FFFF3600  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF3600  0002100469F2 (00021028DF99, 0007FFFF34B8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF3600  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF3600  00021006A545 (0007FFFF3610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF3610, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9A2BC0000 ntdll.dll
7FF9A12D0000 KERNEL32.DLL
7FF9A0220000 KERNELBASE.dll
7FF9A2440000 USER32.dll
7FF9A0890000 win32u.dll
7FF9A2410000 GDI32.dll
7FF9A0940000 gdi32full.dll
7FF9A05A0000 msvcp_win.dll
7FF9A0640000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9A0B30000 advapi32.dll
7FF9A2050000 msvcrt.dll
7FF9A1230000 sechost.dll
7FF9A22F0000 RPCRT4.dll
7FF99F8C0000 CRYPTBASE.DLL
7FF9A08C0000 bcryptPrimitives.dll
7FF9A0AF0000 IMM32.DLL
